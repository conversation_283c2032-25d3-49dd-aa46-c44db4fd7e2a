# CLI 模块设计文档

## 概述

CLI 模块为 OneDragon-Agent 系统提供基于终端的用户界面。它使用 Textual 框架创建响应式、视觉美观的终端应用程序，并采用事件驱动架构实现实时消息处理和动态显示更新。

## 架构

CLI 模块组织如下：

```
src/one_dragon_agent/cli/
├── __init__.py
├── __main__.py
├── app.py
├── display.py
├── display_manager.py
└── event_handler.py
```

### 组件

1. **app.py** - 包含主 Textual 应用类 `OneDragonAgentCLI`
2. **__main__.py** - 入口点，调用 app.py 中的 main 函数
3. **__init__.py** - 暴露 main 函数以供程序化访问
4. **display.py** - 定义显示数据模型和显示项类型
5. **display_manager.py** - 实现显示管理器，负责所有显示逻辑和动态更新
6. **event_handler.py** - 实现各种事件处理器，处理来自系统的事件

## 实现细节

### OneDragonAgentCLI 类

主应用类继承自 `textual.app.App`，提供以下功能：

1. **UI 组件**：
   - Header：显示应用程序标题
   - Welcome Container：显示欢迎信息
   - Chat Container：显示用户和AI之间的消息历史
   - Input Field：允许用户输入命令
   - Footer：显示按键绑定和状态信息

2. **主要特性**：
   - 使用 Textual 框架的简洁、响应式 UI
   - Ctrl+C 优雅退出处理（需要按两次）
   - 输入字段的焦点管理
   - 自定义 CSS 样式区分用户消息和AI消息
   - 实时显示用户输入和AI响应
   - 与 OdaSession 的集成
   - 事件驱动的消息处理架构
   - 动态显示更新功能

3. **方法**：
   - `compose()`：创建 UI 布局
   - `on_mount()`：挂载时初始化应用、显示管理器和 OdaSession
   - `on_key()`：处理键盘事件
   - `on_input_submitted()`：处理输入提交事件
   - `_is_scrolled_to_bottom()`：检查是否滚动到最底部
   - `_process_message()`：处理单个消息（向后兼容）
   - `_send_message_async()`：异步发送消息，避免阻塞UI
   - `on_unmount()`：应用卸载时清理资源
   - `main()`：入口点函数

### 显示数据模型 (display.py)

定义专门用于 CLI 显示的数据模型：

```python
class DisplayItemType(StrEnum):
    """显示项类型枚举"""
    USER_MESSAGE = "user_message"
    AI_MESSAGE = "ai_message"
    AI_STREAMING = "ai_streaming"
    TOOL_START = "tool_start"
    TOOL_RESULT = "tool_result"
    SYSTEM_MESSAGE = "system_message"
    TODO_LIST = "todo_list"
    PERMISSION_REQUEST = "permission_request"
    PROGRESS_INDICATOR = "progress_indicator"

@dataclass
class DisplayItem:
    """CLI 显示项数据模型"""
    item_type: DisplayItemType
    content: str
    item_id: str  # 用于更新特定项的唯一标识符
    status: Optional[str] = None  # 如 "running", "completed", "failed"
    metadata: Optional[dict] = None  # 额外的元数据
```

### 显示管理器 (display_manager.py)

`DisplayManager` 类负责管理所有显示逻辑，包括动态更新功能：

1. **核心功能**：
   - 管理显示项的生命周期（添加、更新、删除）
   - 支持动态更新已显示的内容
   - 自动滚动到底部功能
   - 根据显示类型和状态应用不同的样式

2. **主要方法**：
   - `add_item()`：添加新的显示项
   - `update_item()`：更新现有显示项的内容和状态
   - `remove_item()`：移除显示项
   - `get_item()`：获取指定ID的显示项
   - `_create_widget()`：根据类型创建对应的UI组件
   - `_get_widget_classes()`：获取CSS类名
   - `_update_widget_style()`：根据状态更新组件样式

### 事件处理器 (event_handler.py)

实现各种事件处理器，处理来自系统的事件：

1. **CLIAgentEventHandler**：
   - 处理 AI 文本流事件（开始、内容、完成）
   - 支持流式显示 AI 响应
   - 使用 `stream_id` 作为唯一标识符

2. **CLIToolEventHandler**：
   - 处理工具执行事件（开始、完成）
   - 支持动态更新工具执行状态
   - 使用 `tool_call_id` 作为唯一标识符
   - 特殊处理 `TodoWrite` 工具（不显示开始和完成消息）

3. **CLITodoEventHandler**：
   - 处理待办事项更新事件
   - 显示待办事项列表或错误信息

4. **CLIPermissionEventHandler**：
   - 处理权限请求事件
   - 显示权限请求提示并等待用户响应
   - 支持多种响应选项（yes/no/always）
   - 包含超时处理机制，防止在测试中挂起

### OdaSession 集成

CLI 模块现在已经与 OdaSession 集成，实现了以下功能：

1. **系统初始化**：
   - 在 `on_mount()` 方法中创建 OdaSession 实例
   - 初始化显示管理器 (`DisplayManager`)
   - 注册所有事件处理器到事件分发器
   - 传递必要的配置参数

2. **事件驱动架构**：
   - 使用事件分发器实现模块间的解耦通信
   - 支持多种事件类型：AI文本流、工具执行、待办事项更新、权限请求
   - 事件处理器直接处理事件数据，无需包装成 `OdaMessage`

3. **消息处理流程**：
   - 用户在输入框中输入文本并按回车
   - `on_input_submitted()` 被触发，显示用户消息并异步发送到 OdaSession
   - OdaSession 处理消息并通过事件分发器发布相应事件
   - 对应的事件处理器接收事件并通过显示管理器更新UI
   - 支持动态更新已显示的内容（如工具执行状态、AI流式响应）

4. **实时消息显示**：
   - 用户消息立即显示在聊天区域
   - AI 响应以流式方式逐步显示在聊天区域
   - 工具执行状态从"运行中"动态更新为"完成"或"失败"
   - 使用不同的背景色和左边框颜色区分不同类型的消息

5. **动态更新机制**：
   - 使用唯一标识符（如 `stream_id`、`tool_call_id`）跟踪显示项
   - 支持更新现有显示项的内容和状态
   - 自动滚动到底部功能，确保用户始终看到最新内容
   - 优化渲染性能，减少不必要的UI更新

## CSS 样式

应用程序使用内联 CSS 来设置界面样式：

- 全屏尺寸的垂直布局
- 带居中文本和边框的欢迎容器
- 可滚动的聊天消息区域
- 带填充和边距的输入容器
- 全宽输入字段
- 不同类型消息的样式区分：
  - 用户消息：深灰色背景，蓝色左边框
  - AI消息：中灰色背景，绿色左边框
  - 工具结果：深蓝紫色背景，圆角边框
  - 系统消息：深红色背景，红色左边框
  - 欢迎消息：浅灰色文本，居中对齐

## 重构改进

CLI 模块已完成重大重构，主要改进包括：

### 1. 事件驱动架构
- **改进的事件模型**：每个事件类直接定义自己的字段，移除了冗余的 `data` 字段包装
- **解耦的事件处理**：事件处理器直接处理事件数据，无需转换为 `OdaMessage` 对象
- **精确的事件关联**：使用 `tool_call_id`、`stream_id` 等作为唯一标识符，确保显示更新的准确性

### 2. 动态显示更新
- **显示管理器**：集中管理所有显示逻辑，支持动态更新已显示的内容
- **状态管理**：支持显示项的状态变化（如 "running" → "completed"）
- **性能优化**：减少不必要的UI更新，优化渲染性能

### 3. 代码简化
- **消除冗余转换**：移除事件处理器中不必要的 `OdaMessage` 包装
- **清晰的职责分离**：显示逻辑、事件处理、UI管理各自独立
- **提高可维护性**：更清晰的架构便于新功能开发和问题排查

### 4. 功能增强
- **流式显示**：支持AI响应的流式显示，提供更好的用户体验
- **工具状态更新**：实时显示工具执行状态，从开始到完成的完整过程
- **权限请求处理**：改进的权限请求UI，支持超时处理和错误恢复
- **滚动优化**：智能滚动到底部功能，只在用户查看底部时才自动滚动

### 5. 扩展性提升
- **模块化设计**：显示管理器和事件处理器的分离便于扩展新功能
- **类型安全**：使用枚举和类型提示提高代码可靠性
- **测试友好**：各组件职责明确，便于单元测试和集成测试

## 使用方法

运行 CLI：

```bash
# 直接执行
python -m src.one_dragon_agent.cli

# 或者安装后运行
one-dragon-agent
```

## 未来增强功能

CLI 模块的计划改进：

1. ~~与核心代理系统集成~~（已完成）
2. ~~实时输出显示~~（已完成）
3. ~~事件驱动架构重构~~（已完成）
4. ~~动态显示更新~~（已完成）
5. 命令解析和执行
6. 交互式帮助系统
7. 配置管理
8. 会话历史和管理
9. 主题和样式自定义
10. 多语言支持
11. 键盘快捷键增强
12. 消息搜索和过滤