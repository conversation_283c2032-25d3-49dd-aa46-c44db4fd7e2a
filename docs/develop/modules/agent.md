# Agent 模块设计文档

## 1. 概述

`Agent` 模块是负责处理用户单轮对话或单个命令的核心组件。它封装了与大语言模型（LLM）交互、协调工具执行以及生成最终响应的逻辑。

该模块的设计受到了对 Claude 源码分析中发现的 `nO` 函数的启发，该函数代表了 Agent 的主执行循环。

## 2. 架构

此模块中的主要类是 `Agent`。

```
src/one_dragon_agent/core/agent/
└── agent.py       # 包含 Agent 类
```

### Agent 类

系统会为每个需要处理的命令（`OdaModelMessage`）创建一个 `Agent` 实例。它是一个生命周期较短的对象，用于持有单次执行周期的状态。

#### 构造函数

Agent 构造函数接收以下参数：
- 会话配置：包含 LLM 配置信息
- 消息管理器：会话中的消息历史管理器
- 用户命令：要处理的用户命令
- LLM 工厂：用于创建 LLM 客户端的工厂
- 工具管理器：负责工具的协调执行
- 事件分发器：用于发送系统事件
- 提醒管理器：可选的提醒管理器

#### 主要方法

- **execute**: Agent 的主要入口点，协调生成响应的整个过程
- **_process_tool_results**: 处理工具执行结果，负责组装消息
- **_assemble_tool_message**: 将单个工具执行结果组装成模型消息

## 3. 执行流程 (mainAgentLoop)

Agent 的执行遵循一个循环流程，直到任务完成（即不再需要调用工具）：

1.  **LLM 客户端创建**: 使用提供的工厂创建 LLM 客户端
2.  **添加用户命令**: 将当前的用户命令添加到消息管理器
3.  **进入主循环**:
    a. **调用LLM**:
        - 从消息管理器获取完整的消息历史
        - 调用 LLM 客户端的流式聊天接口，并传入工具定义
        - 实时处理LLM返回的流式数据，区分文本内容和工具调用请求
        - 文本内容通过事件分发器发送事件进行实时显示
        - 将完整的LLM响应（文本或工具调用）聚合并返回
    b. **工具调用处理**:
        - 如果LLM的响应包含工具调用请求，则调用工具管理器的执行方法
        - 接收工具结果列表作为返回结果
        - 调用工具结果处理方法：
            - 使用消息组装方法将每个工具结果组装成模型消息
            - 将组装好的消息添加到消息管理器
        - 循环返回步骤 3a，带着工具执行结果再次调用LLM
    c. **结束**: 如果LLM的响应不包含工具调用，则认为任务完成，循环结束
4.  **资源清理**: 确保 LLM 客户端被正确关闭

## 4. 与 `OdaSession` 的集成

`OdaSession` 充当协调器。对于其消息队列中的每个用户命令，它执行以下步骤：
1.  创建一个 `Agent` 实例。
2.  将会话配置、消息管理器、命令、工具管理器、事件分发器等传递给 `Agent`。
3.  调用 `await agent.execute()`。
4.  `Agent` 在其生命周期内，通过事件分发器发送所有实时事件，供 UI 组件消费和显示。

这种设计确保了 `OdaSession` 只负责会话和流管理，而 `Agent` 则包含了所有与 LLM 交互和工具协调的复杂逻辑。

## 5. 最新特性

1. **循环执行模型**: 实现了可以多次调用LLM和工具的循环执行模型
2. **工具协调**: 通过工具管理器统一协调工具执行，接收结构化的工具结果
3. **事件驱动架构**: 通过事件分发器实现系统内组件的解耦通信
4. **职责分离**: 清晰分离了工具执行、事件管理和消息组装的职责
5. **资源管理**: 自动管理 LLM 客户端的生命周期，确保正确关闭
6. **结构化数据流**: 使用专门的工具结果类实现类型安全的数据传递