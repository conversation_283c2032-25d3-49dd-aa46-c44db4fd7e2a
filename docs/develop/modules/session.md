# OdaSession 模块设计文档

## 1. 概述

`OdaSession` 模块是用户交互会话的中央神经系统。它负责管理通信的整个生命周期，从接收原始用户输入到分派处理，再到将最终输出流式传输回用户界面。

## 2. 架构

`OdaSession` 类是该模块的核心。它的生命周期贯穿整个用户会话，并维护会话的状态，例如对话历史。

```
src/one_dragon_agent/core/sys/
└── session.py       # 包含 OdaSession 类
```

### 核心职责

-   **队列管理 (Queue Management)**: 它管理两个核心队列：`_message_queue` 用于接收来自CLI的原始 `OdaMessage` 并直接用于处理，`_display_message_queue` 用于向CLI发送响应。
-   **Agent 编排 (Agent Orchestration)**: `_process_messages` 任务持续处理 `_message_queue`。对于每个消息，它会实例化一个 `Agent` 对象，从而有效地委托了"思考"和工具使用等复杂任务。
-   **LLM 客户端管理**: 管理 `ModelClientFactory`，用于创建 `Agent` 所需的LLM客户端。
-   **消息历史管理**: 持有 `MessageManager` 实例，维护对话历史。
-   **文件引用处理**: 解析用户消息中的 `<oda-at-file>path/to/file</oda-at-file>` 标签，读取指定文件的内容，并将其作为系统消息注入到对话历史中。

## 3. 数据流

`OdaSession` 内部的数据流被设计为异步和非阻塞的，以实现实时交互。

```mermaid
flowchart TD
    subgraph "用户界面 CLI"
        A[用户输入] -- "send_input(OdaMessage)" --> B{message_queue};
    end

    subgraph "OdaSession"
        B -- "_process_messages" --> C[_process_file_references];
        C -- "读取文件并注入系统消息" --> D[Agent 实例];
        D -- "send_display_message" --> E{display_message_queue};
        E -- "get_display_message_stream" --> F[CLI];
    end

    subgraph "Agent"
        D -- "agent.execute()" --> G[Agent 逻辑 / LLM / 工具];
    end

    style OdaSession fill:#f9f,stroke:#333,stroke-width:2px
```

1.  **输入 (Input)**: CLI调用 `send_input` 将一个 `OdaMessage` 放入 `_message_queue`。
2.  **文件引用处理 (File Reference Processing)**: `_process_messages` 任务在处理消息之前，会先调用 `_process_file_references` 方法。该方法会解析消息中的 `<oda-at-file>path/to/file</oda-at-file>` 标签，读取指定文件的内容，并将其作为系统消息注入到对话历史中。
3.  **消息处理 (Message Processing)**: `_process_messages` 任务在后台运行，异步地从 `_message_queue` 中拉取消息。
4.  **委托 (Delegation)**: 为每个消息创建一个新的 `Agent`。这将每轮的执行逻辑隔离开来。
5.  **执行 (Execution)**: 调用 `agent.execute()`。`Agent` 执行其任务，并将所有结果（中间和最终）通过 `send_display_message` 回调放入 `_display_message_queue`。
6.  **输出 (Output)**: CLI通过 `get_display_message_stream()` 方法从 `_display_message_queue` 异步读取数据，并实时渲染结果。

## 4. 类和方法详解

### OdaSession 类

核心会话管理类。

#### 主要方法

##### `send_input(message: OdaMessage)`
从CLI接收 `OdaMessage` 并将其放入 `_message_queue`。

##### `_process_messages()`
处理 `_message_queue` 中的消息。在处理每条消息之前，先调用 `_process_file_references` 处理文件引用。然后为每个消息创建并执行一个 `Agent`。

##### `_process_file_references(message: OdaMessage)`
解析用户消息中的 `<oda-at-file>path/to/file</oda-at-file>` 标签，读取指定文件的内容，并将其作为系统消息注入到对话历史中。同时，它会清理原始用户消息，移除这些标签。

##### `_resolve_file_path(file_reference: str) -> str`
将文件引用解析为完整、规范且适应操作系统的绝对路径。

##### `_send_file_read_display_message(file_read_results: list)`
向 CLI 发送一条系统消息，通知用户哪些文件被处理了以及处理结果（成功或失败）。它最多显示前 4 个文件，并在数量过多时显示省略信息。

##### `get_message_stream() -> AsyncIterator[OdaMessage]`
异步生成器，从消息队列中产生 `OdaMessage` 对象。

##### `send_display_message(message: OdaMessage) -> None`
将 `OdaMessage` 发送到显示消息队列。

##### `get_display_message_stream() -> AsyncIterator[OdaMessage]`
异步生成器，从显示消息队列中产生 `OdaMessage` 对象。

##### `close()`
优雅地关闭会话及其资源。

##### `abort(reason: Optional[str] = None) -> None`
通过取消后台任务来中止处理。

## 5. 最新特性

1. **简化的双队列系统**: 使用 `message_queue` 和 `display_message_queue` 将数据流清晰地分离，简化了消息处理流程。
2. **直接消息处理**: 移除了消息转换步骤，直接在 `_process_messages` 中处理 `OdaMessage`，提高了处理效率。
3. **LLM 客户端工厂**: 使用工厂模式创建 LLM 客户端，提供更好的可扩展性。
4. **实时消息处理**: 通过 `send_display_message` 回调实现消息的实时传输到 CLI。
5. **消息历史管理**: 自动维护用户消息和 AI 响应的历史记录。
6. **文件引用处理**: 支持通过 `<oda-at-file>path/to/file</oda-at-file>` 语法在用户消息中引用文件，自动读取文件内容并注入到对话历史中。
7. **文件读取状态反馈**: 在处理完文件引用后，向 CLI 发送系统消息，告知用户哪些文件被处理了以及处理结果。