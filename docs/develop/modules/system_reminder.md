# System Reminder 模块设计文档

## 1. 概述

System Reminder 模块是 OneDragon-Agent 系统中的核心基础设施组件，负责接收系统事件并生成相应的提醒消息。提醒消息会被注入到 LLM 消息队列中，用于向 Agent 提供上下文信息和状态更新。这些消息对用户透明，仅用于增强 Agent 的认知能力。

## 2. 设计目标

1. **事件处理**: 接收和处理系统事件
2. **消息生成**: 根据事件生成相应的提醒消息
3. **状态同步**: 实现系统状态与 Agent 认知状态的同步
4. **用户体验**: 保持系统复杂性对用户的透明，避免干扰用户交互

## 3. 核心概念

### 3.1 System Reminder
System Reminder 是注入到 LLM 消息队列中的特殊系统消息，用于向 Agent 提供上下文信息和状态更新。这些消息对用户透明，仅用于增强 Agent 的认知能力。

### 3.2 事件驱动架构
System Reminder 采用事件驱动架构，不同系统组件在状态变化时生成事件，ReminderManager 接收这些事件并生成相应的提醒内容。

## 4. 架构设计

### 4.1 模块结构

```
src/one_dragon_agent/core/system_reminder/
├── reminder.py          # 核心提醒管理器
└── __init__.py
```

### 4.2 核心组件

#### ReminderManager
负责接收事件并生成提醒消息的核心类。主要功能包括：
- 接收系统事件
- 根据事件类型生成相应的提醒消息
- 存储提醒消息以备下次 LLM 调用时注入
- 提供接口获取待处理的提醒消息

## 5. 工作流程

### 5.1 事件处理流程

1. **事件生成**: 系统组件（如工具执行、文件监控等）生成 Event
2. **事件处理**: ReminderManager 接收事件并根据事件类型生成相应的提醒内容
3. **消息创建**: 创建系统提醒消息
4. **存储管理**: 提醒消息被存储以备下次 LLM 调用时注入

### 5.2 Reminder 获取流程

1. **消息准备阶段**: 在 Agent 调用 LLM 之前，准备发送给 LLM 的消息队列
2. **Reminder 获取**: ReminderManager 提供待处理的提醒消息
3. **消息注入**: 将 System Reminder 消息注入到消息队列的最前面

## 6. 系统集成

### 6.1 与现有模块的集成关系

系统中的各个模块通过事件与 ReminderManager 进行交互：

```mermaid
graph TD
    A["Tool 模块"] -->|生成事件| B[Session 模块]
    C["其他系统组件"] -->|生成事件| B
    B -->|处理事件| D[ReminderManager]
    D -->|生成提醒消息| E[存储待处理消息]
    F[Agent 模块] -->|获取提醒消息| E
    E -->|注入消息队列| G[LLM 调用]
```

1. **Tool 模块**: 在执行完成后生成事件，通过 Session 模块传递给 ReminderManager
2. **Session 模块**: 负责接收系统中产生的事件，并转发给 ReminderManager 处理
3. **ReminderManager**: 接收事件并生成相应的提醒消息，存储以备后续使用
4. **Agent 模块**: 在调用 LLM 之前，从 ReminderManager 获取待处理的提醒消息
5. **LLM 调用**: 将提醒消息注入到消息队列的最前面，然后调用 LLM

在系统实现中，各模块通过以下方式与 ReminderManager 集成：

1. **Agent 模块**: 在调用 LLM 之前，会从 ReminderManager 获取待处理的提醒消息，并将其注入到消息队列的最前面
2. **Session 模块**: 负责创建和管理 ReminderManager 实例，并提供处理事件的接口
3. **Tool 模块**: 在执行完成后生成事件，通过 Session 模块传递给 ReminderManager 处理

### 6.2 支持的事件类型

#### Todo 事件
- **事件类型**: `todo.updated`
- **触发时机**: TodoWrite 工具执行完成后
- **提醒内容**: 更新的待办事项列表
- **处理逻辑**: 
  - TodoWrite 工具在执行完成后直接返回包含最新待办事项列表的系统提醒消息
  - 系统自动将提醒消息注入到下一次LLM调用中
  - 无需通过事件机制处理，由工具直接返回

## 7. 消息格式规范

### 7.1 Todo Reminder 消息格式

#### 非空待办列表
```xml
<system-reminder>
Your todo list has changed. DO NOT mention this explicitly to the user. Here are the latest contents of your todo list:

[{"content": "Task 1", "status": "pending", "priority": "high", "id": "1"}, ...]

You DO NOT need to use the TodoRead tool again, since this is the most up to date list for now. Continue on with the tasks at hand if applicable.
</system-reminder>
```

#### 空待办列表
```xml
<system-reminder>This is a reminder that your todo list is currently empty. DO NOT mention this to the user explicitly because they are already aware. If you are working on tasks that would benefit from a todo list please use the TodoWrite tool to create one. If not, please feel free to ignore. Again do not mention this message to the user.</system-reminder>
```

## 8. 安全和可靠性

### 8.1 安全机制
- Reminder 内容使用预定义模板，防止注入攻击
- 用户输入经过严格过滤和转义

### 8.2 可靠性保障
- 使用异步锁确保线程安全
- 提供清理资源的 close 方法

## 9. 扩展性设计

### 9.1 添加新的事件类型
可以通过扩展 `ReminderManager.handle_event` 方法来支持新的事件类型：

1. 检查事件类型前缀
2. 调用相应的事件处理方法
3. 生成提醒消息并存储

## 10. 测试策略

### 10.1 单元测试
- 测试 ReminderManager 的事件处理逻辑
- 测试不同事件类型生成的提醒消息
- 测试并发访问的安全性

## 11. 实现状态

当前已实现：
- ReminderManager 核心类
- 线程安全保障
- 与 Agent 和 Session 的集成
- TodoWrite 工具直接返回最新待办事项状态，无需通过事件机制处理

## 12. 总结

System Reminder 模块是 OneDragon-Agent 系统中的重要组件，负责接收系统事件并生成相应的提醒消息。通过简洁的设计，该模块实现了事件处理和提醒消息生成的核心功能，确保 Agent 能够实时感知系统状态变化，同时保持对用户的透明性。