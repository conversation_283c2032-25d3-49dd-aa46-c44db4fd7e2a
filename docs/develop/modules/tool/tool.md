# OdaTool 基类设计文档

## 1. 概述

`OdaTool` 是 OneDragon-Agent 系统中所有工具的抽象基类，定义了工具接口的标准规范。该基类提供了与大型语言模型（LLM）工具调用机制兼容的完整框架，确保了所有工具实现的一致性。

## 2. 设计目标

- **标准化接口**: 为所有工具提供统一的接口规范，确保系统的一致性
- **LLM 兼容性**: 与主流 LLM 的工具调用机制完全兼容
- **扩展性**: 支持各种复杂业务场景的工具实现
- **安全性**: 内置权限检查和并发控制机制
- **资源管理**: 提供完整的资源生命周期管理
- **事件驱动**: 支持工具自定义事件创建，实现实时状态监控

## 3. 核心职责

`OdaTool` 基类定义了以下核心职责：

1. **工具标识**: 提供工具名称和描述
2. **接口规范**: 定义标准的输入模式和执行接口
3. **安全控制**: 提供权限检查和并发安全声明
4. **结果处理**: 定义工具结果的格式化和转换
5. **事件创建**: 支持工具特定的自定义事件创建
6. **资源管理**: 提供完整的资源生命周期管理

## 4. 接口规范

### 4.1 基础属性

- **name**: 工具的唯一标识符
- **description**: 工具的人类可读描述
- **input_schema**: 工具的输入参数模式（在构造函数中提供）

### 4.2 抽象方法

- **call**: 执行工具的核心业务逻辑
- **is_read_only**: 指示工具是否为只读操作
- **is_concurrency_safe**: 指示工具是否可以安全地并发执行
- **create_start_event**: 创建工具开始执行事件
- **create_complete_event**: 创建工具执行结果事件

### 4.3 可选方法

- **dispose**: 清理工具使用的资源

**注意**: 具体的方法签名和参数定义以源文件为准。

## 5. ToolResult 数据结构

### 5.1 概述

`ToolResult` 是 OneDragon-Agent 系统中工具执行结果的核心数据结构。它封装了工具执行的所有相关信息，为系统提供类型安全、结构化的数据传递机制。

### 5.2 数据结构

#### ToolResult 类

`ToolResult` 是一个数据类，包含工具执行的完整信息：

- **status**: 工具执行状态（SUCCESS, ERROR, NOT_FOUND）
- **event_data**: 工具执行的原始结果数据，用于事件分发
- **model_message**: 格式化后供大模型使用的消息内容
- **error_message**: 错误信息（仅在执行失败时存在）

#### ToolExecutionStatus 枚举

定义工具执行的可能状态：
- **SUCCESS**: 工具执行成功
- **ERROR**: 工具执行过程中发生错误
- **NOT_FOUND**: 请求的工具未找到

### 5.3 属性方法

ToolResult 提供了便捷的属性方法来检查执行状态：
- **is_success**: 检查工具是否执行成功
- **is_error**: 检查工具是否执行失败
- **is_not_found**: 检查工具是否未找到
