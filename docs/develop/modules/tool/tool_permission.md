# 工具权限模块 (Tool Permission) 设计文档

## 1. 概述

本文档详细阐述了为 OneDragon-Agent 工具系统设计和集成一个权限校验模块的完整方案。随着 Agent能力的增强，特别是涉及到文件系统修改、网络访问和代码执行等高风险操作时，一个健robust的权限系统是保障用户安全的基石。

该模块的核心思想是：**任何工具在执行前，都必须声明其所需权限，并经过权限管理器（PermissionManager）的校验。** 对于敏感权限，系统将暂停当前任务，并向用户界面（CLI）请求授权。任务的执行流程将**阻塞**，直到用户明确授权或拒绝该操作。该设计确保了系统的操作始终在用户的明确控制下进行，实现了安全、透明和可控的工具执行环境。

## 2. 设计目标

-   **安全第一**: 默认拒绝所有未经授权的敏感操作，防止工具被滥用。
-   **用户控制**: 将最高决策权交给用户，用户可以授权、拒绝或为当前会话永久授权某项权限。
-   **阻塞式授权**: 提供同步阻塞式的授权体验，确保在获得用户决策前，任务执行会暂停，使用户界面流程清晰可控。
-   **职责分离**:
    -   **工具（OdaTool）**: 只负责**声明**自己需要什么权限。
    -   **工具管理器（ToolManager）**: 负责发起权限检查并**阻塞等待**结果。
    -   **权限管理器（PermissionManager）**: 只负责**管理和记录**权限授予状态。
    -   **用户界面（CLI）**: 只负责**呈现**权限请求并获取用户输入。
-   **低耦合集成**: 通过事件驱动机制连接后端逻辑和前端UI，以实现阻塞等待。

## 3. 核心组件

### 3.1. PermissionManager (新增)

一个全新的会话级服务，负责在单个会话中跟踪和管理用户授予的权限。

-   **文件路径**: `src/one_dragon_agent/core/agent/tool/permission/permission_manager.py`
-   **核心职责**:
    -   维护一个以 `session_id` 为键的、线程安全的权限授予记录。
    -   提供 `is_granted(session_id, permission)` 方法，用于检查特定权限是否已被授予。
    -   提供 `grant(session_id, permission)` 方法，用于记录用户已授予的权限。
    -   提供异步信号机制，用于在权限状态更新后通知等待的 `ToolManager`。
-   **设计模式**: 由 `OdaSession` 创建并持有，随 `ToolExecutionContext` 注入到工具执行流程中。

### 3.2. OdaTool (修改)

修改工具基类，要求所有工具明确声明其所需权限。

-   **新增抽象属性**:
    ```python
    @property
    @abstractmethod
    def required_permissions(self) -> set[str]:
        """
        Declares the set of permissions required by this tool.
        e.g., {"filesystem.write", "network.request"}
        An empty set means the tool requires no special permissions.
        """
        pass
    ```
-   **实现要求**: 所有具体的工具类（如 `ReadFileTool`, `EditTool`）都必须实现此属性，返回一个包含权限字符串的集合。

### 3.3. ToolManager (修改)

修改工具管理器，在执行工具前插入权限校验逻辑。

-   **核心修改点**: 在 `execute_tool_calls` 方法中，调用任何一个 `tool.call()` 之前，必须：
    1.  获取该工具的 `required_permissions`。
    2.  遍历每一个权限，通过 `PermissionManager` 检查是否已授予。
    3.  如果权限未授予，则**阻塞执行**，通过 `EventDispatcher` 发布 `PermissionRequestEvent` 事件，并异步等待一个来自 `PermissionManager` 的信号（如 `asyncio.Event`）。
    4.  根据用户的最终响应决定是继续执行还是抛出一个权限错误，从而中止任务。

### 3.4. OdaSession (修改)

修改会话类，使其成为权限响应的直接处理者。

-   **新增方法**: `handle_permission_response(permission: str, granted: bool, scope: str)` 方法，用于直接处理来自 CLI 的权限响应。
-   **职责**: 该方法接收用户的权限决策，并更新 `PermissionManager` 的状态，同时设置信号以唤醒等待的 `ToolManager`。

### 3.5. 新增事件 (Events)

-   **`PermissionRequestEvent`**: 当需要用户授权时，由 `ToolManager` 发布。
    -   `event_type`: `permission.request`
    -   `data`: 包含 `session_id`, `tool_name`, `permission` 等信息。
-   **`PermissionResponseEvent`**: 此事件已不再使用，权限响应现在通过直接调用 `OdaSession.handle_permission_response` 方法处理。

## 4. 架构与工作流程

权限校验流程被设计为从`ToolManager`的角度看是阻塞的，但在底层实现上利用了异步事件机制，以避免真正阻塞整个应用程序的事件循环。

```mermaid
sequenceDiagram
    participant Agent
    participant ToolManager
    participant PermissionManager
    participant EventDispatcher
    participant CLI
    participant User

    Agent->>ToolManager: execute_tool_calls(tool_calls)
    ToolManager->>ToolManager: For each tool_call:
    ToolManager->>OdaTool: Get required_permissions
    ToolManager->>PermissionManager: is_granted(permission)?
    
    alt Permission Already Granted
        PermissionManager-->>ToolManager: True
        ToolManager->>OdaTool: call(...)
    else Permission Not Granted
        PermissionManager-->>ToolManager: False
        ToolManager->>EventDispatcher: publish(PermissionRequestEvent)
        
        par
            ToolManager->>PermissionManager: await get_response_signal(permission)
        and
            EventDispatcher->>CLI: handle_permission_request
            CLI->>User: Prompt: "Allow tool X to perform Y?"
            User->>CLI: Provides input (Yes/No/Always/ESC)
            CLI->>OdaSession: handle_permission_response
            OdaSession->>PermissionManager: handle_permission_response
            PermissionManager->>PermissionManager: update_granted_permissions()
            PermissionManager->>PermissionManager: set_response_signal(permission)
        end
        
        alt User Grants Permission
            ToolManager->>OdaTool: call(...)
        else User Denies or ESC
            ToolManager->>Agent: Return ToolResult(status=PERMISSION_DENIED, model_message="User denied permission...")
        end
    end
```

**流程详解**:

1.  `Agent` 请求 `ToolManager` 执行工具。
2.  `ToolManager` 获取工具所需的权限列表。
3.  对于每一个权限，`ToolManager` 向 `PermissionManager` 查询授权状态。
4.  **如果已授权**，流程继续，执行工具。
5.  **如果未授权**：
    a. `ToolManager` 发布一个 `PermissionRequestEvent`。
    b. `ToolManager` **暂停执行并异步等待**一个内部信号（例如 `asyncio.Event`），这个信号由 `PermissionManager` 在收到用户响应后设置。从调用者的角度看，这是一个阻塞操作。
    c. `CLI` 监听到请求事件，向用户显示一个授权提示，例如："工具 `EditTool` 请求 `filesystem.write` 权限。是否允许? [Y]es / [N]o / [A]lways for this session"。
    d. 用户做出选择。如果用户按下 `ESC` 键，`CLI` 将其视为拒绝授权。
    e. `CLI` 直接调用 `OdaSession.handle_permission_response` 方法，传递用户的决策。
    f. `OdaSession` 更新 `PermissionManager` 的状态。如果用户同意，它会更新内部的授权记录。
    g. `PermissionManager` 设置之前 `ToolManager` 正在等待的信号，使其恢复执行。
    h. `ToolManager` 从等待中唤醒，再次检查权限。如果获得授权则继续执行工具。
    i. 如果权限被拒绝，`ToolManager` 将创建一个特殊的 `ToolResult`，其 `model_message` 中包含一条系统消息，明确告知LLM该权限已被用户拒绝。例如：`<system-message>The user has denied the 'filesystem.write' permission for the 'EditTool'. Do not attempt to use this tool for writing files again unless the user explicitly asks for it.</system-message>`。这个结果随后被返回给`Agent`，`Agent`将其添加到对话历史中，从而中止当前任务并为LLM提供上下文。

## 5. 权限命名规范

为了保持一致性和可扩展性，权限采用分层命名规范：`领域.操作`。

-   **`filesystem.read`**: 读取文件系统。
-   **`filesystem.write`**: 写入或修改文件系统。
-   **`network.request`**: 发起外部网络请求。
-   **`shell.execute`**: 执行本地shell命令。
-   **`database.query`**: 执行数据库查询。
-   **`database.mutate`**: 修改数据库内容。

## 6. 实现计划与集成步骤

1.  **定义权限常量**: 在 `src/one_dragon_agent/core/agent/tool/permission/permission.py` 中定义所有已知的权限字符串。
2.  **创建 `PermissionManager`**: 实现 `PermissionManager` 类，包含会话隔离的权限存储和用于阻塞等待的异步信号机制。
3.  **修改 `OdaTool` 基类**: 添加 `required_permissions` 抽象属性。
4.  **更新现有工具**: 为所有现存的工具（`ReadFileTool`, `EditTool` 等）实现 `required_permissions` 属性。
    -   `ReadFileTool`: `{"filesystem.read"}`
    -   `EditTool`: `{"filesystem.write"}`
    -   `TodoWriteTool`: `{}` (因为它只修改内存状态，不直接操作敏感资源)
5.  **定义权限事件**: 在 `src/one_dragon_agent/core/agent/tool/tool_event.py` 中定义 `PermissionRequestEvent`。
6.  **修改 `ToolManager`**: 集成权限校验逻辑，包括发布请求事件和**阻塞等待**用户响应。
7.  **修改 `CLI`**: 添加事件处理器来监听 `PermissionRequestEvent`，并实现用户交互提示逻辑（包括处理`ESC`键作为拒绝）。在用户响应后，直接调用 `OdaSession.handle_permission_response` 方法。
8.  **修改 `OdaSession`**: 在 `OdaSession` 中添加 `handle_permission_response` 方法，并移除对 `PermissionResponseEvent` 事件的处理。
9.  **集成到 `OdaSession`**: 在 `OdaSession` 初始化时，创建 `PermissionManager` 实例，并将其注入到 `ToolExecutionContext` 中。

## 7. 总结

该权限模块设计通过将权限声明、校验、管理和用户交互的职责清晰地分离到不同组件中，构建了一个既安全又灵活的工具执行框架。它利用项目现有的事件驱动架构，实现了对工具执行的**阻塞式**用户授权流程，为 OneDragon-Agent 在处理高风险任务时提供了必要的、用户可控的安全保障。最新实现进一步简化了权限响应的处理流程，使 `OdaSession` 成为权限管理的中心枢纽，符合最初的设计理念。