# 待办事项工具实现

## 概述

本文档描述了OneDragon-Agent系统中待办事项管理工具（`TodoWrite`）的实现。该工具的设计与Claude Code的待办事项功能兼容，提供了结构化的任务管理功能。

## 实现细节

### TodoWrite工具

`TodoWriteTool`允许使用新任务或修改后的任务更新待办事项列表，并在执行完成后直接返回最新的待办事项列表状态。

#### 功能特性
- 创建、更新或替换整个待办事项列表
- 在保存前验证任务数据
- 提供明确的成功/失败反馈
- 自动返回最新的待办事项列表状态，无需额外调用读取工具

#### 输入模式
```json
{
  "type": "object",
  "properties": {
    "todos": {
      "type": "array",
  "items": {
        "type": "object",
        "properties": {
          "content": {"type": "string", "minLength": 1},
          "status": {"type": "string", "enum": ["pending", "in_progress", "completed"]},
          "priority": {"type": "string", "enum": ["high", "medium", "low"]},
          "id": {"type": "string"}
        },
        "required": ["content", "status", "priority", "id"]
      }
    }
  },
  "required": ["todos"]
}
```

#### 实现
- 文件: `src/one_dragon_agent/core/agent/tool/todo_write.py`
- 类: `TodoWriteTool`
- 方法:
  - `is_read_only()`: 返回 `False`
  - `is_concurrency_safe()`: 返回 `True`
  - `create_start_event()`: 创建工具开始执行事件
  - `create_complete_event()`: 创建工具执行完成事件

#### 输出格式
工具执行成功后，会返回包含最新待办事项列表的系统提醒消息：
```xml
<system-reminder>
Your todo list has changed. DO NOT mention this explicitly to the user. Here are the latest contents of your todo list:

<todo-list>
[{"content": "Task 1", "status": "pending", "priority": "high", "id": "1"}, ...]
</todo-list>

Continue on with the tasks at hand if applicable.
</system-reminder>
```

## 支持组件

### ToolExecutionContext

工具执行上下文，提供工具执行所需的信息。

- 文件: `src/one_dragon_agent/core/agent/tool/context.py`
- 类: `ToolExecutionContext`
- 属性:
  - `session_id`: 会话唯一标识符
  - `event_dispatcher`: 事件分发器（不再允许为None）

### TodoManager

使用线程安全的方式管理不同会话的待办事项列表。

- 文件: `src/one_dragon_agent/core/agent/tool/todo_manager.py`
- 类: `TodoManager`
- 功能特性:
  - 会话特定的待办事项列表
  - 使用asyncio锁进行线程安全操作
  - 单例模式实现

### TodoItem

表示具有验证功能的单个待办事项。

- 文件: `src/one_dragon_agent/core/agent/tool/todo_manager.py`
- 类: `TodoItem`
- 属性:
  - `content`: 任务描述（非空字符串）
  - `status`: 任务状态（pending, in_progress, completed）
  - `priority`: 任务优先级（high, medium, low）
  - `id`: 唯一标识符

### 枚举类型

- `TaskStatus`: 定义任务状态
- `TaskPriority`: 定义任务优先级

## 使用示例

### 创建待办事项
```python
params = {
    "todos": [
        {
            "content": "实现用户认证",
            "status": "pending",
            "priority": "high",
            "id": "auth-1"
        },
        {
            "content": "编写单元测试",
            "status": "pending",
            "priority": "medium",
            "id": "test-1"
        }
    ]
}
```

### 工具执行结果
工具执行成功后，会自动返回包含最新待办事项列表的系统提醒消息，无需额外调用读取工具。系统会自动将最新的待办事项状态注入到下一次LLM调用中。

## 测试

为TodoWrite工具实现了测试：
- `tests/one_dragon_agent/core/agent/tool/todo_write/test_todo_write.py`

测试覆盖包括：
- 工具初始化
- 输入模式验证
- 正常操作
- 错误处理
- 边界情况
- 返回消息格式验证