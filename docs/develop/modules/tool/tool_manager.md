# Tool Manager 设计文档

## 1. 概述

`ToolManager` 是 OneDragon-Agent 系统中工具管理和执行的核心组件。该模块的目标是成为系统中所有工具相关职责的中央枢纽，包括工具的**发现、创建、存储和执行**。它将原先分散在各个模块中的工具管理与执行逻辑统一到一个模块中，实现高度的内聚和解耦。

## 2. 设计目标

- **集中化管理**: 将所有与工具相关的逻辑统一到一个模块中
- **职责分离**: 让其他模块专注于各自的核心职责，ToolManager 专注于工具协调
- **健壮的验证与执行**: 在执行前对参数进行严格的模式验证，并提供统一的错误处理
- **并发执行**: 内置支持并发执行声明为"并发安全"的工具，以提升性能
- **可扩展性**: 添加新工具无需修改其他模块，只需在 ToolManager 中注册即可

## 3. 核心组件

### 3.1 ToolManager 类

这是工具管理和执行引擎的核心。它是一个长生命周期的服务，由 `OdaSession` 在初始化时创建并持有。

- **核心职责**:
    - **工具发现与创建**: 自动发现所有 `OdaTool` 的子类并实例化
    - **依赖管理**: 创建并向工具注入其所需的依赖
    - **工具存储**: 内部维护所有可用工具实例的注册表
    - **执行协调**: 提供工具执行方法，协调工具执行并返回结构化结果
    - **事件管理**: 管理工具执行过程中的事件发送

### 3.2 ToolResult 类

工具调用结果的数据结构，封装了工具执行的所有相关信息，支持清晰的数据传递和处理。

- **核心字段**: 
    - `status`: 执行状态（SUCCESS, ERROR, NOT_FOUND）
    - `event_data`: 原始结果数据，用于事件分发
    - `model_message`: 格式化后供大模型使用的消息内容
    - `error_message`: 错误信息（仅在执行失败时存在）

### 3.3 ToolExecutionStatus 枚举

定义工具执行的可能状态：
- **SUCCESS**: 工具执行成功
- **ERROR**: 工具执行过程中发生错误
- **NOT_FOUND**: 请求的工具未找到

## 4. 内部工作流程

### 4.1 ToolManager 内部处理流程

```mermaid
graph TD
    subgraph "ToolManager 内部处理"
        A[接收工具调用请求] --> B[遍历每个 tool_call]
        B --> C{查找工具}
        C -->|找到| D[验证参数]
        C -->|未找到| E[创建 NOT_FOUND 结果]
        D -->|有效| F[调用工具执行]
        D -->|无效| G[创建 ERROR 结果]
        F --> H[接收工具返回的 ToolResult]
        H --> I[请求工具创建事件]
        I --> J[发送工具事件]
        J --> K[收集所有结果]
        K --> L[返回结果列表]
    end
    
    subgraph "与外部交互"
        M[Tool]
        N[EventDispatcher]
        O[Agent]
    end
    
    F --> M
    M --> H
    I --> M
    J --> N
    L --> O
```

### 4.2 工具发现与初始化流程

```mermaid
graph TD
    subgraph "ToolManager 初始化"
        A[系统启动] --> B[扫描工具目录]
        B --> C[发现 OdaTool 子类]
        C --> D[实例化工具]
        D --> E[依赖注入]
        E --> F[注册到工具注册表]
        F --> G[初始化完成]
    end
```

## 5. 并发执行管理

### 5.1 并发策略

- **串行执行**: 对于非并发安全的工具，按顺序执行
- **并行执行**: 对于并发安全的工具，使用异步任务并行执行
- **混合执行**: 支持串行和并行执行的混合模式

### 5.2 并发控制流程

```mermaid
graph TD
    subgraph "并发控制"
        A[接收工具调用列表] --> B[分析并发安全性]
        B --> C[分组工具]
        C --> D[创建执行任务]
        D --> E[调度执行]
        E --> F[监控执行状态]
        F --> G[收集执行结果]
        G --> H[按原始顺序返回]
    end
```

### 5.2 资源管理

- **任务限制**: 根据系统资源限制并发任务数量
- **超时控制**: 为工具执行设置超时时间
- **错误隔离**: 单个工具的错误不影响其他工具的执行

## 6. 事件管理

### 6.1 事件管理职责

- **事件协调**: 请求 Tool 创建特定的事件对象
- **事件发送**: 将 Tool 创建的事件发送到 EventDispatcher
- **事件顺序**: 确保事件的发送顺序与执行顺序一致

### 6.2 事件类型管理

- **开始事件**: 在工具开始执行前发送
- **结果事件**: 在工具执行完成后发送
- **完成事件**: 在所有工具执行完成后发送

## 7. 错误处理

### 7.1 错误处理策略

- **参数验证错误**: 在执行前捕获并返回错误结果
- **工具未找到错误**: 创建 NOT_FOUND 状态的结果
- **执行时错误**: 捕获工具执行异常并返回错误结果
- **系统错误**: 处理系统级别的错误，如资源不足等

### 7.2 错误恢复

- **单工具错误**: 不影响其他工具的执行
- **批量错误**: 提供详细的错误信息汇总
- **重试机制**: 对于可重试的错误提供重试选项

## 8. 与其他模块的集成

### 8.1 与 OdaSession 的集成

- **生命周期管理**: ToolManager 的生命周期与 OdaSession 绑定
- **实例创建**: OdaSession 负责创建 ToolManager 实例
- **资源清理**: 会话结束时清理 ToolManager 资源

### 8.2 与 Agent 的集成

- **执行请求**: Agent 向 ToolManager 发送工具执行请求
- **结果返回**: ToolManager 向 Agent 返回结构化的执行结果
- **职责分离**: Agent 专注于 LLM 交互，ToolManager 专注于工具协调

### 8.3 与 EventDispatcher 的集成

- **事件发送**: ToolManager 负责发送工具相关事件
- **事件订阅**: 其他组件可以订阅工具执行事件
- **状态同步**: 通过事件实现系统状态同步

## 9. 性能优化

### 9.1 缓存策略

- **工具实例缓存**: 缓存已创建的工具实例
- **执行结果缓存**: 对于幂等操作缓存执行结果
- **模式验证缓存**: 缓存工具输入模式的验证结果

### 9.2 执行优化

- **批量执行**: 优化批量工具执行的效率
- **异步处理**: 充分利用异步 I/O 提高性能
- **资源复用**: 复用连接、文件句柄等资源

## 10. 监控与调试

### 10.1 执行监控

- **执行时间统计**: 记录每个工具的执行时间
- **成功率统计**: 统计工具执行的成功率
- **错误分析**: 分析工具执行错误的原因和模式

### 10.2 调试支持

- **详细日志**: 记录工具执行的详细日志
- **执行追踪**: 支持工具执行的追踪和调试
- **性能分析**: 提供工具执行的性能分析数据

## 11. 总结

`ToolManager` 的引入是对 OneDragon-Agent 架构的一次重要优化。通过将工具管理逻辑集中到一个专门的模块中，我们实现了：

1. **更清晰的职责分离**: 每个模块都有明确的职责边界
2. **更好的可维护性**: 工具相关逻辑集中管理，便于维护
3. **更高的性能**: 内置的并发控制和优化策略提升性能
4. **更强的扩展性**: 新工具的添加和集成更加简单
5. **更好的监控能力**: 完整的执行监控和调试支持

这种设计不仅提升了代码的可维护性和可测试性，还为未来的功能扩展奠定了坚实的基础。