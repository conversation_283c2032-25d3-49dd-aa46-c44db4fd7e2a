# FileStateManager 设计文档

## 1. 概述

本文档详细阐述了 OneDragon-Agent 的一个核心服务：**FileStateManager**。该模块的设计灵感来源于对 Claude Code 中 `readFileState` 机制的深入分析，旨在为 Agent 提供一个健壮、集中化的文件读取状态管理机制，是实现“先读后写”安全策略的基石。

`FileStateManager` 的核心职责是**在会话级别追踪所有被读取过的文件**。当 `ReadFileTool` 等读取工具成功访问一个文件时，它会记录下该文件的内容以及读取操作发生时文件系统的时间戳。随后，当 `EditTool` 或 `WriteTool` 等写入工具尝试修改该文件时，它们必须通过 `FileStateManager` 进行验证，以确保：
1.  文件确实已经被读取过。
2.  文件在读取之后没有被外部程序（如代码格式化工具、用户手动编辑）修改过。

通过这种方式，`FileStateManager` 强制执行了一个安全的工作流程，极大地降低了数据丢失或意外覆盖的风险。

## 2. 设计目标

- **状态追踪**: 安全地存储和管理每个会话中已读文件的内容和时间戳。
- **安全保障**: 为写入类工具提供验证接口，强制执行“先读后写”和“内容新鲜度”检查。
- **线程安全**: 保证在异步环境中对文件状态的并发访问是安全的。
- **会话隔离**: 为每个 `OdaSession` 提供独立的状态存储，防止会话间数据串扰。
- **中心化管理**: 作为单例服务，由 `OdaSession` 统一创建和管理，并注入到所有工具的执行上下文中。

## 3. 核心组件与数据结构

### 3.1 FileStateManager 类

一个全新的会话级服务，负责管理所有已读文件的状态。

- **文件路径**: `src/one_dragon_agent/core/tool/file_state_manager.py`
- **类名**: `FileStateManager`
- **核心职责**:
    - 以 `session_id` 为键，维护独立的、线程安全的文件状态缓存。
    - 提供 `update_state` 方法，供 `ReadFileTool` 等读取类工具更新文件内容和时间戳。
    - 提供 `get_state` 方法，供 `EditTool` 等写入类工具验证文件是否已读以及内容是否被外部修改。
- **设计模式**: 与 `TodoManager` 类似，采用单例模式，由 `OdaSession` 创建并持有。

### 3.2 FileState 数据类

用于在 `FileStateManager` 中存储单个文件状态的结构。

- **文件路径**: `src/one_dragon_agent/core/tool/file_state_manager.py`
- **类名**: `FileState`
- **结构**:
    ```python
    @dataclass
    class FileState:
        content: str | bytes  # 文件内容 (文本或二进制)
        timestamp: float      # 文件读取时的修改时间戳 (mtime)
    ```

## 4. 架构与工作流程

### 4.1 架构设计

`FileStateManager` 将作为一项核心服务被 `OdaSession` 管理，并随 `ToolExecutionContext` 传递给所有工具。

```
src/one_dragon_agent/core/
├── tool/
│   ├── tool.py
│   ├── tool_manager.py
│   ├── read_file.py         # 依赖 FileStateManager
│   ├── file_state_manager.py # 包含 FileStateManager 和 FileState
│   └── context.py           # ToolExecutionContext 将包含 FileStateManager
└── sys/
    └── session.py           # 创建并持有 FileStateManager 实例
```

### 4.2 工作流程 (以 EditTool 为例)

```mermaid
graph TD
    subgraph Agent
        A[Agent.execute] -- "LLM 请求 (调用 EditTool)" --> B{tool_manager.execute_tool_calls};
    end

    subgraph ToolManager
        B -- "传入 context (含 file_state_manager)" --> C[EditTool.call];
    end

    subgraph EditTool
        C -- "1. 验证文件状态" --> D{FileStateManager.get_state};
        D -- "状态有效" --> E{执行文件编辑};
        D -- "状态无效或过时" --> F{返回错误消息};
        E -- "成功" --> G{更新 FileStateManager};
        G -- "2. 生成成功结果" --> H[生成成功消息];
    end

    subgraph FileStateManager
        D -- "file_path" --> I{返回文件状态};
        G -- "file_path, new_content, new_timestamp" --> J[更新内部状态];
    end

    F & H -- "yield OdaModelMessage" --> K[格式化的 OdaModelMessage];
    K --> A;
```

**流程详解**:

1.  `OdaSession` 启动时，创建 `FileStateManager` 单例。
2.  `ReadFileTool` 被调用后，会向 `FileStateManager` 中写入或更新对应文件的 `FileState`。
3.  当 `EditTool` 被调用时，它首先从 `ToolExecutionContext` 中获取 `FileStateManager`。
4.  它调用 `file_state_manager.get_state(file_path)` 来获取文件的当前状态。
5.  **验证逻辑**:
    - 如果返回 `None`，说明文件未被读取，操作失败。
    - 如果返回 `FileState`，则比较文件系统上该文件的当前修改时间与 `FileState.timestamp`。若文件系统时间戳更新，说明文件已被外部修改，操作失败。
6.  验证通过后，`EditTool` 执行文件修改。
7.  修改成功后，`EditTool` 再次调用 `file_state_manager.update_state()`，用新内容和新时间戳更新状态，确保后续操作基于最新的状态。

## 5. 总结

`FileStateManager` 是实现 Claude Code 级别文件操作安全性的核心组件。通过将文件读取状态集中管理，并为所有文件操作工具提供统一的验证和更新接口，它为 OneDragon-Agent 构建了一个健壮、安全且可预测的文件处理工作流，是未来实现更多高级文件工具的重要基础。
