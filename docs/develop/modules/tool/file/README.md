# 文件操作工具模块

本目录包含了 OneDragon-Agent 中与文件系统直接交互的核心工具及其支持模块的设计文档。这些工具共同构成了一个安全、健壮的文件操作子系统，其设计严格参考了 Claude Code 的"先读后写"等安全机制。

## 模块关系

文件操作的核心安全机制依赖于 `ReadFileTool` 和 `FileStateManager` 之间的紧密协作。

```mermaid
graph TD
    subgraph "底层服务"
        D[FileReader 服务]
    end
    
    subgraph "核心服务"
        A[FileStateManager]
    end

    subgraph "文件工具"
        B[ReadFileTool]
        C[EditTool / WriteTool]
    end

    B -- "1. 读取文件后, 更新状态" --> A;
    C -- "2. 修改文件前, 验证状态" --> A;
    A -- "3. 返回文件是否可写" --> C;
    C -- "4. 修改文件后, 更新状态" --> A;
    B -- "使用" --> D;
    C -- "使用" --> D;

```

1.  **`FileReader 服务`**: 提供底层的文件读取功能，包括文本文件、二进制文件、图片和PDF文件的读取。
2.  **`ReadFileTool`**: 作为所有文件读取操作的入口。当它成功读取一个文件后，会立即在 `FileStateManager` 中记录或更新该文件的内容和时间戳。
3.  **`FileStateManager`**: 作为一个会话级的状态管理器，它维护着一个"已读文件缓存"。这是实现"先读后写"安全策略的核心。
4.  **写入类工具 (如 `EditTool`, `WriteTool`)**: 在执行任何修改操作之前，它们必须向 `FileStateManager` 查询目标文件的状态，以确保文件已经被读取过，并且在读取后没有被外部修改。

这种设计确保了所有的文件写入操作都是基于已知的、最新的文件状态，有效防止了意外的数据覆盖和错误修改。

## 设计文档链接

- **[文件状态管理器 (FileStateManager)](./file_state_manager.md)**: 详细描述了文件状态缓存和"先读后写"验证机制的实现。
- **[文件读取工具 (ReadFileTool)](./tool_read_file.md)**: 详细描述了文件读取工具的功能、参数和与 `FileStateManager` 的交互。
- **[文件读取服务 (FileReader)](./file_reader.md)**: 详细描述了底层文件读取服务的功能和API。
