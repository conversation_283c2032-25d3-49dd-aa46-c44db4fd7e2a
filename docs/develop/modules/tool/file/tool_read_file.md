# ReadFile 工具 (MH2) 设计文档

## 1. 概述

本文档详细阐述了 OneDragon-Agent 的核心工具之一：**ReadFile 工具**。该工具的设计旨在提供一个安全、高效且可扩展的文件读取功能，其实现严格参考了对 Claude Code 中 `Read` 工具及其关联的强制读取机制的分析。

`ReadFileTool` 不仅仅是一个简单的文件读取器。它在 OneDragon-Agent 的生态系统中扮演着至关重要的角色，是实现“先读后写”安全策略的基石。每次成功读取文件后，它会更新一个会话级别的**文件状态缓存**，记录下文件的内容和读取时的时间戳。这个状态缓存将由 `Edit` 和 `Write` 等写入类工具进行验证，从而确保所有文件修改操作都基于最新的、已被读取过的文件版本，防止意外覆盖或操作过时内容。

## 2. 设计目标

- **安全第一**: 实现“先读后写”机制，强制写入类工具（如 `Edit`, `Write`）在操作前必须先读取文件。
- **功能对等**: 提供与 Claude Code `Read` 工具相当的功能，包括按行偏移（offset）和限制（limit）读取，以及对不同文件类型的支持。
- **状态管理**: 依赖 `FileStateManager` 来追踪已读文件的内容和时间戳。
- **高可读性输出**: 返回带行号的、格式清晰的文件内容，方便 LLM 理解和处理。
- **无缝集成**: 作为标准 `OdaTool` 无缝集成到 `ToolManager` 中，遵循现有架构。

## 3. 核心组件与数据结构

### 3.1 ReadFileTool 类

文件读取的核心实现。

- **文件路径**: `src/one_dragon_agent/core/tool/file/read_file.py`
- **类名**: `ReadFileTool`
- **核心职责**:
    - 验证输入参数（如 `file_path` 的存在性）。
    - 从文件系统读取指定文件的内容。
    - 处理不同文件类型（文本、图片、PDF等）。
    - 将成功读取的文件内容和时间戳更新到 `FileStateManager`。
    - 格式化输出，为文本文件添加行号。
    - 返回包含结果的 `OdaMessage`。

## 4. 架构与工作流程

### 4.1 架构设计

`ReadFileTool` 作为标准工具被 `ToolManager` 管理，并通过 `ToolExecutionContext` 与 `FileStateManager` 交互。

```
src/one_dragon_agent/core/
├── tool/
│   ├── tool.py
│   ├── tool_manager.py
│   ├── file/
│   │   ├── read_file.py         # 包含 ReadFileTool
│   │   ├── file_state_manager.py # 依赖 FileStateManager
│   │   └── file_reader.py       # 底层文件读取服务
│   └── context.py           # ToolExecutionContext 将包含 FileStateManager
└── sys/
    └── session.py           # 创建并持有 FileStateManager 实例
```

### 4.2 工作流程

```mermaid
graph TD
    subgraph Agent
        D[Agent.execute] -- "LLM 请求 (调用 ReadFile)" --> E{tool_manager.execute_tool_calls};
    end

    subgraph ToolManager
        E -- "传入 context (含 file_state_manager)" --> F[ReadFileTool.call];
    end

    subgraph ReadFileTool
        F -- "1. 验证路径和权限" --> G{判断文件类型};
        G -- "文本文件" --> H[_read_text_file];
        G -- "图片文件" --> I[_read_image_file];
        G -- "PDF文件" --> J[_read_pdf_file];
        G -- "其他文件" --> K[_read_binary_file];
        H & I & J & K -- "成功" --> L{更新 FileStateManager};
        H & I & J & K -- "失败" --> M{生成错误消息};
        L -- "2. 格式化内容 (加行号等)" --> N[生成成功结果];
    end

    subgraph FileStateManager
        L -- "file_path, content, timestamp" --> O[更新内部状态];
    end

    M & N -- "yield OdaModelMessage" --> P[格式化的 OdaModelMessage];
    P --> D;
```

**流程详解**:

1.  当 `Agent` 调用 `ReadFileTool` 时，`ToolManager` 会将包含 `FileStateManager` 实例的 `ToolExecutionContext` 传递给 `call` 方法。
2.  `ReadFileTool` 执行文件读取操作。
3.  根据文件类型，选择不同的读取方法：
    -   `_read_text_file`: 读取文本文件，支持 `offset` 和 `limit` 参数，并处理空文件和偏移超出范围的情况。
    -   `_read_image_file`: 读取图片文件的元数据。
    -   `_read_pdf_file`: 读取PDF文件的元数据。
    -   `_read_binary_file`: 读取其他二进制文件的元数据。
4.  如果读取成功，它会调用 `file_state_manager.update_state()`，传入文件路径、内容和从文件系统获取的最新修改时间戳。
5.  `ReadFileTool` 格式化文件内容（例如，添加行号）并将其作为成功结果返回。
6.  后续的 `EditTool` 或 `WriteTool` 在执行时，会通过 `FileStateManager` 检查该文件是否存在有效状态，从而完成安全闭环。

## 5. 功能实现

### 5.1 输入模式

工具的输入参数严格遵循 Claude Code 的设计。

```json
{
  "type": "object",
  "properties": {
    "file_path": {
      "type": "string",
      "description": "要读取的文件的绝对路径"
    },
    "offset": {
      "type": "integer",
      "description": "从指定行号开始读取 (默认为0)",
      "default": 0
    },
    "limit": {
      "type": "integer",
      "description": "要读取的最大行数 (默认为全部)"
    }
  },
  "required": ["file_path"]
}
```

### 5.2 输出格式

- **文本文件**: 返回一个字符串，每行前都带有行号和制表符，格式类似于 `cat -n`。例如：`     1	import os
     2	
`。对于空文件，返回特定的消息："文件存在但内容为空"。对于偏移超出范围的情况，返回特定的消息："偏移量超出了文件范围"。
- **图片文件**: 返回一段描述性文本，包含文件的大小、修改时间等元数据信息。
- **PDF文件**: 返回一段描述性文本，包含文件的大小、修改时间等元数据信息。
- **其他二进制文件**: 返回一段描述性文本，包含文件的大小、修改时间等元数据信息，表明文件是二进制格式且无法直接显示。
- **错误**: 返回具体的错误信息，如文件未找到、权限不足等。

### 5.3 特殊情况处理

- **Jupyter Notebooks**: 如果 `file_path` 以 `.ipynb` 结尾，工具将拒绝执行，并返回错误消息，建议用户使用专用的 `NotebookReadTool`。
- **大文件处理**: `offset` 和 `limit` 参数天然支持对大文件的分块读取。
- **空文件**: 对于空文件，返回特定的消息："文件存在但内容为空"。
- **偏移超出范围**: 对于偏移超出范围的情况，返回特定的消息："偏移量超出了文件范围"。

## 6. 总结

`ReadFileTool` 是 OneDragon-Agent 文件操作工具链的基石。它与 `FileStateManager` 紧密协作，强制执行“先读后写”规则，不仅复刻了 Claude Code 的核心安全特性，还为整个 Agent 系统构建了一个更健壮、更可预测的文件操作环境。
