# FileReader 服务设计文档

## 1. 概述

`FileReader` 服务是 OneDragon-Agent 中一个底层的、可复用的文件读取模块。它提供了一套简洁、统一的API来读取不同类型的文件，包括文本文件、二进制文件、图片和PDF文件。该服务被 `ReadFileTool` 和 `OdaSession` 等上层组件使用，以实现文件读取功能。

## 2. 设计目标

- **统一接口**: 提供一组统一的函数来读取不同类型的文件。
- **可复用性**: 作为一个独立的模块，可以被不同的上层组件安全地复用。
- **功能完整**: 支持读取文本文件的指定行范围、读取二进制文件的元数据、读取图片和PDF文件的元数据。
- **错误处理**: 提供清晰的错误处理机制。

## 3. 核心组件与数据结构

### 3.1 核心函数

#### `read_text_file(file_path: str, offset: int = 0, limit: Optional[int] = None) -> Dict[str, Any]`
读取文本文件的内容，支持指定行偏移和限制。

**参数**:
- `file_path`: 文件的绝对路径。
- `offset`: 从指定行号开始读取 (默认为0)。
- `limit`: 要读取的最大行数 (默认为全部)。

**返回值**:
一个字典，包含以下键值对：
- `success`: 布尔值，表示读取是否成功。
- `content`: 字符串，文件的内容（带行号）或错误消息。
- `is_empty`: 布尔值，表示文件是否为空。
- `is_offset_out_of_range`: 布尔值，表示偏移是否超出范围。

#### `read_binary_file(file_path: str) -> Dict[str, Any]`
读取二进制文件的元数据。

**参数**:
- `file_path`: 文件的绝对路径。

**返回值**:
一个字典，包含以下键值对：
- `success`: 布尔值，表示读取是否成功。
- `content`: 字符串，文件的元数据信息或错误消息。

#### `get_file_info(file_path: str) -> Dict[str, Any]`
获取文件的元数据信息。

**参数**:
- `file_path`: 文件的绝对路径。

**返回值**:
一个字典，包含以下键值对：
- `success`: 布尔值，表示获取信息是否成功。
- `file_path`: 字符串，文件的绝对路径。
- `size`: 整数，文件的大小（字节）。
- `modified_time`: 字符串，文件的最后修改时间。
- `error`: 字符串，错误消息（如果获取信息失败）。

## 4. 架构与工作流程

### 4.1 架构设计

`FileReader` 服务位于 `src/one_dragon_agent/core/sys/file_reader.py`。它不依赖于任何特定的上层组件，可以独立使用。

```
src/one_dragon_agent/core/sys/
├── file_reader.py       # FileReader 服务
└── session.py           # 使用 FileReader 服务
```

### 4.2 工作流程

```mermaid
graph TD
    subgraph "上层组件 (如 ReadFileTool, OdaSession)"
        A[调用 read_text_file / read_binary_file / get_file_info] --> B{FileReader 服务};
    end

    subgraph "FileReader 服务"
        B -- "读取文件" --> C[文件系统];
        C -- "返回文件内容或元数据" --> D[处理并格式化结果];
    end

    D -- "返回结果字典" --> A;
```

**流程详解**:

1.  上层组件调用 `FileReader` 服务提供的函数。
2.  `FileReader` 服务根据函数类型执行相应的文件读取操作。
3.  从文件系统读取文件内容或元数据。
4.  处理并格式化结果。
5.  返回包含结果的字典。

## 5. 总结

`FileReader` 服务通过提供一组统一的API，简化了文件读取操作。它将文件读取的核心逻辑与业务逻辑（如工具调用、会话管理）清晰分离，使得代码更加模块化和可维护。