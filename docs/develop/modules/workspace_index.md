# WorkspaceIndex 实时文件索引服务

结构优化版

## 1. 概述
### 1.1 目标

本模块旨在为 OneDragon-Agent 提供一个高性能、实时同步的文件和目录索引服务。`WorkspaceIndex` 被设计为文件搜索功能的核心后端服务，其核心目标是：

*   **极速响应**: 为客户端的文件引用功能提供毫秒级的搜索建议。
*   **数据实时性**: 保证搜索结果能实时反映文件系统的最新状态。
*   **可靠性与健壮性**: 确保任何存在于项目中的文件都能被搜索到，并能妥善处理各种边界情况和错误。
*   **资源效率**: 在提供高性能服务的同时，有效控制内存占用。

### 1.2 构造方法

`WorkspaceIndex` 类的构造方法负责初始化索引服务，配置核心路径、忽略规则和建立初始索引结构。

**参数**:
*   `root_path: str` - 项目根目录的绝对路径，所有索引操作都基于此路径
*   `core_patterns: list[str]` - 可选参数，用户定义的核心文件/目录模式列表
*   `ignore_patterns: list[str]` - 可选参数，用户定义的忽略文件/目录模式列表
*   `use_gitignore: bool` - 可选参数，是否使用 `.gitignore` 文件定义忽略规则，默认为 `True`

**参数格式说明**:
*   `core_patterns` 和 `ignore_patterns` 中的模式格式与 `.gitignore` 文件中的模式格式完全一致
*   支持使用 `pathspec` 库进行解析，支持以下模式：
    *   **文件匹配**: `src/main.py` - 精确匹配指定文件
    *   **目录匹配**: `src/core/` - 匹配指定目录下的所有文件
    *   **通配符匹配**: `*.py` - 匹配所有 Python 文件
    *   **递归匹配**: `**/*.json` - 递归匹配所有 JSON 文件
    *   **否定模式**: `!temp/` - 排除临时目录
*   系统会使用 `pathspec` 库解析这些模式，构造相应的PathSpec对象（详见第3.1.1节）

**use_gitignore 参数说明**:
*   当 `use_gitignore=True` 时，系统会处理项目中的 `.gitignore` 文件，按文件中的规则忽略文件
*   当 `use_gitignore=False` 时，系统不处理任何 `.gitignore` 文件
*   默认值为 `True`，以保持与 Git 仓库的兼容性


### 1.3 核心设计理念

为同时满足**核心文件的高性能访问**和**用户探索性访问的灵活性**，我们采用**两级索引模型**，将内存索引划分为**静态核心区**（不受LRU淘汰）和**动态发现区**（遵守LRU淘汰）。

#### 1.3.1 静态核心区定义

静态核心区包含项目中的核心文件和目录，通过 `core_patterns` 参数配置。

**构造参数**:
*   `core_patterns: list[str]` - 用户定义的核心文件/目录模式列表
*   这些路径在索引初始化时被标记为`is_core=True`，不受LRU淘汰机制影响
*   核心区的索引在内存中长期保留，确保高频搜索的核心索引具有最优的访问性能

### 1.4 使用入口

本服务的唯一入口点是 `WorkspaceIndex` 类提供的 `search` 方法。
*   **方法签名**: `async def search(self, query: str, contextual_base_path: str) -> list[IndexNode]`
*   **参数**:
    *   `query`: `str` - 用户输入的原始查询字符串（不包含`@`符号）。
    *   `contextual_base_path`: `str` - 用户发起此次搜索时所在的上下文目录，是一个相对路径。
*   **安全性说明**: 系统会对 `contextual_base_path` 和 `query` 进行两阶段安全性验证，确保所有路径操作都在项目根目录范围内进行。任何试图访问项目根目录之外的请求都会被拒绝并返回空列表。

## 2. 核心数据结构
### 2.1 IndexNode

`IndexNode` 是表示文件系统中一个实体（文件或目录）的核心数据对象。

**属性说明**:
*   `name: str` - 文件或目录的名称 (e.g., `workspace_index.py`)
*   `path: str` - 从项目根目录出发的、**范式化**的相对路径 (e.g., `src/core/sys/workspace_index.py`)
*   `is_dir: bool` - 标记该节点是否为一个目录
*   `mtime: float` - 文件最后修改时间的Unix时间戳，用于缓存和状态判断。目录节点的此属性为0。
*   `parent: Optional[IndexNode]` - 指向其父节点的直接引用，根节点的父节点为 `None`
*   `children: dict[str, IndexNode]` - 若为目录，则存储其子节点的 `name` 到 `IndexNode` 实例的映射
*   `is_core: bool` - 标记该节点是否属于"静态核心区"，此标志在节点创建后不可变

### 2.2 IndexData

`IndexData` 是存放所有索引数据的内部容器，集中管理所有索引结构。

**组成结构**:
*   **`path_to_node: dict[str, IndexNode]`**
    *   **角色**: 主索引和数据所有者
    *   **描述**: 存储项目中所有已知的 `IndexNode` 对象实例
    *   **性能**: 提供从节点的完整相对路径到其实例的 O(1) 快速访问
    *   **重要性**: 这是唯一存储 `IndexNode` 对象的地方，其他结构都是索引

*   **`path_trie: Trie`**
    *   **角色**: 路径前缀搜索加速器
    *   **描述**: 一个前缀树，其键为节点的**完整相对路径**
    *   **用途**: 支持高效的路径前缀搜索，如 `src/core` 可以快速匹配所有以该路径开头的文件

*   **`name_trie: Trie`**
    *   **角色**: 名称前缀搜索加速器
    *   **描述**: 另一个前缀树，其键为节点的**名称**
    *   **用途**: 支持高效的文件名前缀搜索，如 `main` 可以快速匹配所有名为 `main.py`、`main.js` 等的文件

*   **`dynamic_nodes_lru: OrderedDict`**
    *   **角色**: 动态发现区节点的LRU管理器
    *   **描述**: 一个有序字典，键为节点路径，值为最后访问时间戳
    *   **用途**: 维护动态节点的访问顺序，实现 LRU 淘汰策略

### 2.3 Trie 节点结构与数据存储

为保证数据的一致性和单一来源，Tries 本身只作为"索引"存在，不存储 `IndexNode` 对象。所有的 `IndexNode` 对象都统一存储在 `path_to_node` 字典中，Trie仅提供快速的前缀搜索能力。

**TrieNode 结构**:
*   `children: dict[str, TrieNode]` - 字符到子节点的映射
*   `is_end_of_word: bool` - 标记该节点是否代表一个完整字符串的结尾
*   `data: Any` - 存储与该字符串关联的数据

**data 属性存储内容**:
*   **在 `path_trie` 中**:
    *   叶子节点的 `data` 属性存储该节点对应的**单个路径字符串**
    *   例如：路径 `src/core/sys/workspace_index.py` 对应的叶子节点存储字符串 `"src/core/sys/workspace_index.py"`

*   **在 `name_trie` 中**:
    *   叶子节点的 `data` 属性存储一个包含所有同名节点**路径的列表 (`list[str]`)**
    *   例如：名称 `main.py` 对应的叶子节点存储 `["src/main.py", "test/main.py", "lib/main.py"]`

**Trie 操作机制**:

**插入操作逻辑**:
*   **字符遍历**: 从根节点开始，根据输入字符串的每个字符逐层向下遍历
*   **节点创建**: 如果某个字符对应的子节点不存在，则创建新的TrieNode
*   **结束标记**: 遍历到最后一个字符时，将对应节点的 `is_end_of_word` 标记为 `True`
*   **数据存储**: 在结束节点中存储相应的数据（路径字符串或路径列表）
*   **重复处理**: 如果插入的字符串已存在，则更新对应节点的数据

**前缀搜索逻辑**:
*   **前缀遍历**: 从根节点开始，根据前缀字符串的每个字符逐层向下遍历
*   **前缀验证**: 如果前缀遍历过程中某个字符对应的子节点不存在，则返回空结果
*   **深度优先搜索**: 从前缀的最后一个节点开始，进行深度优先遍历，收集所有 `is_end_of_word=True` 的节点
*   **数据收集**: 收集所有符合条件的节点中的 `data` 数据，返回搜索结果

## 3. 搜索策略

### 3.1 WorkspaceIndex 的初始化

在WorkspaceIndex构造完成后，系统会执行异步的初始化流程，包括PathSpec构造、核心区索引构建和文件系统事件监听启动。初始化过程采用异步设计，确保不会阻塞主线程，同时提供状态管理机制保证服务的可用性。

初始化流程如下：

1. PathSpec构造 core_patterns -> ignore_patterns -> .gitignore
2. 核心区索引构造
3. 启动文件系统事件监听

#### 3.1.1 PathSpec构造

PathSpec是WorkspaceIndex中用于文件模式匹配的核心组件，系统使用三个独立的PathSpec对象来处理不同类型的匹配需求。

**PathSpec构造时机与流程**:

三个PathSpec对象的构造采用分阶段异步构造策略，确保构造过程不会阻塞系统启动：

**构造时机**:
*   **同步构造阶段**: 在WorkspaceIndex构造函数中同步构造基础PathSpec对象
*   **异步构造阶段**: 对于耗时的.gitignore处理，采用异步构造方式
*   **构造顺序**: 按照core_pathspec → ignore_pathspec_static → ignore_pathspec_git的顺序构造
*   **语法错误**: 如果用户提供的 `core_patterns` 或 `ignore_patterns` 包含语法错误，系统会在构造时抛出 `PathSpecSyntaxError`，终止系统初始化

**core_pathspec构造**:
*   **作用**: 用于识别和标记核心区文件和目录，匹配到的文件会被设置为 `is_core=True`，不受LRU淘汰机制影响。
*   **构造来源**: 来自构造方法参数 `core_patterns: list[str]`，并根据 `use_gitignore` 的设置进行动态调整。
*   **构造方式**: 
    *   在构造函数中同步构造，确保核心区定义立即可用。
    *   **自动包含.gitignore**: 当 `use_gitignore=True` 时，系统会自动将 `**/.gitignore` 模式追加到核心规则列表中。这确保了项目中所有的 `.gitignore` 文件本身都被视为核心文件并被索引。
    *   **目的**: 将 `.gitignore` 文件自身加入核心区，使得在监听到其内容变更时，系统可以快速地从内存索引中定位所有 `.gitignore` 文件，从而高效地重建忽略规则，而无需重新扫描整个磁盘。
    *   使用 `pathspec` 库解析最终的模式列表（用户定义 + 自动追加）。
    *   使用 `pathspec.PathSpec.from_lines()` 方法构造 `core_pathspec` 对象。
*   **构造失败处理**: 如果构造失败，抛出 `PathSpecConstructionError`，终止系统初始化
*   **匹配优先级**: 最高优先级，核心区文件不会被任何忽略规则排除

**ignore_pathspec_static构造**:
*   **作用**: 处理用户定义的静态忽略规则，匹配到的文件会被排除在索引之外。
*   **构造来源**: 来自构造方法参数 `ignore_patterns: list[str]`
*   **构造方式**: 
    *   在构造函数中同步构造，确保忽略规则立即可用
    *   验证 `ignore_patterns` 中的每个模式是否符合 `.gitignore` 语法
    *   使用 `pathspec.gitignore.GitIgnoreSpec.from_lines()` 方法构造 `ignore_pathspec_static` 对象
*   **构造失败处理**: 如果构造失败，抛出 `PathSpecConstructionError`，终止系统初始化
*   **匹配优先级**: 次高优先级，用户定义的忽略规则优先于.gitignore规则

**ignore_pathspec_git构造**:
*   **作用**: 处理 `.gitignore` 文件中定义的忽略规则，支持动态更新。
*   **构造来源**: 项目中所有的 `.gitignore` 文件，当 `use_gitignore=True` 时生效。
*   **构造方式**: 
    *   **递归扫描**: 系统会递归扫描项目根目录，定位所有 `.gitignore` 文件。
    *   **规则作用域重写**: 为了正确处理不同目录下 `.gitignore` 文件的作用域，系统会手动重写规则字符串，而不是依赖 `pathspec` 的 `base_dir` 功能。
        *   对于每一个找到的 `.gitignore` 文件，系统会确定其所在的目录相对于项目根目录的路径（例如 `src/core/`）。
        *   系统会逐行读取该文件中的忽略规则，并根据其所在目录和规则内容，为其添加路径前缀，以确保规则只在正确的范围内生效。
        *   **重写逻辑示例**:
            *   如果 `src/core/.gitignore` 中有一条规则 `temp.log`，它会被重写为 `src/core/temp.log`。
            *   如果规则是 `/temp.log`（表示相对于 `.gitignore` 文件所在目录的根），它会被重写为 `/src/core/temp.log`。
            *   否定规则如 `!config.json` 会被重写为 `!src/core/config.json`。
        *   对于项目根目录下的 `.gitignore` 文件，规则通常保持原样。
    *   **统一构造**: 系统会将所有 `.gitignore` 文件中经过重写的规则收集到一个单一的列表中，然后使用 `pathspec.gitignore.GitIgnoreSpec.from_lines()` 方法，根据这个扁平化的规则列表构造一个统一的 `ignore_pathspec_git` 对象。
*   **构造失败处理**: 
    *   如果.gitignore文件不存在或无法读取，记录警告并跳过该文件。
    *   如果.gitignore文件语法错误，记录错误并跳过该文件的规则。
    *   构造失败不会终止系统初始化，只是忽略相关的.gitignore规则。
*   **动态更新**: 当监听到 `.gitignore` 文件变更时，会重新构造 `ignore_pathspec_git` 对象。

**PathSpec匹配逻辑**:

系统在处理文件时，按照以下优先级顺序进行匹配判断：

1.  **核心区匹配**: 首先使用 `core_pathspec.match_file()` 检查文件是否属于核心区
    *   如果匹配成功，文件被标记为 `is_core=True`，直接进入索引，跳过后续忽略检查
    *   如果匹配失败，继续进行忽略规则检查

2.  **静态忽略匹配**: 使用 `ignore_pathspec_static.match_file()` 检查文件是否被用户定义的规则忽略
    *   如果匹配成功，文件被忽略，不进入索引
    *   如果匹配失败，继续进行.gitignore规则检查

3.  **Git忽略匹配**: 使用 `ignore_pathspec_git.match_file()` 检查文件是否被.gitignore规则忽略
    *   如果匹配成功，文件被忽略，不进入索引
    *   如果匹配失败，文件进入索引，标记为 `is_core=False`

**PathSpec实现细节**:

*   **存储结构**: 三个PathSpec对象存储在 `self._core_pathspec`、`self._ignore_pathspec_static`、`self._ignore_pathspec_git` 属性中
*   **线程安全性**: PathSpec对象本身是线程安全的，可以在多线程环境中并发使用
*   **匹配性能**: 单个文件的匹配时间复杂度为O(n)，其中n为模式数量，通常在毫秒级别

**PathSpec匹配结果**:

返回两个布尔值，分别表示"是否应该索引"和"是否为核心区文件"

#### 3.1.2 核心区索引初始化

PathSpec构造完成后，系统会执行核心区索引初始化流程，非核心区文件通过回退扫描机制补充。

**初始化状态管理机制**:

系统提供完整的初始化状态管理，确保在初始化过程中服务的可用性和一致性：

*   **并发控制**: 使用异步锁机制确保初始化过程的线程安全性，防止并发初始化
*   **状态通知**: 提供状态变更通知机制，允许外部组件监听初始化状态变化

**初始化失败处理策略**:

当初始化过程中遇到错误时，输出错误日志。

**核心区索引初始化流程**:

1. **核心区文件识别**: 使用 `core_pathspec.match_file()` 扫描项目根目录，识别所有核心区文件和目录
2. **IndexNode创建**: 为每个核心区文件和目录创建 `IndexNode` 对象
   *   设置 `name`、`path`、`is_dir` 等基本属性
   *   设置 `is_core=True` 标记为核心区节点
   *   建立父子节点关系，形成完整的目录树结构
3. **索引结构填充**: 将创建的核心区 `IndexNode` 对象添加到各个索引结构中
   *   添加到 `path_to_node` 字典中，以路径为键存储对象
   *   将路径添加到 `path_trie` 中，支持路径前缀搜索
   *   将名称添加到 `name_trie` 中，支持名称前缀搜索
   *   核心区节点不添加到 `dynamic_nodes_lru` 中（因为不受LRU淘汰影响）
4. **初始化完成**: 更新状态为"初始化完成"，触发状态变更通知

**search方法的初始化处理逻辑**:

`search` 方法根据初始化状态采用不同的处理策略，以确保服务的最高可用性和最快响应速度：

*   **初始化完成状态**: 正常执行完整的搜索流程。
*   **初始化中状态**:
    1.  **立即搜索核心区**: 请求首先会在当前已部分完成的索引（主要包含核心区文件）中进行一次即时搜索。
    2.  **若命中则立即返回**: 如果在核心区找到了匹配结果，则立即返回，不进行任何等待。
    3.  **若未命中则等待并衔接主流程**: 如果核心区中没有找到结果，请求将异步等待初始化过程完成（最大等待超时30秒）。**等待结束后，请求会使用刚刚构建完成的完整索引再次执行搜索。如果依然未命中，则无缝衔接至标准的回退扫描流程（详见 3.4 节），以确保对文件系统的全面查找。**
*   **等待超时或初始化失败状态 (降级模式)**:
    *   如果等待超时，或初始化过程最终失败，系统将进入**降级服务模式**。
    *   在此模式下，`search` 请求将只在当前不完整的索引上执行，并返回任何能找到的结果。这确保了即使在最坏的情况下，系统依然能提供部分服务，而不是完全拒绝请求或抛出异常。

这种分层处理机制确保了核心文件始终具有毫秒级响应能力，同时在系统启动过程中保持了良好的可用性和健壮性。

#### 3.1.3 文件系统事件监听机制

文件系统事件监听是实现实时同步的核心机制，系统通过watchdog库监听文件系统变化来保持索引数据与实际文件系统状态的一致性。

**监听启动与停止机制**:

*   **启动时机**: 在核心区索引初始化完成后自动启动文件系统监听
*   **停止机制**: 提供优雅的停止机制，在系统关闭时确保所有事件处理完成
*   **重启策略**: 监听异常终止时自动重启，最多连续重启3次

**监听范围与事件类型**:

*   **监听范围**: 监听整个项目根目录及其所有子目录的文件系统变化

### 3.2 输入处理与验证

在 `search` 方法入口，首先对 `query` 和 `contextual_base_path` 进行规范化处理，并判断搜索类型。规范化后不处理空字符串的搜索，直接返回空列表。

#### 3.2.1 搜索类型判断

在开始规范化处理之前，系统首先需要判断用户的搜索意图，确定 `is_listing_request` 标记的值：

**`is_listing_request` 赋值逻辑**:

*   **判断条件**: 检查原始 `query` 字符串（去除首尾空格后）是否以 `/` 结尾
*   **赋值规则**:
    *   如果 `query.strip().endswith('/')` 为 `True`，则设置 `is_listing_request = True`
    *   否则，设置 `is_listing_request = False`

**判断时机**: 此判断在规范化处理之前进行，因为结尾的 `/` 是用户明确表达目录列出意图的语法标记，需要在路径清理过程中保留此信息。

**示例说明**:
*   用户输入 `@src/` → `query` 为 `"src/"` → `is_listing_request = True`
*   用户输入 `@src/core` → `query` 为 `"src/core"` → `is_listing_request = False`
*   用户输入 `@` → `query` 为 `""` → `is_listing_request = False`

#### 3.2.2 路径与查询字符串规范化

**内部范式**: 所有内部路径都遵循**无前导/结尾斜杠**的格式。根目录由**空字符串 `''`** 表示。

**规范化处理流程**:

对 `query` 和 `contextual_base_path` 在入口处进行严格的两阶段规范化处理，确保所有路径操作都在项目根目录范围内进行。

**阶段一：contextual_base_path 合法性验证**

1.  **去除首尾空格**: 移除 `contextual_base_path` 字符串前后的所有空白字符，确保输入的干净性。

2.  **统一路径分隔符**: 将所有反斜杠 `\` 替换为正斜杠 `/`，以保证跨平台的一致性。

3.  **路径拼接与解析**: 将 `root_path` 与 `contextual_base_path` 拼接为完整路径，然后进行路径解析。
    *   **相对路径解析**: 逐级处理路径中的 `.` 和 `..`，`.` 表示当前目录，`..` 表示上级目录
    *   **路径清理**: 移除路径中多余的 `./` 和连续的斜杠，简化路径结构
    *   **符号链接处理**: 解析符号链接的真实路径，避免路径遍历攻击

4.  **安全性验证**: 检查解析后的路径是否在项目根目录范围内。
    *   **范围检查算法**: 将解析后的绝对路径与项目根目录进行规范化比较，确保路径不超出根目录范围
    *   **违规处理**: 如果路径超出项目根目录范围，直接返回空列表，拒绝处理
    *   **合法路径处理**: 如果路径合法，提取相对于项目根目录的路径作为**真正的 contextual_base_path**

**阶段二：目标路径构建与规范化**

5.  **初步清理**: 对原始 `query` 字符串进行初步清理，包括去除首尾空格和统一路径分隔符（`\` -> `/`）。

6.  **构建目标绝对路径**: 将 `root_path`、第一阶段验证过的 `contextual_base_path` 和初步清理后的 `query` 拼接起来，形成一个完整的目标绝对路径。

7.  **路径解析与安全验证**:
    *   **解析**: 对该目标绝对路径进行彻底的解析，处理所有相对路径指示符（如 `.` 和 `..`），并解析任何符号链接，得到最终的真实路径。
    *   **验证**: 检查该真实路径是否在项目根目录 `root_path` 的范围之内。如果路径非法（例如，试图通过 `../` 遍历到根目录之外），则立即拒绝请求，返回空列表。

8.  **提取规范化查询**: 如果路径验证通过，则将这个**最终的、安全的、绝对的真实路径**转换为相对于 `root_path` 的路径。这个结果就是本次搜索所使用的**规范化查询字符串**。

9.  **格式统一**: 确保该规范化查询字符串符合内部范式，即不包含前导或结尾的斜杠（根目录本身表示为空字符串 `''`）。



这种两阶段规范化处理确保了所有路径操作的安全性。


#### 3.2.3 空查询处理

在完成规范化处理后，系统会检查 `query` 是否为空字符串：

*   如果规范化后的 `query` 为空字符串，无论 `is_listing_request` 为何值，都直接返回空列表，不进行任何搜索操作。
*   这样设计简化了处理逻辑，避免了空字符串搜索的特殊情况判断。

### 3.3 自适应搜索策略

系统根据规范化后的 `query` 和 `is_listing_request` 标记，选择以下一种模式执行：

#### 3.3.1 目录内容列出 (Directory Listing)

*   **触发条件**: `is_listing_request` 为 `True`（例如，用户输入 `@src/`）。
*   **处理流程**:
    1.  使用规范化后的 `query` 作为 `path` 键。
    2.  在 `path_to_node` 字典中进行 O(1) 查找。
    3.  如果找到节点，且 `node.is_dir` 为 `True`，则返回 `list(node.children.values())`。
    4.  否则，返回空列表。
    *   **健壮性设计**: 此模式直接依赖内存索引，快速响应，且通过 `path_to_node` 的精确查找保证了准确性。

#### 3.3.2 路径前缀搜索 (Path Prefix Search)

*   **触发条件**: 规范化后的 `query` 包含 `/`（例如，用户输入 `@src/core`）。
*   **处理流程**:
    1.  在 `path_trie` 中根据 `query` 字符串进行遍历，找到其在前缀树中对应的末端节点。
    2.  如果找不到（即前缀不存在），触发回退扫描 (Fallback to 3.4.1)。
    3.  从该末端节点开始，深度优先遍历其所有后代节点。
    4.  收集所有 `is_end_of_word` 为 `True` 的后代节点中 `data` 属性存储的路径字符串。
    5.  使用收集到的路径字符串列表，从 `path_to_node` 字典中批量获取对应的 `IndexNode` 对象。
    6.  对结果按路径排序后返回。
    *   **健壮性设计**: 使用 `path_trie` 加速前缀匹配，并通过最终从 `path_to_node` 获取对象来保证数据的完整性和一致性。

#### 3.3.3 名称前缀搜索 (Name Prefix Search)

*   **触发条件**: 规范化后的 `query` 不包含 `/`（例如，用户输入 `@main`）。
*   **处理流程**: 这是一个**上下文感知**的两阶段搜索。
    1.  **阶段一 (上下文搜索)**:
        a. 使用 `contextual_base_path` 在 `path_to_node` 中找到上下文目录的 `IndexNode`。
        b. 如果找到，则遍历其 `children` 字典，对每个子节点的 `name` 进行不区分大小写的前缀匹配。
        c. 如果有匹配结果，则直接返回这些结果，搜索结束。
        *   **健壮性设计**: 优先在用户当前上下文目录中查找，提高相关性。大小写不敏感匹配提升用户体验。
    2.  **阶段二 (全局搜索)**:
        a. **只有当**阶段一没有返回任何结果时，才启用 `name_trie` 进行全局搜索。
        b. 执行与"路径前缀搜索"几乎相同的流程，只是操作对象是 `name_trie`。
        c. 从 `name_trie` 的叶子节点中收集到的 `data` 是一个**路径列表**，需要将所有列表合并并去重。
        d. 使用最终的路径列表，从 `path_to_node` 获取 `IndexNode` 对象并返回。
        *   **健壮性设计**: 全局名称搜索作为后备方案，能覆盖更广泛的情况。合并去重确保结果唯一性。

### 3.4 回退扫描

当内存索引搜索未命中时，意味着当前索引中没有与查询匹配的数据。此时，会回退到文件系统扫描，动态发现并补充索引数据。

根据查询类型的不同，系统会采用两种不同的扫描策略。

#### 3.4.1 目录单层扫描

当用户进行路径前缀搜索（即 `query` 包含 `/`）但未命中时，系统推断用户正在探索一个尚未被完全索引的目录。此时，执行全局扫描的成本过高且没有必要。

*   **触发条件**: 路径前缀搜索 (Path Prefix Search) 在 `path_trie` 中未找到任何匹配项。
*   **处理流程**:
    1.  **定位父级目录**: 从查询字符串 `query` 中解析出最深层的父级目录路径。例如，对于查询 `src/core/utils/helpers.py`，系统会解析出 `src/core/utils`。
    2.  **检查父级目录真实性**: **直接调用文件系统API来判断该父级目录是否存在于磁盘上**。这样做比仅在内存索引中检查更为准确，可以正确处理在初始索引构建后于文件系统上新建的目录。
        *   **健壮性设计**: 通过直接的文件系统调用确保判断的准确性，并能处理目录刚被创建的情况。
    3.  **执行单层扫描**:
        *   如果API确认父级目录存在，系统将对该目录在真实文件系统上执行一次**单层**的文件和子目录列表扫描。
        *   如果API确认父级目录不存在，则直接返回空结果。
        *   使用 `pathlib.Path` 对象，并调用其 `iterdir()` 方法来遍历目录下的所有直接子项。`p.iterdir()` 会生成一个迭代器，可以高效地逐一处理文件和子目录。
        *   扫描过程中使用PathSpec进行文件过滤。对于每个发现的文件，先检查是否为核心区文件，如果不是则使用相应的PathSpec进行忽略检查。只有不被忽略的文件才会被添加到索引中。
    4.  **增量更新索引**: 将扫描到的新文件和目录（以及父目录，如果它本身尚未被索引）作为**动态节点 (dynamic nodes)** 添加到 `path_to_node`, `path_trie`, `name_trie` 和 `dynamic_nodes_lru` 中。
        *   **健壮性设计**: 新节点被正确地集成到所有相关的索引结构中，并标记为动态节点以参与 LRU 管理。
    5.  **重新执行搜索**: 在索引更新后，**重新执行一次原始的路径前缀搜索**。
    6.  **返回结果**: 返回第二次搜索的结果。
    *   **健壮性设计**: 这种策略极大地提升了用户在项目目录树中进行探索性浏览时的响应速度和体验，避免了不必要的全局扫描，并通过重新搜索确保结果的准确性。

#### 3.4.2 文件名全局扫描

当用户进行名称前缀搜索（即 `query` 不含 `/`）且在全局 `name_trie` 中未命中时，系统认为用户正在寻找一个特定名称的文件，但其位置未知。

*   **触发条件**: 名称前缀搜索 (Name Prefix Search) 在 `name_trie` 中未找到任何匹配项。
*   **处理流程**:
    1.  **并发控制**: 在启动磁盘扫描前获取一个全局锁。这可以防止因短时间内多个未命中请求而触发多次重复的、昂贵的全局文件扫描。
        *   **锁等待优化**: 如果锁已被其他任务持有，则当前任务将异步等待锁释放。**等待锁释放后，直接使用其他任务已更新的索引进行搜索，无需再次触发全局扫描**。
        *   **健壮性设计**: 通过并发锁机制防止资源耗尽和重复工作，同时优化了锁等待场景的处理效率。
    2.  **执行全局扫描**: 启动一个全项目范围的文件系统扫描。此扫描的目标是明确的：找到所有名称与 `query` 匹配的文件或目录。为了优化性能并保证兼容性，这个扫描过程会：
        *   扫描过程中使用PathSpec进行文件过滤。对于每个发现的文件，先检查是否为核心区文件，如果不是则使用相应的PathSpec进行忽略检查。只有名称匹配且不被忽略的文件才会被添加到索引中。
        *   使用 `pathlib` 库来实现此功能。通过创建一个指向项目根目录的 `pathlib.Path` 对象，并调用其 `rglob('*')` 方法，可以高效地递归遍历所有文件和目录，对每个返回的 `Path` 对象的 `name` 属性与 `query` 进行匹配。
    3.  **增量更新索引**:
        *   对于扫描过程中发现的每一个匹配项，系统会检查它是否已存在于 `path_to_node` 索引中。
        *   如果不存在，系统会为其创建 `IndexNode`，并**递归地为其所有尚未被索引的父目录创建节点**，直到根目录。
        *   所有新创建的节点都将被标记为**动态节点**，并被添加到所有相关的索引结构中。
        *   **健壮性设计**: 确保新发现的节点及其完整路径都被正确索引，防止出现索引断裂。
    4.  **释放锁与搜索**: 扫描和索引更新完成后，释放全局锁，然后**执行一次原始的名称前缀搜索**。
    5.  **返回结果**: 返回搜索结果。
    *   **健壮性设计**: 通过这种方式，即使用户查询的文件尚未在初始索引中，系统也能够通过一次目标明确的全局查找来定位它，并将其及相关路径信息动态地融入到内存索引中，服务于后续的查询。锁等待优化避免了重复的全局扫描，提升了系统性能。

## 4. 事件驱动的索引更新

系统通过监听文件系统事件（如创建、删除、移动/重命名）来实时更新内存索引，确保索引数据与文件系统状态保持一致。

### 4.1 文件/目录创建

当监听到文件或目录创建事件时，系统执行以下操作：

1.  **路径检查**: 首先检查新建项的路径是否符合PathSpec忽略规则。如果路径应被忽略，则忽略此事件，不进行后续操作。
    *   **健壮性设计**: 防止索引无关或无权限的文件，保证索引的干净和高效。
2.  **节点创建**: 为新创建的文件或目录创建一个新的 `IndexNode` 对象。
    *   此节点的 `is_core` 属性通常被初始化为 `False`（因为它是运行时发现的动态节点）。
    *   **特殊情况**: 如果新建项匹配 `core_pathspec`，则可以将其 `is_core` 初始化为 `True`。但在典型的实时监听场景下，新创建的文件更可能是动态的。
3.  **父节点查找与关联**:
    *   根据新建项的路径，查找其父目录对应的 `IndexNode`。
    *   如果父节点不存在（例如，父目录也是刚刚创建的且事件尚未处理），则递归地为其父目录（直至根目录）创建 `IndexNode`。
    *   新创建的父节点的 `is_core` 属性同样遵循上述规则。
    *   将新创建的节点添加到其父节点的 `children` 字典中。
4.  **索引更新**:
    *   将新创建的节点及其所有新建的父节点添加到 `path_to_node` 字典中。
    *   将它们的路径和名称分别添加到 `path_trie` 和 `name_trie` 中。
5.  **LRU管理**: 将新创建的节点（非其父节点）添加到 `dynamic_nodes_lru` 的末尾。
    *   **注意**: 只有 `is_core=False` 的节点才会被添加到 LRU 中。
6.  **内存检查**: 触发一次 LRU 淘汰检查，以确保动态节点总数不超过上限。

### 4.2 文件/目录删除

当监听到文件或目录删除事件时，系统执行以下操作：

1.  **节点查找**: 根据被删除项的路径，在 `path_to_node` 字典中查找对应的 `IndexNode`。如果找不到，则忽略此事件。
    *   **健壮性设计**: 处理可能的重复或过时事件。
2.  **核心节点处理**: 检查找到的节点的 `is_core` 属性。
    *   **无论 `is_core` 是 True 还是 False**: 核心节点 (`is_core=True`) 和动态节点 (`is_core=False`) 都会从索引中被移除。**索引必须始终反映文件系统的当前真实状态**。`is_core` 标志的核心作用是保护节点**不被LRU内存淘汰策略自动移除**，但它**不能**阻止节点因响应外部文件系统的真实删除事件而被动地从索引中移除。
3.  **索引移除 (Path)**: 从 `path_to_node` 字典中移除该节点的条目。
4.  **索引移除 (Tries)**:
    *   从 `path_trie` 中移除该节点的路径。
    *   从 `name_trie` 中移除该节点的名称。这需要从该名称对应的路径列表中移除此节点的路径。如果列表变为空，则从 `name_trie` 中完全移除该名称的条目。
5.  **LRU管理**: 如果该节点存在于 `dynamic_nodes_lru` 中（即 `is_core=False`），则将其从 LRU 字典中移除。
6.  **父子关系解除**: 从其父节点的 `children` 字典中移除对该节点的引用。
7.  **递归处理 (目录)**: 如果被删除的是一个目录，需要递归地对其所有子节点执行上述 2-6 的操作。

### 4.3 文件/目录移动/重命名

当监听到文件或目录移动/重命名事件时，系统执行以下操作以保证数据一致性和原子性：

1.  **源节点查找**: 根据事件中的旧路径，在 `path_to_node` 字典中查找对应的源 `IndexNode` 对象。如果找不到，则忽略此事件。
    *   **健壮性设计**: 处理可能的无效或过时的移动事件。
2.  **核心节点处理**: 检查源节点的 `is_core` 属性。
    *   **无论 `is_core` 是 True 还是 False**: 移动操作都会完整执行。`is_core=True` 表示该节点不受 LRU 缓存管理策略影响，但它仍然是索引的一部分，其路径和名称的变化必须被准确跟踪。移动核心文件是一个需要索引正确反映的文件系统变更。
3.  **数据准备**: 递归地计算出源节点及其所有子节点（如果是目录）的**新路径和新名称**。
4.  **索引移除 (旧信息)**: **首先**，将源节点及其所有子节点的**旧信息**从所有索引中**完全移除**。这包括：
    *   从 `path_to_node` 移除旧路径到节点的映射。
    *   从 `path_trie` 和 `name_trie` 移除旧的路径和名称。
    *   从 `dynamic_nodes_lru` 中移除（如果存在，即 `is_core=False`）。
    *   从旧父节点的 `children` 字典中移除引用。
5.  **节点更新**: **然后**，在内存中更新源节点及其所有子节点的 `IndexNode` 对象的 `path` 和 `name` 属性。
    *   节点的 `is_core` 状态**必须保持不变**。移动操作本身不改变一个节点是否为核心节点的属性。
6.  **索引添加 (新信息)**: **最后**，将源节点及其所有子节点的**新信息**重新添加到所有索引中。这包括：
    *   将节点添加到 `path_to_node` 字典（使用新路径作为键）。
    *   将新路径和新名称添加到 `path_trie` 和 `name_trie`。
    *   将节点添加到 `dynamic_nodes_lru` 的末尾（**仅对 `is_core=False` 的节点**）。
7.  **父子关系变更**:
    *   更新源节点在新父节点的 `children` 字典中的引用。
    *   如果是目录移动，还需更新其所有子节点在各自新父节点 `children` 字典中的引用。

### 4.4 文件内容修改

当监听到一个已索引文件的内容发生变更时，系统会执行以下操作来更新其元数据。

1.  **节点查找**: 根据事件提供的路径，在 `path_to_node` 字典中查找对应的 `IndexNode`。如果找不到，则忽略此事件。
2.  **时间戳更新**: 如果找到节点，系统会从文件系统中获取该文件最新的修改时间戳。
3.  **属性赋值**: 将获取到的新时间戳更新到 `IndexNode` 实例的 `mtime` 属性上。

此操作不影响节点的其他属性，也不会改变其在LRU缓存中的位置。

### 4.5 特殊文件处理：.gitignore 的变更

由于 `.gitignore` 文件直接定义了索引的忽略规则，因此系统会对所有影响 `.gitignore` 文件的事件进行特殊处理，以确保忽略规则的实时性。

**触发条件**:
以下任何事件都会触发忽略规则的重建：
*   **创建**: 在项目中任何位置创建了一个新的 `.gitignore` 文件。
*   **删除**: 删除了一个已存在的 `.gitignore` 文件。
*   **移动/重命名**: 一个文件被重命名为 `.gitignore`，或一个 `.gitignore` 文件被重命名为其他名称，或一个 `.gitignore` 文件被移动到新的目录。
*   **内容修改**: 一个已存在的 `.gitignore` 文件的内容发生了变更。

**核心操作**:
*   一旦监听到上述任一事件，系统会立即触发 `ignore_pathspec_git` 对象的**异步重新构造**。
*   该流程与初始化时完全一致（详见 3.1.1 节），利用内存中已索引的 `.gitignore` 文件列表来高效完成重建。

**并发请求处理**:
*   **服务不中断**: 在 `ignore_pathspec_git` 异步重建的过程中，新的 `search` 请求**不会被阻塞或等待**。
*   **使用旧规则**: 进入的请求会立即使用**当前内存中仍然有效的、旧的 `PathSpec` 对象**进行搜索。这保证了服务的100%可用性。
*   **原子替换**: 当新的 `PathSpec` 对象在后台构建完成后，系统会通过一次**原子操作**将其替换掉旧的对象。此后的新请求将自动使用更新后的忽略规则。

**后续影响**:
*   新的忽略规则生效后，系统会启动一个后台任务来同步索引。此任务会负责移除那些新近被忽略的文件，并扫描添加那些不再被忽略的文件，确保索引与规则的完全一致。

## 5. 内存管理与性能优化

### 5.1 LRU淘汰策略

#### 5.1.1 基于OrderedDict的实现

**数据结构**:

*   **`dynamic_nodes_lru`: `OrderedDict`**
    *   键为节点路径，值为最后访问时间戳
    *   维护节点的访问顺序，最近访问的节点位于字典末尾，最久未访问的节点位于字典开头

**核心操作**:

1.  **节点访问时更新**:
    *   当动态节点被访问时，先将其从`OrderedDict`中删除，然后以当前时间戳重新插入到字典末尾

2.  **淘汰操作**:
    *   当需要淘汰节点时，直接从字典开头取出最久未访问的节点，直到节点数量降至上限以下

3.  **节点添加**:
    *   新节点以当前时间戳插入到字典末尾

**淘汰操作的具体实现**:

淘汰操作是一个复杂的过程，需要从多个数据结构中同步删除节点，确保数据一致性：

*   **淘汰触发条件**: 当动态节点数量超过上限（默认10,000）时触发淘汰
*   **淘汰数量计算**: 根据超出的数量决定淘汰的节点数量，通常一次性淘汰超出数量的110%
*   **淘汰节点选择**: 从LRU字典开头选择最久未访问的节点进行淘汰
*   **淘汰执行步骤**:
  1. 从 `dynamic_nodes_lru` 中移除节点记录
  2. 从 `path_to_node` 字典中移除节点对象
  3. 从 `path_trie` 中移除节点路径信息
  4. 从 `name_trie` 中移除节点名称信息
  5. 从父节点的 `children` 字典中移除节点引用
  6. 递归处理子节点（如果是目录节点）

**淘汰过程中的并发控制**:

*   **淘汰锁**: 使用专门的淘汰锁确保淘汰过程的原子性
*   **写操作隔离**: 淘汰过程中暂停其他写操作，避免数据不一致
*   **读操作兼容**: 淘汰过程允许读操作继续进行，通过适当的同步机制保证数据一致性
*   **超时处理**: 为淘汰操作设置超时时间，避免长时间阻塞系统

**淘汰操作的性能优化**:

*   **批量淘汰**: 采用批量淘汰策略，减少锁竞争和操作开销
*   **惰性删除**: 对Trie节点的删除采用惰性策略，标记后延迟实际删除
*   **内存回收**: 淘汰完成后触发内存回收，及时释放不再使用的内存
*   **性能监控**: 实时监控淘汰操作的性能指标，动态调整淘汰策略

**淘汰失败的处理**:

*   **部分失败处理**: 如果淘汰过程中某个步骤失败，记录错误并继续处理其他节点
*   **数据一致性检查**: 淘汰完成后进行数据一致性检查，确保所有数据结构状态一致
*   **失败重试**: 对于可恢复的失败，采用短时间重试机制
*   **降级处理**: 对于不可恢复的失败，采用降级处理策略，确保系统基本功能可用

#### 5.1.2 淘汰策略配置

*   **作用范围**: 只作用于**动态发现区**的节点（即 `is_core=False` 的节点）。
*   **数量上限**: 总节点数受上限（默认10,000）限制。
*   **清理时机**: **在每次成功添加新节点后**触发检查。
*   **淘汰流程**: 若超限，则从字典开头开始，依次取出最久未访问的节点，并执行完整的删除操作，直到节点数量降至上限以下。

### 5.2 LRU状态管理

#### 5.2.1 索引命中时的LRU更新

**是否需要更新**: 是，当索引命中时，需要对动态节点进行LRU状态更新。

**更新逻辑**: 对于在内存索引搜索中找到的每个匹配节点，需要根据其 `is_core` 属性进行不同的处理：

*   **动态节点 (`is_core=False`)**:
    1.  **检查存在性**: 确认该节点的路径存在于 `dynamic_nodes_lru` 字典中
    2.  **更新访问时间**: 获取当前时间戳，先从字典中删除该节点，然后以新的时间戳重新插入到字典末尾
    3.  **更新目的**: 将该节点标记为最近访问，延长其在缓存中的保留时间

*   **核心节点 (`is_core=True`)**:
    1.  **不进行LRU操作**: 核心节点不受LRU淘汰机制影响
    2.  **保持原状**: 不对 `dynamic_nodes_lru` 进行任何修改
    3.  **设计原因**: 核心节点属于静态核心区，需要长期保留在内存中以确保高性能访问

**更新时机**: LRU状态更新发生在以下两个时机：

1.  **首次索引命中时** (对应主流程步骤6):
    *   在内存索引中找到匹配结果后，立即对找到的动态节点进行LRU状态更新
    *   更新完成后才继续后续流程

2.  **回退扫描后重新搜索命中时** (对应主流程步骤13):
    *   在回退扫描更新索引后，重新搜索找到匹配结果时，再次进行LRU状态更新
    *   这确保了通过回退扫描新发现的动态节点也能正确参与LRU管理

**批量处理**: 当搜索返回多个匹配节点时，系统会对所有匹配的动态节点进行批量LRU更新，将每个节点先删除后重新插入到字典末尾。

**异常处理**: 如果发现动态节点不存在于 `dynamic_nodes_lru` 中，系统会记录警告并自动修复，将该节点添加到字典末尾。

#### 5.2.2 其他LRU操作

*   **添加时**: 新加入动态区的节点被**添加到`dynamic_nodes_lru`末尾**，初始访问时间为创建时间。
*   **淘汰时**: 淘汰操作总是从**字典开头**开始，取出最久未访问的节点进行删除。

## 6. 执行全景

### 6.1 search 方法执行步骤

从接收一个请求到返回结果，`search` 方法严格遵循以下步骤，通过明确的跳转指令来处理不同的执行分支。

**步骤流程描述**:

1.  **输入规范化**: 对用户输入的 `query` 字符串和 `contextual_base_path` 进行两阶段规范化处理（详见 3.2.2 节），确保所有路径操作都在项目根目录范围内进行。

2.  **空查询检查**: 检查规范化后的 `query` 是否为空字符串。如果是，则**跳转到步骤 14**。

3.  **搜索类型判断**: 根据原始 `query` 是否以 `/` 结尾，确定 `is_listing_request` 标记的值（详见 3.2.1 节）。

4.  **在内存索引中搜索**: 根据 `query` 的内容和类型，在内存索引中进行查找（详见 3.3 节）。

5.  **检查初步结果**: 判断上一步是否找到了匹配的节点。
    *   **如果找到结果**: **跳转到步骤 6**。
    *   **如果未找到结果**: **跳转到步骤 7**。

6.  **更新LRU状态并准备返回**: 对于在内存索引中找到的动态节点（`is_core=False`），更新其在 LRU 缓存中的状态，将其标记为最近访问（详见 5.2.1 节）。然后，**跳转到步骤 14**。

7.  **触发回退扫描**: 由于内存索引未命中，启动基于磁盘I/O的动态发现流程（详见 3.4 节）。

8.  **并发控制**: （仅针对文件名全局扫描）获取全局锁以防止并发的昂贵磁盘扫描。如果锁已被其他任务持有，则异步等待其释放，然后**跳转到步骤 11**，直接使用其他任务更新后的索引进行搜索。

9.  **执行磁盘I/O**: 根据未命中的搜索类型，执行相应的磁盘扫描（目录单层扫描或文件名全局扫描）。

10. **更新索引**: 将磁盘扫描发现的新节点增量式地添加到内存索引中。

11. **再次在索引中搜索**: 使用原始的 `query` 和 `contextual_base_path`，重新在更新后的内存索引中执行搜索。

12. **检查最终结果**: 判断重新搜索后是否找到了匹配的节点。
    *   **如果找到结果**: **跳转到步骤 13**。
    *   **如果未找到结果**: **跳转到步骤 14**。

13. **更新LRU状态 (二次)**: 对于新找到的动态节点，更新其 LRU 状态（详见 5.2.1 节）。

14. **返回结果**: 返回最终的搜索结果列表（可能为空），流程结束。
