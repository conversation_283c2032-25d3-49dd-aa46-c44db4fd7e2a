# OneDragon-Agent 开发文档

## 📋 概述

本文档提供了OneDragon-Agent核心模块的完整设计文档，基于对Claude Code的深度分析，设计了从简单对话循环升级为智能任务规划和执行系统的完整方案。

## 📚 模块文档结构

### 核心设计文档

- **[Agent 模块](./modules/agent.md)**: 详细介绍了 Agent 的核心架构，包括主循环、上下文管理、工具执行和子 Agent 机制。
- **[CLI 模块](./modules/cli.md)**: 介绍了命令行界面的实现，包括与 OdaSession 的集成和实时消息显示。
- **[Message 模块](./modules/message.md)**: 定义了系统中使用的消息格式和类型。
- **[Session 模块](./modules/session.md)**: 介绍会话管理，包括输入/输出流处理和与 Agent 的交互。
- **[System Reminder 模块](./modules/system_reminder.md)**: 介绍系统提醒机制，实现上下文注入和状态同步功能。
- **[Event Dispatcher 模块](./modules/event_dispatcher.md)**: 介绍事件分发中心，实现模块间解耦合通信机制。

### 工具文档

- **[工具概述](./modules/tool_all.md)**: 所有可用工具的概述
- **[待办事项工具](./modules/tool_todo.md)**: 待办事项管理工具的详细实现

## 🎯 设计目标

1. **模块化设计**：每个模块都有明确的职责和接口
2. **异步优先**：充分利用 Python 的异步特性
3. **可扩展性**：支持插件机制和功能扩展
4. **实时交互**：支持流式消息处理和实时显示
5. **容错性**：对异常输入进行适当处理，不中断系统运行

## 🧩 核心特性

### System Reminder 机制
System Reminder 模块实现了类似 Claude Code 的系统提醒功能，能够在 Agent 与 LLM 交互时动态注入上下文信息和系统状态提醒。该机制确保 Agent 能够实时感知系统状态变化，同时保持对用户的透明性。

### 事件驱动架构
通过 Event Dispatcher 模块，系统实现了事件驱动的架构，各组件可以通过发布和订阅事件进行解耦合通信。这大大提高了系统的可扩展性和可维护性。

### 模块化工具系统
系统支持模块化的工具扩展，当前实现了待办事项管理工具，未来可以轻松添加更多工具。

## 🚀 快速开始

### 安装依赖
```bash
uv sync
```

### 配置环境
```bash
cp .env.example .env
# 编辑 .env 文件，填入你的 API 密钥等配置
```

### 运行 CLI
```bash
uv run --env-file .env one_dragon_agent
```

## 🧪 测试

### 运行所有测试
```bash
uv run --env-file .env pytest
```

### 运行特定模块测试
```bash
uv run --env-file .env pytest tests/one_dragon_agent/core/system_reminder/
uv run --env-file .env pytest tests/one_dragon_agent/core/event/
```

## 📖 开发指南

### 代码规范
- 遵循 PEP 8 代码规范
- 使用类型提示
- 编写单元测试
- 保持代码简洁和可读性

### 添加新工具
1. 在 `src/one_dragon_agent/core/tool/` 目录下创建新工具类
2. 继承 `OdaTool` 基类并实现 required 方法
3. 在 `OdaSession` 中注册新工具
4. 编写相应的测试用例
5. 更新文档

### 扩展事件系统
1. 在 `src/one_dragon_agent/core/event/event.py` 中定义新事件类型
2. 创建事件处理器处理相应事件
3. 在适当的地方发布事件
4. 编写测试用例
5. 更新文档

## 📈 架构图

```mermaid
graph TD
    A[CLI] --> B[OdaSession]
    B --> C[Agent]
    C --> D[LLM Client]
    C --> E[Tool System]
    E --> F[Todo Tools]
    
    B --> G[Event Dispatcher]
    G --> H[System Reminder]
    
    B --> I[Message Manager]
    I --> J[Message History]
    
    subgraph "核心组件"
        B
        C
        G
        H
        I
    end
    
    subgraph "工具系统"
        E
        F
    end
    
    subgraph "外部依赖"
        D
    end
    
    style A fill:#f9f,stroke:#333
    style B fill:#bbf,stroke:#333
    style C fill:#bfb,stroke:#333
    style D fill:#fbb,stroke:#333
    style E fill:#fbb,stroke:#333
    style G fill:#ffb,stroke:#333
    style H fill:#ffb,stroke:#333
    style I fill:#bff,stroke:#333
```

## 📚 参考资料

- [Claude Code 分析文档](../../reference/analyse_claude_code/)
- [Claude Code 系统提醒机制分析](../../reference/claude_code/system_reminder.md)