# project.md

This file provides guidance to code agents when working with code in this repository.

## Project Overview
- **Python 3.11 intelligent agent framework** with event-driven, plugin-based architecture
- **Package manager**: `uv`
- **Testing**: `pytest` with `pytest-asyncio` for async tests

## Quick Commands

### Development Setup
```bash
uv install                    # Install dependencies
uv install --dev
cp .env.example .env         # Copy env config
```

### Testing
```bash
uv run pytest tests/                    # Run all tests
uv run pytest tests/one_dragon_agent/core/agent/  # Run specific module
```

### Code Quality
```bash
uv run black src/ tests/     # Format code
uv run mypy src/             # Type checking
uv run flake8 src/           # Linting
```

## Architecture
The project follows a modular, event-driven architecture. The core components are in `one_dragon_agent` package:

### Core Components

- **`CLI` (`cli.app`)**: The primary user interface for the agent, built with the Textual framework. It is responsible for capturing user input, sending it to the `OdaSession`, and subscribing to events from the `EventDispatcher` to display real-time responses and updates.
- **`OdaSession` (`core.sys.session`)**: The central orchestrator for a user session. It is a long-lived object that initializes and holds all core services like `ToolManager`, `EventDispatcher`, and `MessageManager`.
- **`Agent` (`core.agent.agent`)**: A short-lived worker created by `OdaSession` for each user command. It encapsulates the main execution loop, which involves calling the LLM and delegating tool execution to the `ToolManager`.
- **`ToolManager` (`core.tool.tool_manager`)**: A centralized engine for tool management. It is responsible for discovering, creating, storing, and executing all available tools. The `Agent` interacts with this manager instead of individual tools directly.
- **`EventDispatcher` (`core.event.dispatcher`)**: A system-wide event bus that enables decoupled communication between different modules. For example, the `Agent` publishes text streaming events, and the `CLI` subscribes to them for real-time display.

### Execution Flow
The following is an example flow for a single user command, emphasizing the event-driven communication:

1.  The **CLI** captures user input and sends it as a message to the `OdaSession`.
2.  `OdaSession` creates a new short-lived `Agent` instance to handle this specific message.
3.  The `Agent` enters its main execution loop, preparing and sending the conversation history to the **LLM**.
4.  If the LLM's response includes tool calls, the `Agent` delegates them to the `ToolManager`. The manager executes the tools, and the results are added to the message history for the next iteration of the loop.
5.  If the LLM generates a text response, the `Agent` publishes a series of events (e.g., `AgentTextStreamStartEvent`, `AgentTextStreamContentEvent`) to the `EventDispatcher`.
6.  The CLI has event handlers subscribed to these events. Upon receiving an event, the corresponding handler updates the UI in real-time (e.g., streaming the AI's text response).
7.  The loop continues until the LLM produces a final text response without any further tool calls.

For a detailed explanation of each module and the overall system design, please refer to the documentation located in `docs/develop/modules/`.

## Agent Operating Principles
- **File encoding**: Always use UTF-8 format when writing files to ensure proper character encoding support.
- **Keep Test Synchronized**: After modifying any module, you **must** update its test file in `tests/`. Ensure that modified code is under coverage and all tests pass.
- **Keep Documentation Synchronized**: After modifying any module, you **must** update its corresponding documentation file in `docs/develop/modules/`. Ensure the documentation accurately reflects the changes.
- **Git Workflow**: Your responsibility is to write and modify the code based on the user's request. Do not perform any `git` operations like `commit` or `push`. The user will handle all version control actions.

## Markdown Guidelines
- **Avoid embedding code**: Use text descriptions or mermaid diagrams, avoid embedding actual code
- **Mermaid diagrams**: Use standard flowchart/state diagram syntax, avoid loop constructs and "loop" as variable name
  - **Node Text Quoting**: Node text must be wrapped in double quotes (`"`) to avoid parsing errors. For example, use `I["User Interface (CLI)"]` instead of `I[User Interface (CLI)]`.
- **Language**: Use Chinese to write document.

## Coding Style
- **Async-first**: All operations use async/await
- **Docstrings**: All functions must have Google-style docstrings, written in English.
- **Type Hinting**: All variables and function signatures must include type hints.
- **Built-in Generics**: Use built-in generic types (`list`, `dict`) instead of imports from the `typing` module (`List`, `Dict`).
  - **Correct**: `my_list: list[str] = []`
  - **Incorrect**: 
    ```python
    from typing import List
    my_list: List[str] = []
    ```
- **Relative Imports**: Within the `one_dragon_agent` package, use relative imports.
  - **Example**: If you are in `one_dragon_agent/core/sys/session.py` and need to import `Message` from `one_dragon_agent/core/sys/message.py`, use `from .message import Message`.
- **Explicit Parent Class Constructor Calls**:
    - **Rule**: In all subclass `__init__` methods, you **must** call the parent class constructor directly (e.g., `ParentClass.__init__(self, ...)`). You **must not** use `super().__init__()`.
    - **Reason**: This is a mandatory project-specific coding standard to ensure code clarity and explicitness.
    - **Examples**:
      ```python
      # CORRECT:
      class MySubclass(MyBaseClass):
          def __init__(self, name, value):
              # Directly call the parent's __init__
              MyBaseClass.__init__(self, name)
              self.value = value
      ```
      ```python
      # WRONG:
      class MySubclass(MyBaseClass):
          def __init__(self, name, value):
              # Do not use super() for initialization
              super().__init__(name)
              self.value = value
      ```
- **Explicit Data Structure**: You should define a dataclass object, instead of using dict.

## Testing Guidelines
- **No Test Packages**: Must not create python package in test directories.
- **Test Classes and Fixtures**: Test files must use a test class (prefixed with `Test`) to organize related test methods. You must use `pytest.fixture` to manage test dependencies and state (such as object instance creation and teardown) to improve code reusability and maintainability.
- **Import Convention**: Because the project uses a `src-layout`, import paths in test files must not include the `src` directory.
  - **Correct**: `from one_dragon_agent.core.agent import Agent`
  - **Incorrect**: `from src.one_dragon_agent.core.agent import Agent`
- **Single-Method Test Files**: Each Python test file should focus exclusively on testing a single method across various scenarios.
  - **Example**: To test the `src/one_dragon_agent/core/agent/agent.py`, create a folder `tests/one_dragon_agent/core/agent/agent/` to store all test files.
  - **Example**: To test the `execute_main_loop` method of `agent.py`, create a file named `test_execute_main_loop.py` in `agent` folder. This file should contain all test cases specifically for the `execute_main_loop` method.
- **Test File Path Convention**: The directory path for a test file must mirror the package path of the module under test.
  - **Example**: For the class `Agent` in the module `one_dragon_agent.core.agent.agent`, its test files should be placed in the `tests/one_dragon_agent/core/agent/agent/` directory. The test file itself should be named after the specific method being tested (e.g., `test_execute_main_loop.py`).
- **Async Test Timeout**: All asynchronous test methods must include a timeout setting (e.g., using `pytest.mark.timeout(seconds)`) to prevent tests from hanging indefinitely.
- **Temporary File**: Use `.temp` directory under current working directory to store temp files.
- **To be honest**: When test cases fail and you dont know why or how to fix it, you should stop to ask what to do next.
