### **最终版** 设计文档：交互式文件处理增强

**开发进度**: 
- ✅ **阶段一：后端 - WorkspaceIndex 集成** (已完成)
- ⏳ **阶段二：前端 - CLI 交互实现** (待开发)

#### 1. 目标

当前文件引用功能依赖用户手动输入完整路径。本次升级旨在将其改造为主动、实时的交互式体验，允许用户在 CLI 中通过 `@` 符号触发文件搜索、选择，并以标签（Pill）的形式在输入框中管理文件，最终无缝地集成到发送给 `OdaSession` 的消息中。此功能将由全新的 **`WorkspaceIndex`** 服务提供后端支持，以实现高性能和实时性。

#### 2. 核心设计思想

我们将严格遵循项目现有的**事件驱动**和**模块化**架构。

1.  **职责分离**:
    *   **`CLI` (前端)**: 负责所有 UI 交互，包括监听 `@` 输入、显示搜索结果、管理文件标签（Pills）以及在发送前将标签格式化为 `<oda-at-file>` 格式。
    *   **`OdaSession` (后端)**: 作为前端和后端服务的协调者。它将接收 CLI 的搜索请求，并将其转发给 `WorkspaceIndex` 服务。
    *   **`WorkspaceIndex` (核心服务)**: 负责提供高性能、实时的文件索引和搜索能力。它是文件搜索功能的唯一后端实现。

2.  **事件驱动通信**:
    *   CLI 和 `OdaSession` 之间通过 `EventDispatcher` 进行异步通信。我们将定义新的事件类型用于文件搜索请求和结果响应，从而实现低耦合。`OdaSession` 和 `WorkspaceIndex` 之间则通过直接的方法调用进行交互。

#### 3. 模块职责划分 (已更新)

1.  **`WorkspaceIndex` (`one_dragon_agent.core.workspace.workspace_index`)**
    *   **核心职责**:
        *   在后台独立维护一个高性能、实时同步的全项目文件索引。
        *   提供一个 `async def search(query, contextual_base_path)` 方法作为唯一的搜索入口。
        *   内部实现两级索引（静态核心区、动态发现区）、回退扫描、实时事件更新和内存管理（LRU）。
        *   处理所有文件系统交互和路径安全校验。

2.  **`OdaSession` (`one_dragon_agent.core.sys.session`)**
    *   **新增职责**:
        *   在初始化时，创建并持有一个 `WorkspaceIndex` 实例。
        *   监听来自前端的 `FileSearchRequestEvent` 事件。
        *   接收到请求后，调用 `self.workspace_index.search()` 方法执行搜索。
        *   将 `WorkspaceIndex` 返回的结果（`list[IndexNode]`）格式化后，通过 `FileSearchResultsEvent` 发回给前端。
        *   在响应事件中**回传请求ID**，用于处理并发。
    *   **移除的职责**:
        *   不再自行实现文件搜索逻辑。
        *   不再管理文件搜索缓存 (`FileSearchCache` 已被 `WorkspaceIndex` 的高级内存管理替代)。
    *   **不变的职责**:
        *   处理 `<oda-at-file>` 标签的逻辑 (`_process_file_references`)。

3.  **`CLI` (`one_dragon_agent.cli.app`)**
    *   **职责 (与旧版一致，但更清晰)**:
        *   **光标感知输入监控**: 实时监控 `@` 输入，提取从 `@` 到光标的查询字符串。
        *   **并发请求管理**: 为每个搜索请求生成唯一ID，并只处理最新请求的响应。
        *   **上下文感知搜索与键盘控制**: 管理当前的基础路径，响应上/下、Tab、Enter 等按键，实现结果导航、目录探索和最终选择。
        *   **UI 渲染与状态**: 使用浮动层展示搜索结果，管理文件标签（Pill）的插入和替换。

4.  **`EventDispatcher` (`one_dragon_agent.core.event.dispatcher`)**
    *   **新增事件类型 (已更新)**:
        *   `FileSearchRequestEvent`: 包含 `request_id: str`, `query: str`, `contextual_base_path: str`。
        *   `FileSearchResultsEvent`: 包含 `request_id: str`, `results: list[dict]`。结果字典将由 `IndexNode` 转换而来，例如 `{'path': str, 'is_dir': bool}`。

#### 4. 详细实现计划 (已更新)

**阶段一：后端 - `WorkspaceIndex` 集成**

1.  **实现 `WorkspaceIndex` 服务 (`one_dragon_agent/core/workspace/workspace_index.py`)**: ✅ **已完成**
    *   实现 `IndexNode` 和 `IndexData` 核心数据结构。
    *   实现包含 `path_trie` 和 `name_trie` 的高效索引结构。
    *   实现 `search` 方法，包含自适应搜索策略（目录列出、路径前缀、名称前缀）。
    *   实现回退扫描机制（目录单层扫描、文件名全局扫描）。
    *   通过 `watchdog` 实现事件驱动的实时索引更新。
    *   实现基于 `OrderedDict` 的 LRU 内存淘汰策略。
    *   包含完整的测试覆盖，所有测试通过。

2.  **增强 `OdaSession` 以集成 `WorkspaceIndex`**: ✅ **已完成**
    *   在 `OdaSession.__init__` 中，实例化 `WorkspaceIndex`：`self.workspace_index = WorkspaceIndex(root_path=self.working_dir)`.
    *   在 `OdaSession.start` 中，异步初始化索引：`await self.workspace_index.initialize()`.
    *   订阅 `FileSearchRequestEvent` 事件，并绑定到新的处理方法 `_handle_file_search_request`。
    *   实现 `async def _handle_file_search_request(self, event: FileSearchRequestEvent)`:
        *   调用 `results: list[IndexNode] = await self.workspace_index.search(query=event.query, contextual_base_path=event.contextual_base_path)`。
        *   将 `IndexNode` 列表转换为字典列表。
        *   发布 `FileSearchResultsEvent`，回传 `request_id` 和格式化后的结果。
    *   移除旧的 `_execute_file_search` 方法和对 `FileSearchCache` 的所有引用。

**阶段二：前端 - `CLI` 交互实现 (无变化)**

*   前端的实现计划保持不变。其交互逻辑不关心后端是 `OdaSession` 直接搜索还是通过 `WorkspaceIndex` 搜索，因为它只通过事件与后端通信。

#### 5. CLI 交互流程 (Mermaid 图 - 已更新，集成 WorkspaceIndex)

```mermaid
sequenceDiagram
    participant User
    participant CLI
    participant EventDispatcher
    participant OdaSession
    participant WorkspaceIndex

    User->>CLI: 输入 `@src`
    CLI->>CLI: 进入搜索模式, query="src"
    CLI->>EventDispatcher: publish(FileSearchRequestEvent(req_id="req1", query="src", contextual_base_path="."))
    
    EventDispatcher->>OdaSession: deliver(req1)
    OdaSession->>WorkspaceIndex: search(query="src", contextual_base_path=".")
    
    alt 内存索引命中
        WorkspaceIndex-->>OdaSession: 返回结果: [IndexNode(path="src", is_dir=True)]
    else 索引未命中 (触发回退扫描)
        WorkspaceIndex->>WorkspaceIndex: 执行磁盘扫描并更新索引
        WorkspaceIndex-->>OdaSession: 返回结果: [IndexNode(path="src", is_dir=True)]
    end

    OdaSession->>OdaSession: 格式化结果
    OdaSession->>EventDispatcher: publish(FileSearchResultsEvent(req_id="req1", results=[{'path': 'src', 'is_dir': True}]))
    EventDispatcher->>CLI: deliver(res1)
    CLI->>User: 显示结果列表<br>📁 src/

    User->>CLI: 按 Down 键, 选中 "src/"
    User->>CLI: 按 Tab 键
    CLI->>CLI: 识别为目录探索<br>更新 base_path="src"<br>更新输入框为 "@src/"
    CLI->>EventDispatcher: publish(FileSearchRequestEvent(req_id="req2", query="/", contextual_base_path="src"))
    
    EventDispatcher->>OdaSession: deliver(req2)
    OdaSession->>WorkspaceIndex: search(query="/", contextual_base_path="src")
    WorkspaceIndex-->>OdaSession: 返回 src/ 下的内容
    OdaSession->>EventDispatcher: publish(FileSearchResultsEvent(req_id="req2", ...))
    EventDispatcher->>CLI: deliver(res2)
    CLI->>User: 显示 src/ 目录下的内容<br>📁 one_dragon_agent/

    User->>CLI: 按 Down 键, 选中 "one_dragon_agent/"
    User->>CLI: 按 Enter 键
    CLI->>CLI: 识别为最终选择<br>退出搜索模式<br>将 "@src/one_dragon_agent/" 替换为 Pill
    CLI->>User: 输入框显示: "[Pill: src/one_dragon_agent/]"
```

#### 6. `WorkspaceIndex` 的优势

用 `WorkspaceIndex` 替代简单的缓存机制，带来了显著的架构优势：

1.  **高性能与实时性**: 通过优化的Trie索引和`watchdog`事件监听，搜索响应极快，且能实时同步文件系统的变更。
2.  **智能与健壮**:
    *   **两级索引**: 区分核心文件和动态发现文件，保证高频访问的性能和内存效率。
    *   **回退扫描**: 即使所需文件不在初始索引中，也能通过智能回退扫描找到，保证了功能的健壮性。
    *   **上下文感知**: 搜索结果能根据用户当前路径进行优化，提升相关性。
3.  **资源高效**: LRU内存管理策略能有效控制动态索引的内存占用，防止内存泄漏。
4.  **高内聚与低耦合**: `WorkspaceIndex` 是一个完全独立的、高内聚的服务。`OdaSession` 只需作为客户端调用它，职责更清晰，符合单一职责原则。

#### 7. 总结

该设计方案通过引入 `WorkspaceIndex` 服务，从根本上提升了文件搜索功能的性能、实时性和健壮性。新的架构职责划分更清晰，`OdaSession` 作为协调者，`WorkspaceIndex` 作为专业的文件索引服务，`CLI` 专注于UI交互。这种模块化的设计不仅解决了当前的需求，也为未来功能的扩展（如支持更复杂的查询、多工作区等）奠定了坚实的基础。


