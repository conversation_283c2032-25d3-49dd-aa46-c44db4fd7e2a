# Claude Code 工具参考文档

此目录包含构成 Claude Code 开发环境的 13 个核心工具的详细文档。每个工具在开发工作流程中都有特定用途，从文件操作到网络搜索。

## 工具概览

### 文件系统操作

#### **Bash 工具** (`bash-tool.txt`)
**作用**：在持久会话中执行 shell 命令
- **使用场景**：构建项目、运行测试、Git 操作、包管理
- **核心特性**：命令安全检查、超时控制、持久 shell 状态
- **安全性**：执行前多层安全验证

#### **Read 工具** (`read-tool.txt`)
**作用**：读取并显示文件内容（带行号）
- **使用场景**：查看源代码、配置文件、文档
- **核心特性**：编码检测、行号输出、大文件处理
- **输出**：上下文丰富的文件内容分析

#### **Write 工具** (`write-tool.txt`)
**作用**：创建新文件或完全重写现有文件
- **使用场景**：创建源文件、生成配置、编写文档
- **核心特性**：自动创建目录、原子写入、编码处理
- **安全性**：覆盖前必须读取现有文件

#### **Edit 工具** (`edit-tool.txt`)
**作用**：对现有文件进行精确修改
- **使用场景**：修复 bug、添加功能、更新配置
- **核心特性**：搜索/替换块、保持格式、精准修改
- **精确性**：精确行匹配，最小化干扰

#### **MultiEdit 工具** (`multiedit-tool.txt`)
**作用**：跨多个文件同时应用协调变更
- **使用场景**：重构、API 更新、跨文件一致性变更
- **核心特性**：批量操作、一致格式、回滚能力

### 搜索和发现

#### **Glob 工具** (`glob-tool.txt`)
**作用**：使用模式匹配查找文件
- **使用场景**：按扩展名发现文件、定位特定文件类型
- **模式**：`*.js`、`src/**/*.ts`、`tests/**/test_*.py`
- **集成**：与其他工具配合进行定向操作

#### **Grep 工具** (`grep-tool.txt`)
**作用**：使用正则表达式搜索文件内容
- **使用场景**：查找代码模式、定位特定函数、搜索日志
- **特性**：上下文丰富结果、正则支持、文件过滤
- **输出**：匹配行及周围上下文

#### **LS 工具** (`ls-tool.txt`)
**作用**：列出目录内容和结构
- **使用场景**：探索项目结构、理解文件组织
- **特性**：递归列表、文件类型识别、大小信息

### 任务管理

#### **Task 工具** (`task-tool.txt`)
**作用**：复杂多步操作的智能代理
- **使用场景**：开放式搜索、架构分析、复杂重构
- **特性**：工具编排、上下文优化、智能路由
- **优势**：减少上下文使用、自动处理复杂工作流

#### **Todo 工具** (`todo-tools.txt`)
**作用**：跟踪工作进度的任务管理系统
- **组件**：TodoWrite（更新任务）、TodoRead（查看任务）
- **特性**：状态跟踪（待处理/进行中/已完成）、优先级级别
- **集成**：与所有其他工具配合进行工作流管理

### 网络操作

#### **WebFetch 工具** (`webfetch-tool.txt`)
**作用**：获取和处理网络内容
- **使用场景**：检索文档、API 参考、教程
- **特性**：HTML 转 Markdown、15 分钟缓存、AI 处理
- **输出**：干净可读内容，带来源引用

#### **WebSearch 工具** (`websearch-tool.txt`)
**作用**：搜索互联网获取实时信息
- **使用场景**：查找最新文档、研究解决方案、验证信息
- **特性**：域名过滤、仅限美国、结构化结果
- **集成**：提供 AI 知识截止日期之外的最新信息

### 专用工具

#### **NotebookRead 工具** (`notebook-read-tool.txt`)
**作用**：读取和分析 Jupyter 笔记本文件
- **使用场景**：检查数据科学工作流、审查基于笔记本的项目
- **特性**：单元格提取、输出分析、代码/Markdown 分离

## 架构模式

### 安全第一设计
- **写入前读取验证** 用于现有文件
- **绝对路径要求** 用于文件操作
- **全面权限验证**
- **多层安全检查**

### 智能编排
- **Task 工具协调** 复杂多工具工作流
- **上下文优化** 减少 token 使用
- **基于任务复杂度的智能工具选择**
- **错误恢复** 和替代策略

### 性能优化
- **缓存机制**（WebFetch 中 15 分钟缓存）
- **高效搜索策略** 带上下文压缩
- **安全并发操作支持**
- **实时与缓存数据** 考虑

## 使用指南

### 简单操作
- 使用 **Glob** + **Read** 进行文件发现和检查
- 使用 **Grep** 进行基于内容的搜索
- 使用 **Edit** 进行定向文件修改

### 复杂工作流
- 使用 **Task** 进行需要工具协调的多步操作
- 使用 **Todo** 工具跟踪复杂项目进度
- 使用 **MultiEdit** 进行跨文件协调变更

### 研究和文档
- 使用 **WebSearch** 查找最新信息
- 使用 **WebFetch** 检索特定文档
- 使用 **NotebookRead** 分析数据科学项目

## 安全特性

- **Bash 工具中的命令注入预防**
- **文件操作中的路径遍历保护**
- **网络搜索的域名过滤**
- **所有工具的敏感文件保护**
- **所有操作的审计日志**

此工具集提供了全面的开发环境，在强大功能与安全性之间取得平衡，实现高效软件开发的同时保持安全性和可靠性标准。