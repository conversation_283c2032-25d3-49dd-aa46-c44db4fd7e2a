# RealtimeSteeringSystem 开发设计方案

## 1. 概述 (Overview)

`RealtimeSteeringSystem` 是一个为**命令行界面（CLI）**设计的核心交互引擎。它并非 CLI 本身，而是驱动 CLI 实现与 AI Agent 实时、非阻塞交互的后端系统。

系统的核心目标是打破传统 CLI 的“请求-响应”同步模式。它通过处理来自标准输入（`stdin`）的连续指令流，允许用户在 Agent 执行长时间任务的过程中，随时输入新的指令进行“实时引导 (Real-time Steering)”，从而动态调整任务方向或中断当前操作。

### 核心设计原则

- **异步非阻塞 (Asynchronous & Non-Blocking)**: 系统的所有 I/O 操作和任务处理都基于异步模型，确保用户输入和系统响应不会相互阻塞。
- **流式处理 (Streaming)**: 无论是输入、Agent 思考过程还是最终输出，都以数据流的形式处理，实现低延迟和高效的内存利用。
- **解耦设计 (Decoupled Design)**: 将消息的接收、解析、排队和执行过程解耦，允许各组件独立、并发地工作。
- **可中断性 (Interruptible)**: Agent 的核心任务循环必须是可中断的，以便系统能够响应更高优先级的实时指令。

## 2. 流程示例说明

以下将通过一个两轮对话的例子，详细说明一个指令从 CLI 发出后，在 `RealtimeSteeringSystem` 内部的完整流转过程，并重点解释**多轮对话上下文是如何维护的**。

### 初始状态

系统启动时，会创建一个唯一的 `RealtimeSteeringSystem` 实例，该实例内部包含一个核心的 `StreamingProcessor`（源自 `kq5`）对象。这个 `StreamingProcessor` 对象将**贯穿整个应用的生命周期**，并持有一个关键的内部状态：`messageHistory` 数组，用于存储完整的对话历史。

---

### 第一轮对话: "你好"

1.  **用户输入 (CLI `stdin`)**: 用户输入 `{"type":"user", "message":{"role":"user", "content":"你好"}}` 并回车。

2.  **流式解析 (`StreamingMessageParser`)**: `stdin` 的原始文本流被解析成一个结构化的 `UserMessage` 对象。

3.  **指令入队 (`StreamingProcessor`)**:
    - `StreamingProcessor` 接收到 `UserMessage`，将其封装成一个 `Command` 对象，并推入内部的 `commandQueue`。
    - 由于此时 Agent 空闲，`StreamingProcessor` 立即开始处理该命令。

4.  **任务执行与上下文创建 (`AgentCore`)**:
    - `StreamingProcessor` 从队列中取出“你好”这个指令。
    - **【关键点】** 它**为本次任务动态创建一个全新的 `AgentContext`**。这个上下文包含了执行此任务所需的所有资源，以及一个全新的 `AbortController`，用于独立控制这一轮对话的中断。
    - `StreamingProcessor` 调用 `AgentCore` (`nO`)，并将**当前的 `messageHistory`（此时为空或只包含初始系统提示）** 和新指令“你好”一同传入。

5.  **结果输出与历史维护**:
    - `AgentCore` 执行后，通过 `yield` 流式地返回思考过程和最终回复（例如：“你好，有什么可以帮您？”）。
    - `StreamingProcessor` 捕获到这些返回的结果，并做两件事：
        a.  将结果实时推入 `outputQueue`，供外部渲染。
        b.  **【关键点】** 将用户的输入“你好”和 Agent 的完整回复，**追加（push）到它自己持有的 `messageHistory` 数组中**。

6.  **界面渲染 (CLI `stdout`)**: CLI 监听到 `outputQueue` 中的新消息，并将其打印到屏幕上。

**第一轮结束后，`StreamingProcessor` 内部的 `messageHistory` 已经包含了这次完整的交互记录。**

---

### 第二轮对话: "帮我查一下天气"

1.  **用户输入与解析**: 用户输入新的指令，同样被解析为 `UserMessage` 对象。

2.  **指令入队**: 新的 `Command` 被推入 `commandQueue`。

3.  **任务执行与上下文传递**:
    - `StreamingProcessor` 从队列中取出“帮我查一下天气”这个新指令。
    - **【关键点】** 它**再次为这新一轮任务动态创建另一个全新的 `AgentContext`**。
    - `StreamingProcessor` 再次调用 `AgentCore` (`nO`)，但这一次，它传入的是**已经包含第一轮对话内容的 `messageHistory`** 和新指令“帮我查一下天气”。

4.  **具备上下文的 Agent 处理**:
    - `AgentCore` 接收到了完整的对话历史和新指令。由于它“知道”上一轮的对话内容，因此能够理解这是一个连续的对话。
    - Agent 开始执行任务，并流式返回结果（例如：“好的，请问您想查询哪个城市的天气？”）。

5.  **结果输出与历史维护 (循环)**:
    - `StreamingProcessor` 再次将新一轮的结果推送到 `outputQueue` 并**继续追加到 `messageHistory` 数组中**。

**至此，`messageHistory` 包含了前后两轮的所有对话记录，为第三轮、第四轮...的连续对话打下了基础。**

这个设计通过**持久化的 `StreamingProcessor` 实例**和其内部**不断累积的 `messageHistory`** 实现了多轮对话的上下文保持，同时通过**为每一轮任务动态创建 `AgentContext`** 保证了每个任务执行环境的独立和可控。

## 3. 系统架构 (System Architecture)

系统由多个核心组件构成，它们通过异步消息流协同工作，形成一个完整的数据处理管道。

- **StreamingMessageParser (源自 `g2A`)**: 负责将原始输入流（如 `stdin`）解析为结构化的、经过验证的消息。
- **StreamingProcessor (源自 `kq5`)**: 系统的中央调度引擎。它管理一个内部命令队列，并协调 Agent 的执行。
- **AsyncMessageQueue (源自 `h2A`)**: 一个异步消息队列实现，用作系统的输出流，允许外部消费者以流式方式获取结果。
- **AgentCore (源自 `nO`)**: Agent 的主执行循环，被设计为可中断的异步生成器 (`async generator`)。
- **AbortController**: 标准的 Web API，用于创建和管理中断信号，是实现实时引导的关键。


## 4. 关键组件设计

### 4.1 流式消息解析器 (StreamingMessageParser)

- **职责**:
    1.  监听原始输入流（如 `process.stdin`）。
    2.  将字节流按换行符 `\n` 分割成独立的行。
    3.  将每一行解析为 JSON 对象。
    4.  对解析后的对象进行严格的结构验证（例如，`type` 和 `role` 必须为 `"user"`）。
- **错误处理**: 采用“快速失败 (Fail-Fast)”策略。任何解析或验证失败都会立即终止进程，防止非法数据污染下游系统。
- **输出**: 一个结构化的 `UserMessage` 对象的异步流 (`AsyncIterable<UserMessage>`)。

### 4.2 流式处理引擎 (StreamingProcessor)

这是系统的核心，内部包含两个并发运行的协程。

- **内部状态**:
    - `commandQueue: Command[]`: 一个先进先出 (FIFO) 的命令队列，用于缓冲用户指令。
    - `isExecuting: boolean`: 一个互斥锁，确保同一时间只有一个 Agent 任务在执行。
    - `outputQueue: AsyncMessageQueue`: 用于向外部推送结果的输出流。

- **输入处理协程 (`processInput`)**:
    - **触发**: 系统启动时自动运行。
    - **逻辑**:
        1.  异步迭代 `StreamingMessageParser` 的输出。
        2.  每接收到一个新消息，就将其封装成一个命令并推入 `commandQueue`。
        3.  检查 `isExecuting` 标志。如果为 `false`，则立即启动**命令执行协程**。

- **命令执行协程 (`executeCommands`)**:
    - **触发**: 由**输入处理协程**在队列非空且无任务执行时启动。
    - **逻辑**:
        1.  设置 `isExecuting = true`。
        2.  循环从 `commandQueue` 中取出命令。
        3.  为每个命令创建一个新的 `AbortController`。
        4.  调用 `AgentCore` 的主循环 (`nO`)，并传入命令内容和 `AbortSignal`。
        5.  异步迭代 `AgentCore` 返回的结果，并将每个结果实时推入 `outputQueue`。
        6.  当队列为空时，循环结束，在 `finally` 块中重置 `isExecuting = false`。

### 4.3 Agent主循环 (AgentCore)

- **实现**: 必须是一个**异步生成器 (`async function*`)**。
- **职责**:
    1.  接收用户指令和 `AbortSignal`。
    2.  执行复杂的思考、工具调用等任务。
    3.  在长时间操作的循环中，定期检查 `abortSignal.aborted` 状态。如果为 `true`，则应立即清理资源并 `return`。
    4.  通过 `yield` 关键字，以流式方式逐步返回思考过程、中间结果和最终答案。

### 4.4 中断控制器 (AbortController)

- **集成**: `StreamingProcessor` 在处理新指令时，可以决定是否要中断当前正在执行的任务。
- **流程**:
    1.  当一个高优先级的“引导”指令进入队列时，`StreamingProcessor` 可以调用与当前执行任务关联的 `AbortController` 的 `abort()` 方法。
    2.  `AgentCore` (`nO`) 内部的 `abortSignal` 状态变为 `aborted`。
    3.  `AgentCore` 检测到信号，中断当前操作，执行清理逻辑并返回。
    4.  `executeCommands` 协程捕获到中断，结束当前命令的处理，并开始处理队列中的下一个命令（即新的引导指令）。

## 5. 数据流与交互流程

1.  **启动**: `RealtimeSteeringSystem` 启动，返回一个 `outputStream` 给调用方。内部的 `processInput` 协程开始监听。
2.  **用户输入**: 用户在 CLI 输入第一个指令。
3.  **解析**: `StreamingMessageParser` 将其解析为合法的 `UserMessage`。
4.  **入队**: `StreamingProcessor` 将消息放入 `commandQueue`，并启动 `executeCommands`。
5.  **执行**: `executeCommands` 从队列取出指令，调用 `AgentCore`。
6.  **流式输出**: `AgentCore` 开始 `yield` 结果，这些结果被实时推送到 `outputStream`，UI 层可以立即渲染。
7.  **实时引导**: 在 `AgentCore` 仍在执行时，用户输入第二个指令。
8.  **并发处理**: `processInput` 协程（仍在运行）接收并处理新指令，将其放入 `commandQueue`。
9.  **中断决策**: `StreamingProcessor` 根据业务逻辑（例如，新指令是紧急停止命令）决定是否中断当前任务。如果需要，它会调用 `abort()`。
10. **任务切换**: 被中断的 `AgentCore` 退出，`executeCommands` 协程从队列中取出新的引导指令，并开始一个新的 `AgentCore` 执行周期。

## 6. 错误处理

- **解析层**: 任何解析错误都会导致进程立即退出，保证系统纯净。
- **执行层**: `executeCommands` 和 `AgentCore` 中的 `try...catch` 块负责捕获运行时错误。
- **传播**: 错误信息会被封装并推送到 `outputStream`，由消费者处理。

## 7. 性能与可扩展性

- **性能**: 流式处理和异步 I/O 确保了低延迟和高吞吐量。内存占用低，因为数据不会被完全加载到内存中。
- **可扩展性**: 组件化的设计使得可以轻松替换或增强任何部分，例如，可以接入不同的 Agent 实现，或从 `stdin` 以外的数据源（如 WebSocket）读取输入。