# Todo List 功能详细设计方案

## 1. 综述

本设计方案基于对 Claude Code Todo 工具的深度逆向分析，旨在完整阐述 Todo List 功能的设计与实现。Todo List 是 Claude Code 智能代理的核心组件之一，它不仅是任务跟踪工具，更是代理进行复杂任务规划、状态管理和工作流控制的基石。通过提供结构化的任务管理能力，Todo List 确保了代理在执行长周期、多步骤任务时的一致性、可靠性和透明度。

## 2. 核心设计理念

- **工作流驱动**: Todo List 深度集成于 Agent 的工作流程中，是其思考、规划和执行过程的外部化体现。
- **状态持久化**: 任务列表在整个会话（Session）中持久存在，确保了跨越多轮交互的任务连续性。
- **Agent 隔离**: 每个 Agent 拥有独立的任务列表，实现了多 Agent 环境下的数据隔离，保证了并行任务的独立性。
- **用户透明**: 通过频繁更新和读取 Todo List，向用户清晰地展示当前的工作进展、下一步计划和已完成的任务。
- **主动式管理**: 强烈建议 Agent 主动、频繁地使用此工具，将其作为任务执行的标准操作流程（SOP），而非被动记录工具。

## 3. 数据结构 (Schema)

Todo List 的数据结构通过严格的 Schema 定义，确保了数据的一致性和有效性。

### 3.1 单个任务项 (Todo Item)

每个任务项包含内容、状态、优先级和唯一ID。

```typescript
// 单个任务项结构 (DL6)
interface TodoItem {
  id: string; // 任务的唯一标识符
  content: string; // 任务的具体内容，必须非空
  status: "pending" | "in_progress" | "completed"; // 任务状态
  priority: "high" | "medium" | "low"; // 任务优先级
}
```

### 3.2 任务列表 (Todo List)

任务列表是多个任务项组成的数组。

```typescript
// 任务列表结构 (GJ1)
type TodoList = TodoItem[];
```

### 3.3 任务状态与优先级

- **任务状态 (Status)**:
  - `pending`: 任务已创建但尚未开始。
  - `in_progress`: 任务正在进行中。Agent 应确保同一时间只有一个任务处于此状态。
  - `completed`: 任务已成功完成。

- **任务优先级 (Priority)**:
  - `high`: 高优先级，需要优先处理。
  - `medium`: 中等优先级。
  - `low`: 低优先级。

## 4. 数据存储机制

Todo List 的数据以 JSON 文件的形式持久化存储在本地文件系统，实现了会话级别的数据隔离。

### 4.1 存储路径

存储路径的设计保证了每个会话（Session）和每个代理（Agent）都有其独立的存储文件。

- **配置目录**: `~/.claude/` (或由 `CLAUDE_CONFIG_DIR` 环境变量指定)
- **Todo 目录**: `~/.claude/todos/`
- **文件命名**: `${sessionId}-agent-${agentId}.json`

这种设计确保了不同会话和不同 Agent 之间的任务数据互不干扰。

### 4.2 文件 I/O 操作

- **读取 (`La0`)**: 从指定路径读取 JSON 文件，并使用 Schema (`GJ1`) 进行严格的类型和数据验证。如果文件不存在或解析/验证失败，则返回一个空数组 `[]`，并记录错误，保证了系统的健壮性。
- **写入 (`Ra0`)**: 将任务列表（格式化的 JSON 字符串）写入指定文件。写入操作是原子的（`writeFileSync`），并使用 `flush: true` 选项确保数据立即写入磁盘，防止数据丢失。

## 5. 工具定义 (Tools)

Todo List 功能主要由 `TodoWrite` 和 `TodoRead` 两个工具实现。

### 5.1 TodoWrite (更新任务)

该工具用于创建、更新或删除任务。其核心逻辑是**接收一个完整的任务列表并覆盖旧的列表**。

- **工具名称**: `TodoWrite`
- **用户界面名称**: `Update Todos`
- **并发安全**: `否 (isConcurrencySafe: false)`。写操作不是并发安全的，需要串行执行以避免竞态条件。
- **只读**: `否 (isReadOnly: false)`。
- **输入 Schema**:
  ```json
  {
    "todos": "The updated todo list" // 包含所有任务的完整列表
  }
  ```
- **核心逻辑**:
  1. 接收新的 `todos` 列表。
  2. （可选）读取旧的 `todos` 列表用于返回信息。
  3. 将新的 `todos` 列表写入该 Agent 的特定 JSON 文件中，覆盖原有内容。
  4. 返回成功信息。

### 5.2 TodoRead (读取任务)

该工具用于查询当前的任务列表。

- **工具名称**: `TodoRead`
- **用户界面名称**: `Read Todos`
- **并发安全**: `是 (isConcurrencySafe: true)`。读操作是并发安全的，可以并行执行。
- **只读**: `是 (isReadOnly: true)`。
- **输入 Schema**: 无输入参数。
- **核心逻辑**:
  1. 读取该 Agent 的特定 JSON 文件。
  2. 解析并验证数据。
  3. 返回完整的任务列表。

## 6. 核心业务逻辑

### 6.1 任务排序机制

在 UI 或需要展示时，任务列表会根据一个双重排序算法 (`YJ1`) 进行排序，确保最重要的任务优先显示。

1.  **按状态排序**: 首先根据任务状态的优先级进行排序。
    - `completed` (已完成): 0 (最先显示)
    - `in_progress` (进行中): 1
    - `pending` (待处理): 2 (最后显示)
2.  **按优先级排序**: 如果任务状态相同，则根据任务本身的优先级进行排序。
    - `high` (高): 0 (最先显示)
    - `medium` (中): 1
    - `low` (低): 2 (最后显示)

**排序结果**: 最终的列表会首先展示所有已完成的任务，然后是进行中的任务，最后是待处理的任务。在每个状态分组内，再按高、中、低优先级排序。

### 6.2 任务状态管理

Agent 被严格指导如何管理任务状态，这是保证工作流严谨性的关键。

- **实时更新**: Agent 必须在任务状态发生变化时立即更新，而不是批量处理。
- **单一进行中**: 理想情况下，Agent 应该只保持一个任务处于 `in_progress` 状态，这有助于聚焦当前工作。
- **完成标准**: 只有在**完全**达成任务目标后，才能将任务标记为 `completed`。如果遇到错误、阻碍或实现不完整，任务应保持 `in_progress` 状态，并可能创建一个新的子任务来解决阻碍。

## 7. Agent 集成与工作流

Todo List 的真正威力在于它如何与 Agent 的工作流程深度集成。系统提示词 (Prompts) 对此有详细的指导。

### 7.1 何时使用 Todo List

Agent 被鼓励在以下场景中**主动、频繁**地使用 Todo 工具：

- **复杂任务分解**: 当任务需要3个或更多步骤时，应首先创建 Todo List 进行规划。
- **用户提供多任务**: 当用户一次性提出多个需求时。
- **接收新指令后**: 立即将用户的新需求捕获为待办事项。
- **开始工作前**: 将要开始的任务标记为 `in_progress`。
- **完成任务后**: 立即将任务标记为 `completed`，并添加任何必要的后续任务。
- **不确定时**: 当不确定下一步做什么时，通过 `TodoRead` 查看任务列表以获取方向。
- **周期性检查**: 每隔几轮对话，使用 `TodoRead` 检查进度，确保没有偏离轨道。

### 7.2 何时不使用 Todo List

为避免过度使用，Agent 在以下情况应避免使用 Todo List：

- **单一、直接的任务**: 如“在 Python 中打印 'Hello World'”。
- **纯信息性问答**: 如“`git status` 命令是做什么的？”。
- **琐碎、一步到位的操作**: 如“给函数加个注释”。

### 7.3 工作流示例

一个典型的、使用 Todo List 的工作流如下：

1.  **规划**: Agent 收到一个复杂请求（如“实现暗黑模式”）。
2.  **`TodoWrite`**: Agent 创建一个详细的 Todo List，将任务分解为多个步骤（如创建组件、添加状态管理、编写样式、运行测试等）。
3.  **`TodoRead`**: Agent 读取列表，确定第一个任务。
4.  **`TodoWrite`**: Agent 将第一个任务的状态更新为 `in_progress`。
5.  **执行**: Agent 使用其他工具（如 `Edit`, `Write`）执行该任务。
6.  **`TodoWrite`**: 任务完成后，Agent 将其状态更新为 `completed`。
7.  **循环**: Agent 重复步骤 3-6，直到所有任务完成。

## 8. UI 渲染

虽然本方案主要关注后端设计，但逆向分析揭示了其 UI 渲染机制，这有助于理解其完整设计。

- **列表渲染 (`ka0`)**: 任务列表在 UI 上会先经过排序 (`YJ1`)，然后逐项渲染。
- **单项渲染 (`JJ1`)**: 每个任务项会根据其状态显示不同的图标和颜色，提供直观的视觉反馈。
  - `completed`: `✓` (绿色)
  - `in_progress`: `⏳` (黄色)
  - `pending`: `⭕` (灰色)

## 9. 错误处理与安全性

- **输入验证**: 所有写入的数据都通过 Zod Schema 进行严格验证，防止无效数据进入系统。
- **文件系统错误**: 文件读写操作被包裹在 `try...catch` 块中。如果发生错误（如权限问题），系统会记录错误并返回一个安全默认值（如空列表），而不是崩溃。
- **会话隔离**: 通过基于 `sessionId` 和 `agentId` 的文件路径设计，从根本上保证了不同会T话和 Agent 之间的数据不会串扰，提供了多租户安全保障。

## 10. 与大模型的交互：System-Reminder 机制

为了让 Agent (大模型) 实时感知到 Todo List 的状态变化，Claude Code 设计了一套精巧的事件驱动的 `system-reminder` 机制，而不是在每次请求中都被动地附加任务列表。

### 10.1 概述

当 Todo List 发生变化时，系统会生成一个特殊的、对用户不可见的内部消息（`system-reminder`），并将其注入到下一次发送给大模型的请求中。这个消息会告知模型最新的任务列表状态，从而确保模型的工作上下文与系统状态保持同步。

### 10.2 触发时机与事件处理

- **核心触发点**: `TodoWrite` 工具成功执行后，会触发一个 `todo` 事件。
- **事件分发器 (`WD5`)**: 系统中存在一个核心的事件处理函数 `WD5`，它负责监听并处理各类事件。当 `WD5` 捕获到 `todo` 事件时，它会立即生成相应的 `system-reminder`。

### 10.3 提醒 (Reminder) 的生成

`WD5` 函数会根据 Todo List 的当前状态，生成两种不同的提醒内容：

1.  **当任务列表被更新（非空）时**:
    ```xml
    <system-reminder>
    Your todo list has changed. DO NOT mention this explicitly to the user. Here are the latest contents of your todo list:

    [{"id":"task-1","content":"...","status":"completed","priority":"high"}, ...]

    You DO NOT need to use the TodoRead tool again, since this is the most up to date list for now. Continue on with the tasks at hand if applicable.
    </system-reminder>
    ```
    这个提醒将完整的、最新的任务列表以 JSON 字符串的形式直接提供给模型。

2.  **当任务列表变为空时**:
    ```xml
    <system-reminder>
    This is a reminder that your todo list is currently empty. DO NOT mention this to the user explicitly because they are already aware. If you are working on tasks that would benefit from a todo list please use the TodoWrite tool to create one. If not, please feel free to ignore. Again do not mention this message to the user.
    </system-reminder>
    ```
    这个提醒旨在通知模型列表已空，并指导其在需要时重新创建。

### 10.4 消息注入与传递

- **元信息标记 (`isMeta: true`)**: 所有 `system-reminder` 消息都由一个消息工厂函数 (`K2`) 创建，并被标记为 `isMeta: true`。这个标记至关重要，它告诉系统这是一个内部元信息，不应直接展示给用户，并且在上下文压缩等流程中可能需要特殊处理。
- **注入流程**: 生成的元信息消息被放入消息队列，在下一次 Agent 主循环 (`nO`) 启动时，它会作为请求的一部分，与用户的消息一起被发送给大模型。

### 10.5 对 Agent 的核心指导

`system-reminder` 不仅同步了状态，还包含了对 Agent 行为的明确指导：

- **提高效率**: 当提供了最新的列表后，明确告知 Agent “你不需要再次使用 `TodoRead` 工具”，这避免了冗余的工具调用，节省了时间和 token。
- **优化用户体验**: 反复强调“不要向用户明确提及此事”。这是为了让系统的内部状态管理对用户保持透明，避免 Agent 输出干扰性的、与用户任务无关的系统操作信息，从而使交互更自然、更聚焦于任务本身。
