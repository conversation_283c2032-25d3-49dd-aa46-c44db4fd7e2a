[project]
name = "onedragon-agent"
version = "0.1.0"
description = "To build generic agent for OneDragon framework"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "httpx==0.28.1",
    "openai==1.97.1",
    "pydantic==2.11.7",
    "tenacity==9.1.2",
    "textual>=5.0.1",
    "watchdog>=6.0.0",
    "pathspec>=0.12.1",
]

[project.scripts]
onedragon-agent = "one_dragon_agent.cli:__main__"

[project.optional-dependencies]
dev = [
    "black>=25.1.0",
    "flake8>=7.3.0",
    "mypy>=1.17.0",
    "textual-dev>=1.7.0",
    "pytest==8.4.1",
    "pytest-asyncio==1.1.0",
    "pytest-timeout>=2.4.0",
]

[tool.setuptools.packages.find]
where = ["src"]  # 告诉 setuptools 去 src 目录下寻找包

[tool.flake8]
max-line-length = 88
