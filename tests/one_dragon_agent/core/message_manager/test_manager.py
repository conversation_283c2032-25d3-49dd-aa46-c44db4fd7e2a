"""Tests for MessageManager."""

import pytest
from one_dragon_agent.core.message_manager.manager import MessageManager
from one_dragon_agent.core.model.message import (
    OdaModelMessage,
    OdaModelMessageRole,
    OdaMessageTextContent,
)


class TestMessageManager:
    """Test cases for MessageManager."""

    @pytest.fixture
    def message_manager(self):
        """Create a MessageManager instance for testing."""
        return MessageManager()

    @pytest.fixture
    def sample_message(self):
        """Create a sample OdaModelMessage for testing."""
        return OdaModelMessage(
            role=OdaModelMessageRole.USER,
            content=OdaMessageTextContent(text="Hello, world!"),
        )

    @pytest.fixture
    def sample_system_prompt(self):
        """Create a sample system prompt for testing."""
        return OdaModelMessage(
            role=OdaModelMessageRole.SYSTEM,
            content=OdaMessageTextContent(text="You are a helpful assistant."),
        )

    def test_initialization(self, message_manager):
        """Test that the message manager is initialized correctly."""
        assert message_manager.get_message_history() == []
        assert message_manager.get_system_prompts() == []

    def test_append_message(self, message_manager, sample_message):
        """Test appending a message to the history."""
        message_manager.append_message(sample_message)
        history = message_manager.get_message_history()
        assert len(history) == 1
        assert history[0] == sample_message

    def test_append_system_prompt(self, message_manager, sample_system_prompt):
        """Test appending a system prompt."""
        message_manager.append_system_prompt(sample_system_prompt)
        prompts = message_manager.get_system_prompts()
        assert len(prompts) == 1
        assert prompts[0] == sample_system_prompt

    def test_set_system_prompts(self, message_manager, sample_system_prompt):
        """Test setting system prompts."""
        prompts = [sample_system_prompt, sample_system_prompt]
        message_manager.set_system_prompts(prompts)
        retrieved_prompts = message_manager.get_system_prompts()
        assert len(retrieved_prompts) == 2
        assert retrieved_prompts[0] == sample_system_prompt
        assert retrieved_prompts[1] == sample_system_prompt

    def test_get_message_use(
        self, message_manager, sample_system_prompt, sample_message
    ):
        """Test get_message_use method combines prompts and history correctly."""
        # Add system prompt
        message_manager.append_system_prompt(sample_system_prompt)

        # Add message
        message_manager.append_message(sample_message)

        # Get combined messages
        combined = message_manager.get_message_use()
        assert len(combined) == 2

        # Check that the messages match what we added
        assert combined[0].role == sample_system_prompt.role
        assert combined[0].content.text == sample_system_prompt.content.text
        assert combined[1].role == sample_message.role
        assert combined[1].content.text == sample_message.content.text

    def test_clear_history(self, message_manager, sample_system_prompt, sample_message):
        """Test clearing message history but not system prompts."""
        # Add system prompt
        message_manager.append_system_prompt(sample_system_prompt)

        # Add message
        message_manager.append_message(sample_message)

        # Clear history
        message_manager.clear_history()

        # Check that only history is cleared
        assert message_manager.get_message_history() == []
        assert len(message_manager.get_system_prompts()) == 1

    def test_clear_all(self, message_manager, sample_system_prompt, sample_message):
        """Test clearing all messages."""
        # Add system prompt
        message_manager.append_system_prompt(sample_system_prompt)

        # Add message
        message_manager.append_message(sample_message)

        # Clear all
        message_manager.clear_all()

        # Check that everything is cleared
        assert message_manager.get_message_history() == []
        assert message_manager.get_system_prompts() == []
