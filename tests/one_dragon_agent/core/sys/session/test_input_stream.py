"""
Test for OdaSession input stream functionality
"""

import asyncio
from unittest.mock import patch, MagicMock, AsyncMock
import pytest

from one_dragon_agent.core.model.config import ModelConfig
from one_dragon_agent.core.sys.config import OdaSessionConfig
from one_dragon_agent.core.sys.session import <PERSON>da<PERSON>ession
from one_dragon_agent.core.sys.message import (
    OdaMessage,
    OdaMessageSource,
)
from one_dragon_agent.core.model.message import (
    OdaModelMessageRole,
    OdaMessageTextContent,
)


@pytest.fixture
def mock_session_config():
    """Fixture for a mock OdaSessionConfig."""
    return OdaSessionConfig(
        common_llm_config=ModelConfig(model="test-model", api_key="test-key")
    )


@pytest.mark.timeout(10)
class TestInputStream:
    @pytest.mark.asyncio
    @patch("one_dragon_agent.core.sys.session.Agent")
    async def test_input_stream(self, MockAgent, mock_session_config):
        """Test that OdaSession properly handles input stream"""
        # Create a proper mock for the Agent class
        mock_agent_instance = MagicMock()
        mock_agent_instance.execute = AsyncMock()
        MockAgent.return_value = mock_agent_instance

        # Create the agent session
        agent_session = OdaSession(
            config=mock_session_config,
        )

        # Verify the agent session was created successfully
        assert agent_session is not None
        assert isinstance(agent_session, OdaSession)
        agent_session.abort()

    @pytest.mark.asyncio
    @patch("one_dragon_agent.core.sys.session.Agent")
    async def test_input_queue_integration(self, MockAgent, mock_session_config):
        """Test integration with input queue in a simulated CLI environment"""
        # Create a proper mock for the Agent class
        mock_agent_instance = MagicMock()
        mock_agent_instance.execute = AsyncMock()
        MockAgent.return_value = mock_agent_instance

        # Create the agent session
        agent_session = OdaSession(
            config=mock_session_config,
        )

        # Verify the agent session was created successfully
        assert agent_session is not None

        # Add some test inputs to the session
        await agent_session.send_input(
            OdaMessage(
                source=OdaMessageSource.USER,
                content=OdaMessageTextContent("First message"),
            )
        )
        await agent_session.send_input(
            OdaMessage(
                source=OdaMessageSource.USER,
                content=OdaMessageTextContent("Second message"),
            )
        )
        agent_session.abort()
