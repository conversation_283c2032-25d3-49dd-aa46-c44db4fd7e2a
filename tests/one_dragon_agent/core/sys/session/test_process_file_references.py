"""Tests for file reference processing in OdaSession."""

import os
import tempfile
from unittest.mock import AsyncMock, patch

import pytest

from one_dragon_agent.core.event.event import Event
from one_dragon_agent.core.event.handler import EventHandler
from one_dragon_agent.core.model.config import ModelConfig
from one_dragon_agent.core.sys.config import OdaSessionConfig
from one_dragon_agent.core.sys.message import (
    OdaMessage,
    OdaMessageSource,
)
from one_dragon_agent.core.model.message import OdaMessageTextContent
from one_dragon_agent.core.sys.session import OdaSession


class TestProcessFileReferences:
    """测试文件引用处理功能"""

    @pytest.fixture
    def session_config(self):
        """创建测试用的会话配置"""
        # 创建一个测试用的 ModelConfig
        model_config = ModelConfig(model="test-model", api_key="test-api-key")
        return OdaSessionConfig(common_llm_config=model_config)

    @pytest.fixture
    def session(self, session_config):
        """创建测试用的会话实例"""
        # 创建会话实例，不自动启动后台任务
        session = OdaSession(session_config, auto_start=False)

        yield session

        # 清理
        # Fix: await the close coroutine
        import asyncio

        asyncio.get_event_loop().run_until_complete(session.close())

    @pytest.fixture
    def temp_file(self):
        """创建临时文件用于测试"""
        current_dir = os.getcwd()
        temp_file_path = os.path.join(current_dir, "test_temp_file.txt")

        try:
            with open(temp_file_path, "w", encoding="utf-8") as f:
                f.write("This is a test file content.\nIt has multiple lines.\n")

            yield temp_file_path

        finally:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)

    # Event handler to capture events
    class CaptureEventHandler(EventHandler):
        def __init__(self):
            self.captured_events = []

        @property
        def event_type(self) -> str:
            return "*"  # Capture all events

        async def handle(self, event) -> None:
            self.captured_events.append(event)

    # --- Tests for file resolution functionality ---

    @pytest.mark.asyncio
    async def test_resolve_file_path_absolute_valid(self, session, temp_file):
        """测试解析绝对路径的有效文件"""
        resolved_path = session._resolve_file_path(temp_file, os.getcwd())
        assert resolved_path == temp_file

    @pytest.mark.asyncio
    async def test_resolve_file_path_relative_valid(self, session, temp_file):
        """测试解析相对路径的有效文件"""
        relative_path = os.path.basename(temp_file)
        resolved_path = session._resolve_file_path(relative_path, os.getcwd())
        assert resolved_path == temp_file

    @pytest.mark.asyncio
    async def test_resolve_file_path_invalid_not_exists(self, session):
        """测试解析不存在的文件路径"""
        resolved_path = session._resolve_file_path("nonexistent_file.txt", os.getcwd())
        assert resolved_path is None

    @pytest.mark.asyncio
    async def test_resolve_file_path_invalid_directory(self, session, temp_file):
        """测试解析目录路径（不是文件）"""
        directory_path = os.path.dirname(temp_file)
        resolved_path = session._resolve_file_path(directory_path, os.getcwd())
        assert resolved_path is None

    @pytest.mark.asyncio
    async def test_resolve_file_path_invalid_traversal(self, session, temp_file):
        """测试路径遍历攻击防护"""
        # 尝试访问当前目录的父目录中的文件
        parent_file = f"..{os.sep}{os.path.basename(temp_file)}"
        resolved_path = session._resolve_file_path(parent_file, os.getcwd())
        # 应该被阻止
        assert resolved_path is None

    # --- Tests for file reading functionality ---

    @pytest.mark.asyncio
    async def test_read_and_add_file_content_success(self, session, temp_file):
        """测试成功读取文件内容并添加到消息管理器"""
        # 记录初始消息数量
        initial_message_count = len(session._message_manager.get_system_prompts())

        original_path = "test_file.txt"
        await session._read_and_add_file_content(temp_file, original_path)

        # 验证消息管理器中是否添加了系统消息
        messages = session._message_manager.get_system_prompts()
        assert len(messages) == initial_message_count + 1
        message = messages[-1]  # 获取最新添加的消息
        assert message.role.name == "SYSTEM"
        assert original_path in message.content.text
        assert "This is a test file content." in message.content.text

    @pytest.mark.asyncio
    async def test_read_and_add_file_content_nonexistent(self, session):
        """测试读取不存在的文件"""
        # 记录初始消息数量和消息内容
        initial_messages = session._message_manager.get_system_prompts()
        initial_message_count = len(initial_messages)

        print(f"Initial message count: {initial_message_count}")
        for i, msg in enumerate(initial_messages):
            print(f"Initial message {i}: {msg.content.text[:100]}...")

        original_path = "nonexistent.txt"
        await session._read_and_add_file_content("nonexistent.txt", original_path)

        # 消息管理器应该没有新增消息
        messages = session._message_manager.get_system_prompts()
        print(f"Final message count: {len(messages)}")
        for i, msg in enumerate(messages):
            print(f"Final message {i}: {msg.content.text[:100]}...")

        assert len(messages) == initial_message_count

    # --- Tests for file reference processing in messages ---

    @pytest.mark.asyncio
    async def test_process_file_references_single(self, session, temp_file):
        """测试处理包含单个文件引用的消息"""
        # 记录初始消息数量
        initial_message_count = len(session._message_manager.get_system_prompts())

        message_content = f"Read this file: <oda-at-file>{temp_file}</oda-at-file>"
        message = OdaMessage(
            source=OdaMessageSource.USER, content=OdaMessageTextContent(message_content)
        )

        await session._process_file_references(message)

        # 验证文件内容是否添加到消息管理器
        messages = session._message_manager.get_system_prompts()
        assert len(messages) == initial_message_count + 1
        assert temp_file in messages[-1].content.text  # 检查最新添加的消息

        # 验证原始消息中的文件引用标签是否被移除
        assert "<oda-at-file>" not in message.content.text

    @pytest.mark.asyncio
    async def test_process_file_references_multiple(self, session, temp_file):
        """测试处理包含多个文件引用的消息"""
        # 记录初始消息数量
        initial_message_count = len(session._message_manager.get_system_prompts())

        # 创建第二个临时文件
        current_dir = os.getcwd()
        temp_file2_path = os.path.join(current_dir, "test_temp_file2.txt")
        try:
            with open(temp_file2_path, "w", encoding="utf-8") as f:
                f.write("This is the second test file.\n")

            message_content = (
                f"Read these files: <oda-at-file>{temp_file}</oda-at-file> "
                f"and <oda-at-file>{temp_file2_path}</oda-at-file>"
            )
            message = OdaMessage(
                source=OdaMessageSource.USER,
                content=OdaMessageTextContent(message_content),
            )

            await session._process_file_references(message)

            # 验证两个文件的内容是否都添加到消息管理器
            messages = session._message_manager.get_system_prompts()
            assert len(messages) == initial_message_count + 2
            # 检查两个最新添加的文件的内容都在消息中
            content1 = messages[-2].content.text  # 倒数第二个消息
            content2 = messages[-1].content.text  # 最后一个消息
            assert (temp_file in content1 and temp_file2_path in content2) or (
                temp_file in content2 and temp_file2_path in content1
            )

            # 验证原始消息中的文件引用标签是否被移除
            assert "<oda-at-file>" not in message.content.text
        finally:
            # 清理第二个临时文件
            if os.path.exists(temp_file2_path):
                os.remove(temp_file2_path)

    @pytest.mark.asyncio
    async def test_process_file_references_none(self, session):
        """测试处理不包含文件引用的消息"""
        # 记录初始消息数量
        initial_message_count = len(session._message_manager.get_system_prompts())

        message_content = "This is a normal message without file references."
        message = OdaMessage(
            source=OdaMessageSource.USER, content=OdaMessageTextContent(message_content)
        )

        await session._process_file_references(message)

        # 消息管理器应该没有新增消息
        messages = session._message_manager.get_system_prompts()
        assert len(messages) == initial_message_count

        # 原始消息内容应该保持不变
        assert message.content.text == message_content

    @pytest.mark.asyncio
    async def test_process_file_references_invalid_path(self, session):
        """测试处理包含无效文件路径的消息"""
        # 记录初始消息数量
        initial_message_count = len(session._message_manager.get_system_prompts())

        message_content = "Read this file: <oda-at-file>nonexistent.txt</oda-at-file>"
        message = OdaMessage(
            source=OdaMessageSource.USER, content=OdaMessageTextContent(message_content)
        )

        await session._process_file_references(message)

        # 消息管理器应该没有新增消息
        messages = session._message_manager.get_system_prompts()
        assert len(messages) == initial_message_count

        # 验证原始消息中的文件引用标签是否被移除
        assert "<oda-at-file>" not in message.content.text

    # --- New tests for display message functionality ---

    @pytest.mark.asyncio
    async def test_send_file_read_display_message_success(self, session, temp_file):
        """测试成功读取文件时发送的显示消息"""
        # Create event handler to capture events
        capture_handler = self.CaptureEventHandler()

        # Subscribe to all events
        session._event_dispatcher.subscribe("*", capture_handler)

        # Create a message with a file reference
        message_content = f"Read this file: <oda-at-file>{temp_file}</oda-at-file>"
        message = OdaMessage(
            source=OdaMessageSource.USER, content=OdaMessageTextContent(message_content)
        )

        # Process the file references
        await session._process_file_references(message)

        # Verify that an event was published
        assert len(capture_handler.captured_events) == 1
        event = capture_handler.captured_events[0]
        assert event.event_type == "file.read_results"
        assert "results" in event.data
        assert len(event.data["results"]) == 1
        assert event.data["results"][0][0] == temp_file
        assert event.data["results"][0][1] is True  # Success

    @pytest.mark.asyncio
    async def test_send_file_read_display_message_failure(self, session):
        """测试读取不存在的文件时发送的显示消息"""
        # Create event handler to capture events
        capture_handler = self.CaptureEventHandler()

        # Subscribe to all events
        session._event_dispatcher.subscribe("*", capture_handler)

        # Mock the send_display_message method
        # session.send_display_message = AsyncMock()

        # Create a message with a file reference to a non-existent file
        message_content = "Read this file: <oda-at-file>nonexistent.txt</oda-at-file>"
        message = OdaMessage(
            source=OdaMessageSource.USER, content=OdaMessageTextContent(message_content)
        )

        # Process the file references
        await session._process_file_references(message)

        # Verify that an event was published
        assert len(capture_handler.captured_events) == 1
        event = capture_handler.captured_events[0]
        assert event.event_type == "file.read_results"
        assert "results" in event.data
        assert len(event.data["results"]) == 1
        assert event.data["results"][0][0] == "nonexistent.txt"
        assert event.data["results"][0][1] is False  # Failure

    @pytest.mark.asyncio
    async def test_send_file_read_display_message_multiple(self, session, temp_file):
        """测试读取多个文件时发送的显示消息"""
        # Create event handler to capture events
        capture_handler = self.CaptureEventHandler()

        # Subscribe to all events
        session._event_dispatcher.subscribe("*", capture_handler)

        # Create a second temporary file
        current_dir = os.getcwd()
        temp_file2_path = os.path.join(current_dir, "test_temp_file2.txt")
        try:
            with open(temp_file2_path, "w", encoding="utf-8") as f:
                f.write("This is the second test file.\n")

            # Create a message with multiple file references
            message_content = (
                f"Read these files: <oda-at-file>{temp_file}</oda-at-file> "
                f"and <oda-at-file>{temp_file2_path}</oda-at-file> "
                f"and <oda-at-file>nonexistent.txt</oda-at-file>"
            )
            message = OdaMessage(
                source=OdaMessageSource.USER,
                content=OdaMessageTextContent(message_content),
            )

            # Process the file references
            await session._process_file_references(message)

            # Verify that an event was published
            assert len(capture_handler.captured_events) == 1
            event = capture_handler.captured_events[0]
            assert event.event_type == "file.read_results"
            assert "results" in event.data
            assert len(event.data["results"]) == 3

            # Check that all files are in the results
            results = event.data["results"]
            file_paths = [result[0] for result in results]
            assert temp_file in file_paths
            assert temp_file2_path in file_paths
            assert "nonexistent.txt" in file_paths

            # Check success/failure status
            success_status = {result[0]: result[1] for result in results}
            assert success_status[temp_file] is True
            assert success_status[temp_file2_path] is True
            assert success_status["nonexistent.txt"] is False
        finally:
            # Clean up the second temporary file
            if os.path.exists(temp_file2_path):
                os.remove(temp_file2_path)
