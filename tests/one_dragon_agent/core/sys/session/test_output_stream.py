"""
Tests for OdaSession output stream functionality
"""

import asyncio
from dataclasses import dataclass
from typing import Any, Dict
from unittest.mock import patch, AsyncMock
import pytest

from one_dragon_agent.core.model.config import ModelConfig
from one_dragon_agent.core.sys.config import OdaSessionConfig
from one_dragon_agent.core.sys.session import OdaSession
from one_dragon_agent.core.sys.message import (
    OdaMessage,
    OdaMessageSource,
)
from one_dragon_agent.core.model.message import (
    OdaModelMessage,
    OdaModelMessageRole,
    OdaMessageTextContent,
)
from one_dragon_agent.core.event.event import Event
from one_dragon_agent.core.event.handler import EventHandler


@dataclass
class TestEvent(Event):
    """A test event with data."""

    data: Dict[str, Any]


@pytest.fixture
def mock_session_config():
    """Fixture for a mock OdaSessionConfig."""
    return OdaSessionConfig(
        common_llm_config=ModelConfig(model="test-model", api_key="test-key")
    )


# Helper to simulate the Agent's behavior of sending output messages
class MockAgent:
    def __init__(
        self,
        session_config,
        message_manager,
        command,
        llm_factory=None,
        tool_manager=None,
        event_dispatcher=None,
        reminder_manager=None,
        permission_manager=None,
    ):
        self.event_dispatcher = event_dispatcher
        self.response_messages = [
            OdaMessage(
                source=OdaMessageSource.AI_RESPONSE,
                content=OdaMessageTextContent(text="Response"),
            ),
            OdaMessage(
                source=OdaMessageSource.AI_RESPONSE,
                content=OdaMessageTextContent(text=" chunk"),
            ),
        ]

    async def execute(self):
        # Simulate publishing events instead of sending messages to the queue
        if self.event_dispatcher:
            for i, msg in enumerate(self.response_messages):
                event = TestEvent(event_type=f"test.response.{i}", data={"message": msg})
                await self.event_dispatcher.publish(event)


# Event handler to capture events
class CaptureEventHandler(EventHandler):
    def __init__(self):
        self.captured_events = []

    @property
    def event_type(self) -> str:
        return "*"  # Capture all events

    async def handle(self, event) -> None:
        self.captured_events.append(event)


@pytest.mark.timeout(10)
class TestOutputStream:
    @pytest.mark.asyncio
    @patch("one_dragon_agent.core.sys.session.Agent", MockAgent)
    async def test_get_output_stream_with_auto_processing(self, mock_session_config):
        """Test that OdaSession automatically processes commands and sends messages to output stream"""

        # Create a mock input stream with OdaMessage objects
        messages = [
            OdaMessage(
                source=OdaMessageSource.USER,
                content=OdaMessageTextContent("Hello, world!"),
            ),
            OdaMessage(
                source=OdaMessageSource.USER,
                content=OdaMessageTextContent("How are you?"),
            ),
        ]

        # Create the agent session
        agent_session = OdaSession(
            config=mock_session_config,
        )

        # Create event handler to capture events
        capture_handler = CaptureEventHandler()

        # Subscribe to all events
        agent_session._event_dispatcher.subscribe("*", capture_handler)

        # Send messages to the session
        for msg in messages:
            await agent_session.send_input(msg)

        # Give some time for processing to happen
        await asyncio.sleep(0.1)

        # Verify we got the expected number of events
        # Each input produces 2 response events = 4 total
        assert len(capture_handler.captured_events) == len(messages) * 2

        # Verify the events have the correct data
        expected_texts = ["Response", " chunk", "Response", " chunk"]
        for i, event in enumerate(capture_handler.captured_events):
            assert event.event_type == f"test.response.{i % 2}"
            message = event.data["message"]
            assert isinstance(message, OdaMessage)
            assert message.source == OdaMessageSource.AI_RESPONSE
            assert isinstance(message.content, OdaMessageTextContent)
            assert message.content.text == expected_texts[i]

        agent_session.abort()
