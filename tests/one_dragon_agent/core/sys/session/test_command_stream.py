"""
Tests for OdaSession command stream functionality
"""

import asyncio
from dataclasses import dataclass
from typing import Any, Dict
from unittest.mock import patch, AsyncMock, MagicMock

import pytest

from one_dragon_agent.core.model.config import ModelConfig
from one_dragon_agent.core.sys.config import OdaSessionConfig
from one_dragon_agent.core.sys.session import OdaSession
from one_dragon_agent.core.sys.message import (
    OdaMessage,
    OdaMessageSource,
)
from one_dragon_agent.core.model.message import (
    OdaModelMessage,
    OdaModelMessageRole,
    OdaMessageTextContent,
)
from one_dragon_agent.core.event.event import Event
from one_dragon_agent.core.event.handler import EventHandler


@dataclass
class TestEvent(Event):
    """A test event with data."""

    data: Dict[str, Any]


@pytest.fixture
def mock_session_config():
    """Fixture for a mock OdaSessionConfig."""
    return OdaSessionConfig(
        common_llm_config=ModelConfig(model="test-model", api_key="test-key")
    )


# Helper to simulate the Agent's behavior of sending output messages
class MockAgent:
    def __init__(
        self,
        session_config,
        message_manager,
        command,
        llm_factory=None,
        tool_manager=None,
        event_dispatcher=None,
        reminder_manager=None,
        permission_manager=None,
    ):
        self.event_dispatcher = event_dispatcher
        self.response_messages = [
            OdaMessage(
                source=OdaMessageSource.AI_RESPONSE,
                content=OdaMessageTextContent(text="Processed"),
            ),
            OdaMessage(
                source=OdaMessageSource.AI_RESPONSE,
                content=OdaMessageTextContent(text=" command"),
            ),
        ]

    async def execute(self):
        # Simulate publishing events instead of sending messages to the queue
        if self.event_dispatcher:
            for i, msg in enumerate(self.response_messages):
                event = TestEvent(event_type=f"test.response.{i}", data={"message": msg})
                await self.event_dispatcher.publish(event)


# Event handler to capture events
class CaptureEventHandler(EventHandler):
    def __init__(self):
        self.captured_events = []

    @property
    def event_type(self) -> str:
        return "*"  # Capture all events

    async def handle(self, event) -> None:
        self.captured_events.append(event)


@pytest.mark.timeout(10)
class TestCommandStream:
    @pytest.mark.asyncio
    @patch("one_dragon_agent.core.sys.session.Agent", MockAgent)
    async def test_command_processing_with_valid_messages(self, mock_session_config):
        """Test that OdaSession processes valid messages correctly"""

        # Create a mock input stream with OdaMessage objects
        messages = [
            OdaMessage(
                source=OdaMessageSource.USER,
                content=OdaMessageTextContent("First command"),
            ),
            OdaMessage(
                source=OdaMessageSource.USER,
                content=OdaMessageTextContent("Second command"),
            ),
        ]

        # Create the agent session
        agent_session = OdaSession(config=mock_session_config)

        # Create event handler to capture events
        capture_handler = CaptureEventHandler()

        # Subscribe to all events
        agent_session._event_dispatcher.subscribe("*", capture_handler)

        # Send messages to the session
        for msg in messages:
            await agent_session.send_input(msg)

        # Give some time for processing to happen
        await asyncio.sleep(0.1)

        # Verify we got the expected number of events
        # Each input produces 2 response events = 4 total
        assert len(capture_handler.captured_events) == len(messages) * 2

        # Verify the events have the correct data
        expected_texts = ["Processed", " command", "Processed", " command"]
        for i, event in enumerate(capture_handler.captured_events):
            assert event.event_type == f"test.response.{i % 2}"
            message = event.data["message"]
            assert isinstance(message, OdaMessage)
            assert message.source == OdaMessageSource.AI_RESPONSE
            assert isinstance(message.content, OdaMessageTextContent)
            assert message.content.text == expected_texts[i]

        agent_session.abort()

    @pytest.mark.asyncio
    @patch("one_dragon_agent.core.sys.session.Agent", MockAgent)
    async def test_command_processing_with_invalid_messages(self, mock_session_config):
        """Test that OdaSession properly filters invalid messages"""

        # Create a mock input stream with mix of valid and invalid OdaMessage objects
        valid_message = OdaMessage(
            source=OdaMessageSource.USER,
            content=OdaMessageTextContent("Valid command"),
        )

        invalid_message = OdaMessage(
            source=OdaMessageSource.ODA,  # Invalid source (not USER)
            content=OdaMessageTextContent("Invalid command"),
        )

        # Create the agent session
        agent_session = OdaSession(config=mock_session_config)

        # Create event handler to capture events
        capture_handler = CaptureEventHandler()

        # Subscribe to all events
        agent_session._event_dispatcher.subscribe("*", capture_handler)

        # Send messages to the session
        for msg in [valid_message, invalid_message, valid_message]:
            await agent_session.send_input(msg)

        # Give some time for processing to happen
        await asyncio.sleep(0.1)

        # The MessageGateway should filter out the invalid message
        # We expect 2 valid inputs * 2 response events each = 4 events
        assert (
            len(capture_handler.captured_events) == 4
        )  # 2 valid commands * 2 response events each

        agent_session.abort()
