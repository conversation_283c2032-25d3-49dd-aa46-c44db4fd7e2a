"""
Tests for OdaSession initialization functionality
"""

import asyncio
from unittest.mock import patch, MagicMock
import pytest

from one_dragon_agent.core.model.config import ModelConfig
from one_dragon_agent.core.sys.config import OdaSessionConfig
from one_dragon_agent.core.sys.session import OdaSession
from one_dragon_agent.core.model.message import (
    OdaModelMessage,
    OdaModelMessageRole,
    OdaMessageTextContent,
)
from one_dragon_agent.core.model.message import (
    OdaModelMessageRole,
    OdaMessageTextContent,
)


@pytest.fixture
def mock_session_config():
    """Fixture for a mock OdaSessionConfig."""
    return OdaSessionConfig(
        common_llm_config=ModelConfig(model="test-model", api_key="test-key")
    )


@pytest.mark.timeout(10)
class TestSessionInitialization:
    @pytest.mark.asyncio
    async def test_session_initialization_with_system_prompts(
        self, mock_session_config
    ):
        """Test that OdaSession properly initializes with system prompts"""

        # Create the agent session
        agent_session = OdaSession(
            config=mock_session_config,
        )

        # Give some time for initialization to happen
        await asyncio.sleep(0.1)

        # Verify the agent session was created successfully
        assert agent_session is not None
        assert isinstance(agent_session, OdaSession)

        # Check that system prompts were added to message manager
        prompts = agent_session._message_manager.get_system_prompts()
        assert len(prompts) >= 2  # Should have at least 2 system prompts

        # Check the first prompt (agent system prompt)
        first_prompt = prompts[0]
        assert first_prompt.role == OdaModelMessageRole.SYSTEM
        assert isinstance(first_prompt.content, OdaMessageTextContent)
        assert first_prompt.content.text is not None
        assert "interactive AI agent" in first_prompt.content.text

        # Check the second prompt (environment info prompt)
        second_prompt = prompts[1]
        assert second_prompt.role == OdaModelMessageRole.SYSTEM
        assert isinstance(second_prompt.content, OdaMessageTextContent)
        assert second_prompt.content.text is not None
        assert "environment" in second_prompt.content.text

        agent_session.abort()
