"""
Tests for OdaSession.handle_permission_response functionality
"""

import asyncio
from unittest.mock import patch, MagicMock, AsyncMock
import pytest
import pytest_asyncio

from one_dragon_agent.core.model.config import ModelConfig
from one_dragon_agent.core.sys.config import OdaSessionConfig
from one_dragon_agent.core.sys.session import OdaSession


@pytest.fixture
def mock_session_config():
    """Fixture for a mock OdaSessionConfig."""
    return OdaSessionConfig(
        common_llm_config=ModelConfig(model="test-model", api_key="test-key")
    )


@pytest_asyncio.fixture
async def mock_session(mock_session_config):
    """Fixture for a mock OdaSession."""
    with patch.object(OdaSession, '_process_messages', return_value=None):
        session = OdaSession(config=mock_session_config, auto_start=False)
        yield session
        await session.close()


@pytest.mark.timeout(10)
class TestHandlePermissionResponse:
    @pytest.mark.asyncio
    async def test_handle_permission_response_grant_session(
        self, mock_session
    ):
        """Test handle_permission_response with granted=True and scope='session'"""
        permission = "test.permission"
        granted = True
        scope = "session"
        
        # Mock the PermissionManager methods
        mock_permission_manager = AsyncMock()
        mock_session._permission_manager = mock_permission_manager
        
        # Call the method under test
        await mock_session.handle_permission_response(permission, granted, scope)
        
        # Assertions
        mock_permission_manager.grant.assert_awaited_once_with(
            mock_session._session_id, permission
        )
        mock_permission_manager.set_response_signal.assert_awaited_once_with(
            mock_session._session_id, permission, granted
        )

    @pytest.mark.asyncio
    async def test_handle_permission_response_grant_once(
        self, mock_session
    ):
        """Test handle_permission_response with granted=True and scope='once'"""
        permission = "test.permission"
        granted = True
        scope = "once"
        
        # Mock the PermissionManager methods
        mock_permission_manager = AsyncMock()
        mock_session._permission_manager = mock_permission_manager
        
        # Call the method under test
        await mock_session.handle_permission_response(permission, granted, scope)
        
        # Assertions
        # grant should not be called for scope='once'
        mock_permission_manager.grant.assert_not_awaited()
        mock_permission_manager.set_response_signal.assert_awaited_once_with(
            mock_session._session_id, permission, granted
        )

    @pytest.mark.asyncio
    async def test_handle_permission_response_deny(
        self, mock_session
    ):
        """Test handle_permission_response with granted=False"""
        permission = "test.permission"
        granted = False
        scope = "session"  # scope doesn't matter if not granted
        
        # Mock the PermissionManager methods
        mock_permission_manager = AsyncMock()
        mock_session._permission_manager = mock_permission_manager
        
        # Call the method under test
        await mock_session.handle_permission_response(permission, granted, scope)
        
        # Assertions
        # grant should not be called if not granted
        mock_permission_manager.grant.assert_not_awaited()
        mock_permission_manager.set_response_signal.assert_awaited_once_with(
            mock_session._session_id, permission, granted
        )