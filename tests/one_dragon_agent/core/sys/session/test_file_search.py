"""Tests for OdaSession file search functionality."""

import async<PERSON>
import os
import tempfile
from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch

import pytest

from one_dragon_agent.core.event.file_events import FileSearchRequestEvent, FileSearchResultsEvent
from one_dragon_agent.core.model.config import ModelConfig
from one_dragon_agent.core.sys.config import OdaSessionConfig
from one_dragon_agent.core.sys.session import OdaSession


class TestOdaSessionFileSearch:
    """Test cases for OdaSession file search functionality."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory with test files."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create test files and directories
            test_file = os.path.join(temp_dir, "test.py")
            with open(test_file, "w") as f:
                f.write("print('hello')")
            
            src_dir = os.path.join(temp_dir, "src")
            os.makedirs(src_dir)
            
            src_file = os.path.join(src_dir, "main.py")
            with open(src_file, "w") as f:
                f.write("import sys")
            
            docs_dir = os.path.join(temp_dir, "docs")
            os.makedirs(docs_dir)
            
            readme_file = os.path.join(temp_dir, "README.md")
            with open(readme_file, "w") as f:
                f.write("# Test Project")
            
            yield temp_dir
    
    @pytest.fixture
    def session_config(self, temp_dir):
        """Create OdaSessionConfig with temp directory."""
        model_config = ModelConfig(
            model="test-model",
            api_key="test-key"
        )
        return OdaSessionConfig(
            common_llm_config=model_config,
            current_working_directory=temp_dir
        )
    
    @pytest.fixture
    def session(self, session_config):
        """Create OdaSession instance for testing."""
        # Don't auto-start the processing task
        session = OdaSession(session_config, auto_start=False)
        yield session
        # Clean up
        asyncio.run(session.close())
    
    @pytest.mark.asyncio
    async def test_handle_file_search_request_success(self, session):
        """Test handling file search request successfully."""
        # Mock directory cache to return results
        cache_results = [
            {"name": "test.py", "path": "test.py", "is_dir": False, "modified_time": 1234567890}
        ]
        session._directory_cache.search = AsyncMock(return_value=cache_results)
        
        # Mock event dispatcher
        session._event_dispatcher.publish = MagicMock()
        
        # Create search request event
        event = FileSearchRequestEvent(
            event_type="file.search.request",
            request_id="test-req-1",
            query="test",
            base_path="."
        )
        
        # Handle the request
        await session._handle_file_search_request(event)
        
        # Verify directory cache was called
        session._directory_cache.search.assert_called_once_with(
            query="test",
            base_path=".",
            limit=50
        )
        
        # Verify results were published
        session._event_dispatcher.publish.assert_called_once()
        published_event = session._event_dispatcher.publish.call_args[0][0]
        assert isinstance(published_event, FileSearchResultsEvent)
        assert published_event.request_id == "test-req-1"
        assert published_event.from_cache is True
        assert len(published_event.results) == 1
        assert published_event.results[0] == {"path": "test.py", "type": "file"}
    
    @pytest.mark.asyncio
    async def test_handle_file_search_request_with_directories(self, session):
        """Test handling file search request with directory results."""
        # Mock directory cache to return mixed results
        cache_results = [
            {"name": "src", "path": "src", "is_dir": True, "modified_time": 1234567890},
            {"name": "test.py", "path": "test.py", "is_dir": False, "modified_time": 1234567890}
        ]
        session._directory_cache.search = AsyncMock(return_value=cache_results)
        
        # Mock event dispatcher
        session._event_dispatcher.publish = MagicMock()
        
        # Create search request event
        event = FileSearchRequestEvent(
            event_type="file.search.request",
            request_id="test-req-2",
            query="",
            base_path="."
        )
        
        # Handle the request
        await session._handle_file_search_request(event)
        
        # Verify results were published with correct types
        session._event_dispatcher.publish.assert_called_once()
        published_event = session._event_dispatcher.publish.call_args[0][0]
        assert isinstance(published_event, FileSearchResultsEvent)
        assert len(published_event.results) == 2
        
        # Check directory result
        dir_result = next((r for r in published_event.results if r["type"] == "folder"), None)
        assert dir_result is not None
        assert dir_result["path"] == "src"
        
        # Check file result
        file_result = next((r for r in published_event.results if r["type"] == "file"), None)
        assert file_result is not None
        assert file_result["path"] == "test.py"
    
    @pytest.mark.asyncio
    async def test_handle_file_search_request_empty_results(self, session):
        """Test handling file search request with no results."""
        # Mock directory cache to return empty results
        session._directory_cache.search = AsyncMock(return_value=[])
        
        # Mock event dispatcher
        session._event_dispatcher.publish = MagicMock()
        
        # Create search request event
        event = FileSearchRequestEvent(
            event_type="file.search.request",
            request_id="test-req-3",
            query="nonexistent",
            base_path="."
        )
        
        # Handle the request
        await session._handle_file_search_request(event)
        
        # Verify empty results were published
        session._event_dispatcher.publish.assert_called_once()
        published_event = session._event_dispatcher.publish.call_args[0][0]
        assert isinstance(published_event, FileSearchResultsEvent)
        assert published_event.request_id == "test-req-3"
        assert published_event.results == []
        assert published_event.from_cache is True
    
    @pytest.mark.asyncio
    async def test_handle_file_search_request_error_handling(self, session):
        """Test error handling in file search request."""
        # Mock directory cache to raise an exception
        session._directory_cache.search = AsyncMock(side_effect=Exception("Cache error"))
        
        # Mock event dispatcher
        session._event_dispatcher.publish = MagicMock()
        
        # Create search request event
        event = FileSearchRequestEvent(
            event_type="file.search.request",
            request_id="test-req-4",
            query="test",
            base_path="."
        )
        
        # Handle the request (should not raise exception)
        await session._handle_file_search_request(event)
        
        # Verify error response was published
        session._event_dispatcher.publish.assert_called_once()
        published_event = session._event_dispatcher.publish.call_args[0][0]
        assert isinstance(published_event, FileSearchResultsEvent)
        assert published_event.request_id == "test-req-4"
        assert published_event.results == []
        assert published_event.error == "Cache error"
        assert published_event.from_cache is False
    
    @pytest.mark.asyncio
    async def test_handle_file_search_request_with_base_path(self, session):
        """Test handling file search request with specific base path."""
        # Mock directory cache to return results
        cache_results = [
            {"name": "main.py", "path": "src/main.py", "is_dir": False, "modified_time": 1234567890}
        ]
        session._directory_cache.search = AsyncMock(return_value=cache_results)
        
        # Mock event dispatcher
        session._event_dispatcher.publish = MagicMock()
        
        # Create search request event with base path
        event = FileSearchRequestEvent(
            event_type="file.search.request",
            request_id="test-req-5",
            query="main",
            base_path="src"
        )
        
        # Handle the request
        await session._handle_file_search_request(event)
        
        # Verify directory cache was called with correct base path
        session._directory_cache.search.assert_called_once_with(
            query="main",
            base_path="src",
            limit=50
        )
        
        # Verify results were published
        session._event_dispatcher.publish.assert_called_once()
        published_event = session._event_dispatcher.publish.call_args[0][0]
        assert isinstance(published_event, FileSearchResultsEvent)
        assert published_event.request_id == "test-req-5"
        assert len(published_event.results) == 1
        assert published_event.results[0] == {"path": "src/main.py", "type": "file"}
    
    @pytest.mark.asyncio
    async def test_directory_cache_initialization(self, session, temp_dir):
        """Test that directory cache is properly initialized."""
        # Start the session
        await session._directory_cache.start()
        
        # Verify cache manager is running
        assert session._directory_cache._is_running is True
        assert session._directory_cache.update_queue is not None
        
        # Stop the cache
        await session._directory_cache.stop()
        
        # Verify cache manager is stopped
        assert session._directory_cache._is_running is False
    
    @pytest.mark.asyncio
    async def test_session_starts_directory_cache(self, session_config, temp_dir):
        """Test that session starts directory cache when auto_start is True."""
        # Create session with auto_start=True
        session = OdaSession(session_config, auto_start=True)
        
        # Give some time for background tasks to start
        await asyncio.sleep(0.1)
        
        # Verify cache manager is running
        assert session._directory_cache._is_running is True
        
        # Clean up
        await session.close()
    
    @pytest.mark.asyncio
    async def test_session_stops_directory_cache_on_close(self, session_config, temp_dir):
        """Test that session stops directory cache on close."""
        # Create session with auto_start=True
        session = OdaSession(session_config, auto_start=True)
        
        # Give some time for background tasks to start
        await asyncio.sleep(0.1)
        
        # Verify cache manager is running
        assert session._directory_cache._is_running is True
        
        # Close the session
        await session.close()
        
        # Verify cache manager is stopped
        assert session._directory_cache._is_running is False