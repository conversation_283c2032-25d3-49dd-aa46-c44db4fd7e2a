import pytest
from one_dragon_agent.core.sys.workspace_index import IndexNode


class TestIndexNode:
    """测试IndexNode类"""

    def test_index_node_creation(self) -> None:
        """测试IndexNode的创建"""
        # 测试基本文件节点
        file_node = IndexNode(
            name="test.txt",
            path="src/test.txt",
            is_dir=False,
            mtime=1234567890.0,
            is_core=True
        )
        
        assert file_node.name == "test.txt"
        assert file_node.path == "src/test.txt"
        assert file_node.is_dir is False
        assert file_node.mtime == 1234567890.0
        assert file_node.is_core is True
        assert file_node.parent is None
        assert file_node.children == {}
        
        # 测试目录节点
        dir_node = IndexNode(
            name="src",
            path="src",
            is_dir=True,
            mtime=0.0,
            is_core=False
        )
        
        assert dir_node.name == "src"
        assert dir_node.path == "src"
        assert dir_node.is_dir is True
        assert dir_node.mtime == 0.0
        assert dir_node.is_core is False
        assert dir_node.parent is None
        assert dir_node.children == {}

    def test_index_node_with_children(self) -> None:
        """测试带有子节点的IndexNode"""
        # 创建父目录节点
        parent_node = IndexNode(
            name="src",
            path="src",
            is_dir=True
        )
        
        # 创建子文件节点
        child_node = IndexNode(
            name="test.txt",
            path="src/test.txt",
            is_dir=False,
            parent=parent_node
        )
        
        # 手动添加到父节点的children字典
        parent_node.children[child_node.name] = child_node
        
        assert len(parent_node.children) == 1
        assert "test.txt" in parent_node.children
        assert parent_node.children["test.txt"] == child_node
        assert child_node.parent == parent_node