import pytest
import tempfile
import os
import asyncio
from pathlib import Path
from one_dragon_agent.core.sys.workspace_index import WorkspaceIndex


class TestSearch:
    """测试WorkspaceIndex的search方法"""

    @pytest.mark.asyncio
    async def test_search_empty_query(self) -> None:
        """测试空查询"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建一些测试文件
            (Path(temp_dir) / "test.py").write_text("print('hello')")
            (Path(temp_dir) / "main.py").write_text("print('main')")
            
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            await workspace_index.initialize()
            
            # 测试空查询
            results = await workspace_index.search("", "")
            assert len(results) == 0

    @pytest.mark.asyncio
    async def test_search_path_prefix(self) -> None:
        """测试路径前缀搜索"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试目录结构
            src_dir = Path(temp_dir) / "src"
            src_dir.mkdir()
            (src_dir / "main.py").write_text("print('main')")
            (src_dir / "utils.py").write_text("print('utils')")
            
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            await workspace_index.initialize()
            
            # 测试路径前缀搜索
            results = await workspace_index.search("src", "")
            assert len(results) == 2
            paths = [node.path for node in results]
            assert "src/main.py" in paths
            assert "src/utils.py" in paths

    @pytest.mark.asyncio
    async def test_search_name_prefix(self) -> None:
        """测试名称前缀搜索"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试文件
            (Path(temp_dir) / "main.py").write_text("print('main')")
            (Path(temp_dir) / "utils.py").write_text("print('utils')")
            (Path(temp_dir) / "main.js").write_text("console.log('main')")
            
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            await workspace_index.initialize()
            
            # 测试名称前缀搜索
            results = await workspace_index.search("main", "")
            assert len(results) == 2
            paths = [node.path for node in results]
            assert "main.py" in paths
            assert "main.js" in paths

    @pytest.mark.asyncio
    async def test_search_directory_listing(self) -> None:
        """测试目录内容列出"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试目录结构
            src_dir = Path(temp_dir) / "src"
            src_dir.mkdir()
            (src_dir / "main.py").write_text("print('main')")
            (src_dir / "utils.py").write_text("print('utils')")
            (src_dir / "config.json").write_text("{}")
            
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            await workspace_index.initialize()
            
            # 测试目录内容列出
            results = await workspace_index.search("src/", "")
            assert len(results) == 3
            names = [node.name for node in results]
            assert "main.py" in names
            assert "utils.py" in names
            assert "config.json" in names

    @pytest.mark.asyncio
    async def test_search_with_context(self) -> None:
        """测试带上下文的搜索"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试目录结构
            src_dir = Path(temp_dir) / "src"
            src_dir.mkdir()
            (src_dir / "main.py").write_text("print('main')")
            (Path(temp_dir) / "main.js").write_text("console.log('main')")
            
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            await workspace_index.initialize()
            
            # 在src目录上下文中搜索main
            results = await workspace_index.search("main", "src")
            assert len(results) == 1
            assert results[0].path == "src/main.py"

    @pytest.mark.asyncio
    async def test_search_path_normalization(self) -> None:
        """测试路径规范化处理"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试目录结构
            src_dir = Path(temp_dir) / "src"
            src_dir.mkdir()
            (src_dir / "main.py").write_text("print('main')")
            
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            await workspace_index.initialize()
            
            # 测试各种路径格式
            test_cases = [
                ("src", "", ["src/main.py"]),  # 标准格式
                ("/src", "", ["src/main.py"]),  # 前导斜杠
                ("src/", "", ["src/main.py"]),  # 结尾斜杠
                ("/src/", "", ["src/main.py"]),  # 前后都有斜杠
                ("src\\main", "", ["src/main.py"]),  # 反斜杠
            ]
            
            for query, context, expected_paths in test_cases:
                results = await workspace_index.search(query, context)
                paths = [node.path for node in results]
                for expected_path in expected_paths:
                    assert expected_path in paths, f"Failed for query: {query}, context: {context}"

    @pytest.mark.asyncio
    async def test_search_security_validation(self) -> None:
        """测试路径安全性验证"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试文件
            (Path(temp_dir) / "safe.py").write_text("print('safe')")
            
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            await workspace_index.initialize()
            
            # 测试路径遍历攻击
            malicious_queries = [
                ("../etc/passwd", ""),  # 尝试访问系统文件
                ("..\\windows\\system32", ""),  # Windows路径遍历
                ("src/../../../etc", ""),  # 多层路径遍历
                ("", "../malicious"),  # 上下文路径遍历
            ]
            
            for query, context in malicious_queries:
                results = await workspace_index.search(query, context)
                assert len(results) == 0, f"Security validation failed for query: {query}, context: {context}"

    @pytest.mark.asyncio
    async def test_search_initialization_states(self) -> None:
        """测试初始化状态处理"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试文件
            (Path(temp_dir) / "core.py").write_text("print('core')")
            (Path(temp_dir) / "dynamic.py").write_text("print('dynamic')")
            
            # 设置核心模式，使core.py成为核心文件
            workspace_index = WorkspaceIndex(
                root_path=temp_dir,
                core_patterns=["*.py"],  # 所有.py文件都是核心文件
            )
            
            # 不调用initialize，测试未初始化状态
            results = await workspace_index.search("core", "")
            assert len(results) == 0  # 未初始化应该返回空结果
            
            # 开始初始化但不等待完成
            init_task = asyncio.create_task(workspace_index.initialize())
            
            # 等待一小段时间确保初始化开始
            await asyncio.sleep(0.1)
            
            # 测试初始化中的状态
            results = await workspace_index.search("core", "")
            # 应该能找到核心文件（即使在初始化中）
            assert len(results) > 0
            assert any(node.path == "core.py" for node in results)
            
            # 等待初始化完成
            await init_task
            
            # 测试初始化完成后的状态
            results = await workspace_index.search("dynamic", "")
            assert len(results) > 0
            assert any(node.path == "dynamic.py" for node in results)

    @pytest.mark.asyncio
    async def test_search_fallback_scan_directory(self) -> None:
        """测试回退扫描 - 目录单层扫描"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试目录结构
            src_dir = Path(temp_dir) / "src"
            src_dir.mkdir()
            (src_dir / "existing.py").write_text("print('existing')")
            
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            await workspace_index.initialize()
            
            # 在索引构建后创建新文件
            new_file = src_dir / "new_file.py"
            new_file.write_text("print('new')")
            
            # 搜索应该触发回退扫描
            results = await workspace_index.search("src/new_file", "")
            assert len(results) == 1
            assert results[0].path == "src/new_file.py"

    @pytest.mark.asyncio
    async def test_search_fallback_scan_global(self) -> None:
        """测试回退扫描 - 文件名全局扫描"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试文件
            (Path(temp_dir) / "existing.py").write_text("print('existing')")
            
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            await workspace_index.initialize()
            
            # 在索引构建后创建新文件
            new_file = Path(temp_dir) / "new_file.py"
            new_file.write_text("print('new')")
            
            # 搜索应该触发回退扫描
            results = await workspace_index.search("new_file", "")
            assert len(results) == 1
            assert results[0].path == "new_file.py"

    @pytest.mark.asyncio
    async def test_search_concurrent_global_scan(self) -> None:
        """测试并发全局扫描的锁机制"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试文件
            (Path(temp_dir) / "test.py").write_text("print('test')")
            
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            await workspace_index.initialize()
            
            # 在索引构建后创建新文件
            new_file = Path(temp_dir) / "concurrent_test.py"
            new_file.write_text("print('concurrent')")
            
            # 并发执行多个搜索请求
            tasks = []
            for _ in range(5):
                task = asyncio.create_task(workspace_index.search("concurrent_test", ""))
                tasks.append(task)
            
            # 等待所有任务完成
            results_list = await asyncio.gather(*tasks)
            
            # 所有任务都应该返回相同的结果
            for results in results_list:
                assert len(results) == 1
                assert results[0].path == "concurrent_test.py"

    @pytest.mark.asyncio
    async def test_search_lru_management(self) -> None:
        """测试LRU管理"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建多个测试文件
            for i in range(15):  # 超过默认LRU限制
                (Path(temp_dir) / f"file_{i}.py").write_text(f"print('file_{i}')")
            
            # 设置较小的LRU限制用于测试
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            workspace_index.DYNAMIC_NODES_LRU_LIMIT = 10
            
            await workspace_index.initialize()
            
            # 访问前面的文件，使它们进入LRU
            for i in range(12):
                results = await workspace_index.search(f"file_{i}", "")
                assert len(results) == 1
            
            # 检查LRU是否正确淘汰了最久未访问的节点
            # 前面的文件应该还在LRU中
            results = await workspace_index.search("file_11", "")
            assert len(results) == 1
            
            # 最老的文件可能已经被淘汰
            results = await workspace_index.search("file_0", "")
            # 可能被淘汰，所以不断言结果数量

    @pytest.mark.asyncio
    async def test_search_case_insensitive(self) -> None:
        """测试大小写不敏感搜索"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试文件
            (Path(temp_dir) / "Main.py").write_text("print('Main')")
            (Path(temp_dir) / "utils.py").write_text("print('utils')")
            (Path(temp_dir) / "CONFIG.json").write_text("{}")
            
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            await workspace_index.initialize()
            
            # 测试大小写不敏感搜索
            test_cases = [
                ("main", "", ["Main.py"]),
                ("MAIN", "", ["Main.py"]),
                ("utils", "", ["utils.py"]),
                ("config", "", ["CONFIG.json"]),
                ("Config", "", ["CONFIG.json"]),
            ]
            
            for query, context, expected_paths in test_cases:
                results = await workspace_index.search(query, context)
                paths = [node.path for node in results]
                for expected_path in expected_paths:
                    assert expected_path in paths, f"Case insensitive search failed for query: {query}"

    @pytest.mark.asyncio
    async def test_search_with_gitignore(self) -> None:
        """测试带.gitignore的搜索"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试文件
            (Path(temp_dir) / "included.py").write_text("print('included')")
            (Path(temp_dir) / "excluded.py").write_text("print('excluded')")
            (Path(temp_dir) / "temp.log").write_text("log content")
            
            # 创建.gitignore文件
            gitignore_content = "*.log\nexcluded.py\n"
            (Path(temp_dir) / ".gitignore").write_text(gitignore_content)
            
            workspace_index = WorkspaceIndex(root_path=temp_dir, use_gitignore=True)
            await workspace_index.initialize()
            
            # 测试被包含的文件
            results = await workspace_index.search("included", "")
            assert len(results) == 1
            assert results[0].path == "included.py"
            
            # 测试被排除的文件
            results = await workspace_index.search("excluded", "")
            assert len(results) == 0
            
            results = await workspace_index.search("temp", "")
            assert len(results) == 0

    @pytest.mark.asyncio
    async def test_search_core_patterns(self) -> None:
        """测试核心模式搜索"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试文件
            (Path(temp_dir) / "core_file.py").write_text("print('core')")
            (Path(temp_dir) / "normal_file.py").write_text("print('normal')")
            (Path(temp_dir) / "another_file.txt").write_text("text content")
            
            # 设置核心模式
            workspace_index = WorkspaceIndex(
                root_path=temp_dir,
                core_patterns=["*.py"],  # 所有.py文件都是核心文件
            )
            await workspace_index.initialize()
            
            # 测试核心文件
            results = await workspace_index.search("core_file", "")
            assert len(results) == 1
            assert results[0].path == "core_file.py"
            assert results[0].is_core is True
            
            results = await workspace_index.search("normal_file", "")
            assert len(results) == 1
            assert results[0].path == "normal_file.py"
            assert results[0].is_core is True
            
            # 测试非核心文件
            results = await workspace_index.search("another_file", "")
            assert len(results) == 1
            assert results[0].path == "another_file.txt"
            assert results[0].is_core is False