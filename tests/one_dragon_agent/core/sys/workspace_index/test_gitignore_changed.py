import pytest
import tempfile
import asyncio
from pathlib import Path
from unittest.mock import patch, MagicMock
from one_dragon_agent.core.sys.workspace_index import WorkspaceIndex


class TestGitIgnoreChanged:
    """测试.gitignore变更处理功能"""

    @pytest.mark.asyncio
    async def test_handle_gitignore_changed(self) -> None:
        """测试.gitignore变更处理"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建WorkspaceIndex实例
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            
            # Mock _construct_gitignore_pathspec 和 _rescan_index_for_ignore_rules 方法
            async def mock_construct_gitignore():
                return None

            workspace_index._construct_gitignore_pathspec = MagicMock(side_effect=mock_construct_gitignore)
            workspace_index._rescan_index_for_ignore_rules = MagicMock()

            # 调用_handle_gitignore_changed（异步方法）
            await workspace_index._handle_gitignore_changed()
            
            # 验证_construct_gitignore_pathspec被调用
            # 注意：在实际实现中，这会在异步任务中被调用，所以这里只是验证方法存在

    @pytest.mark.asyncio
    async def test_rescan_index_for_ignore_rules_with_ignored_files(self) -> None:
        """测试重新扫描索引以应用忽略规则 - 包含被忽略的文件"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试文件
            temp_path = Path(temp_dir)
            test_file = temp_path / "test.py"
            test_file.write_text("print('hello')")
            
            # 创建WorkspaceIndex实例（先不使用gitignore）
            workspace_index = WorkspaceIndex(root_path=temp_dir, use_gitignore=False)
            
            # 初始化索引
            await workspace_index.initialize()
            
            # 验证.py文件在索引中
            assert "test.py" in workspace_index.index_data.path_to_node
            
            # 创建.gitignore文件
            gitignore_file = temp_path / ".gitignore"
            gitignore_file.write_text("*.py")
            
            # 重新设置gitignore规则并重新扫描
            workspace_index.use_gitignore = True
            await workspace_index._construct_gitignore_pathspec()
            
            # 重新扫描索引（同步方法）
            workspace_index._rescan_index_for_ignore_rules()
            
            # 验证被忽略的节点已被移除
            assert "test.py" not in workspace_index.index_data.path_to_node
            
    @pytest.mark.asyncio
    async def test_rescan_index_for_ignore_rules_without_ignored_files(self) -> None:
        """测试重新扫描索引以应用忽略规则 - 不包含被忽略的文件"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试文件
            temp_path = Path(temp_dir)
            test_file = temp_path / "test.txt"
            test_file.write_text("hello")
            
            # 创建.gitignore文件，忽略.py文件
            gitignore_file = temp_path / ".gitignore"
            gitignore_file.write_text("*.py")
            
            # 创建WorkspaceIndex实例
            workspace_index = WorkspaceIndex(root_path=temp_dir, use_gitignore=True)
            
            # 初始化索引
            await workspace_index.initialize()
            
            # 验证.txt文件在索引中
            assert "test.txt" in workspace_index.index_data.path_to_node
            
            # 重新扫描索引（同步方法）
            workspace_index._rescan_index_for_ignore_rules()
            
            # 验证.txt文件仍在索引中（因为它不被忽略）
            assert "test.txt" in workspace_index.index_data.path_to_node

    @pytest.mark.asyncio
    async def test_rescan_index_for_ignore_rules_with_core_files(self) -> None:
        """测试重新扫描索引以应用忽略规则 - 核心文件"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试文件
            temp_path = Path(temp_dir)
            test_file = temp_path / "test.py"
            test_file.write_text("print('hello')")
            
            # 创建.gitignore文件
            gitignore_file = temp_path / ".gitignore"
            gitignore_file.write_text("*.py")
            
            # 创建WorkspaceIndex实例，将.py文件标记为核心文件
            workspace_index = WorkspaceIndex(
                root_path=temp_dir, 
                core_patterns=["*.py"],
                use_gitignore=True
            )
            
            # 初始化索引
            await workspace_index.initialize()
            
            # 验证.py文件在索引中且标记为核心文件
            assert "test.py" in workspace_index.index_data.path_to_node
            assert workspace_index.index_data.path_to_node["test.py"].is_core is True
            
            # 重新扫描索引（同步方法）
            workspace_index._rescan_index_for_ignore_rules()
            
            # 验证核心文件仍在索引中（核心文件不被忽略规则影响）
            assert "test.py" in workspace_index.index_data.path_to_node