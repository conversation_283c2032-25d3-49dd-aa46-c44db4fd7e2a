import pytest
import tempfile
import os
from pathlib import Path
from unittest.mock import patch
from one_dragon_agent.core.sys.workspace_index import WorkspaceIndex


class TestWorkspaceIndex:
    """测试WorkspaceIndex类"""

    def test_workspace_index_init_valid_path(self) -> None:
        """测试使用有效路径初始化WorkspaceIndex"""
        # 使用临时目录作为根路径
        with tempfile.TemporaryDirectory() as temp_dir:
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            
            assert workspace_index.root_path == os.path.abspath(temp_dir)
            assert workspace_index.core_patterns == []
            assert workspace_index.ignore_patterns == []
            assert workspace_index.use_gitignore is True
            assert workspace_index._initialized is False
            assert workspace_index._initializing is False

    def test_workspace_index_init_with_patterns(self) -> None:
        """测试使用模式初始化WorkspaceIndex"""
        with tempfile.TemporaryDirectory() as temp_dir:
            core_patterns = ["*.py", "src/"]
            ignore_patterns = ["*.tmp", "temp/"]
            
            workspace_index = WorkspaceIndex(
                root_path=temp_dir,
                core_patterns=core_patterns,
                ignore_patterns=ignore_patterns,
                use_gitignore=False
            )
            
            assert workspace_index.core_patterns == core_patterns
            assert workspace_index.ignore_patterns == ignore_patterns
            assert workspace_index.use_gitignore is False

    def test_workspace_index_init_invalid_path(self) -> None:
        """测试使用无效路径初始化WorkspaceIndex"""
        # 测试不存在的路径
        with pytest.raises(ValueError, match="根路径不存在"):
            WorkspaceIndex(root_path="/non/existent/path")
        
        # 测试文件路径而不是目录路径
        with tempfile.NamedTemporaryFile() as temp_file:
            with pytest.raises(ValueError, match="根路径不是一个目录"):
                WorkspaceIndex(root_path=temp_file.name)
