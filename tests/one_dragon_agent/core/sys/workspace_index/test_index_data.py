import pytest
from one_dragon_agent.core.sys.workspace_index import IndexData
from one_dragon_agent.core.algo.trie import Trie


class TestIndexData:
    """测试IndexData类"""

    def test_index_data_initialization(self) -> None:
        """测试IndexData的初始化"""
        index_data = IndexData()
        
        # 检查所有属性是否正确初始化
        assert isinstance(index_data.path_to_node, dict)
        assert len(index_data.path_to_node) == 0
        
        assert isinstance(index_data.path_trie, <PERSON>e)
        assert isinstance(index_data.name_trie, <PERSON><PERSON>)
        
        from collections import OrderedDict
        assert isinstance(index_data.dynamic_nodes_lru, OrderedDict)
        assert len(index_data.dynamic_nodes_lru) == 0

    def test_index_data_attributes_exist(self) -> None:
        """测试IndexData的所有属性是否存在"""
        index_data = IndexData()
        
        # 检查所有必要的属性是否存在
        assert hasattr(index_data, 'path_to_node')
        assert hasattr(index_data, 'path_trie')
        assert hasattr(index_data, 'name_trie')
        assert hasattr(index_data, 'dynamic_nodes_lru')