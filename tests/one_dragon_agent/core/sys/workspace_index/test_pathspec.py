import pytest
import tempfile
import os
from pathlib import Path
from one_dragon_agent.core.sys.workspace_index import WorkspaceIndex


class TestWorkspaceIndexPathSpecs:
    """测试WorkspaceIndex的PathSpec构造功能"""

    @pytest.mark.asyncio
    async def test_construct_pathspecs_without_gitignore(self) -> None:
        """测试不使用gitignore构造PathSpec"""
        with tempfile.TemporaryDirectory() as temp_dir:
            core_patterns = ["*.py", "src/"]
            ignore_patterns = ["*.tmp", "temp/"]
            
            workspace_index = WorkspaceIndex(
                root_path=temp_dir,
                core_patterns=core_patterns,
                ignore_patterns=ignore_patterns,
                use_gitignore=False
            )
            
            # 构造PathSpec
            await workspace_index._construct_pathspecs()
            
            # 检查PathSpec对象是否已创建
            assert workspace_index._core_pathspec is not None
            assert workspace_index._ignore_pathspec_static is not None
            assert workspace_index._ignore_pathspec_git is None

    @pytest.mark.asyncio
    async def test_construct_pathspecs_with_gitignore(self) -> None:
        """测试使用gitignore构造PathSpec"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建一个.gitignore文件
            gitignore_path = Path(temp_dir) / ".gitignore"
            gitignore_path.write_text("*.log\n.temp/\n")
            
            core_patterns = ["*.py", "src/"]
            ignore_patterns = ["*.tmp", "temp/"]
            
            workspace_index = WorkspaceIndex(
                root_path=temp_dir,
                core_patterns=core_patterns,
                ignore_patterns=ignore_patterns,
                use_gitignore=True
            )
            
            # 构造PathSpec
            await workspace_index._construct_pathspecs()
            
            # 检查PathSpec对象是否已创建
            assert workspace_index._core_pathspec is not None
            assert workspace_index._ignore_pathspec_static is not None
            assert workspace_index._ignore_pathspec_git is not None

    @pytest.mark.asyncio
    async def test_construct_gitignore_pathspec(self) -> None:
        """测试构造.gitignore PathSpec"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建多层目录结构和.gitignore文件
            src_dir = Path(temp_dir) / "src"
            src_dir.mkdir()
            
            # 在根目录创建.gitignore
            root_gitignore = Path(temp_dir) / ".gitignore"
            root_gitignore.write_text("*.log\n.temp/\n")
            
            # 在src目录创建.gitignore
            src_gitignore = src_dir / ".gitignore"
            src_gitignore.write_text("*.tmp\nbuild/\n")
            
            workspace_index = WorkspaceIndex(
                root_path=temp_dir,
                use_gitignore=True
            )
            
            # 构造.gitignore PathSpec
            await workspace_index._construct_gitignore_pathspec()
            
            # 检查PathSpec对象是否已创建
            assert workspace_index._ignore_pathspec_git is not None