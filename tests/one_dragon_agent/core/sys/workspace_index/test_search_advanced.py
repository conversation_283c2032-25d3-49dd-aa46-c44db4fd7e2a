import pytest
import tempfile
import os
import asyncio
from pathlib import Path
from one_dragon_agent.core.sys.workspace_index import WorkspaceIndex


class TestSearchAdvanced:
    """测试WorkspaceIndex的高级搜索功能"""

    @pytest.mark.asyncio
    async def test_search_path_normalization_complex(self) -> None:
        """测试复杂路径规范化处理"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试目录结构
            src_dir = Path(temp_dir) / "src"
            src_dir.mkdir()
            (src_dir / "main.py").write_text("print('main')")
            
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            await workspace_index.initialize()
            
            # 测试复杂的路径规范化场景
            test_cases = [
                # (query, context, expected_results)
                ("src/main", "", ["src/main.py"]),  # 标准路径
                ("src\\main", "", ["src/main.py"]),  # 反斜杠
                ("/src/main", "", ["src/main.py"]),  # 前导斜杠
                ("src/main/", "", []),  # 结尾斜杠 - 这是文件，不是目录，所以应该返回空
                ("./src/main", "", ["src/main.py"]),  # 当前目录
                ("src/./main", "", ["src/main.py"]),  # 中间当前目录
                ("main", "src", ["src/main.py"]),  # 上下文路径
                ("main", "src/", ["src/main.py"]),  # 上下文路径带斜杠
                ("main", "/src", ["src/main.py"]),  # 上下文路径前导斜杠
            ]
            
            for query, context, expected_paths in test_cases:
                results = await workspace_index.search(query, context)
                paths = [node.path for node in results]
                for expected_path in expected_paths:
                    assert expected_path in paths, f"Failed for query: '{query}', context: '{context}'"

    @pytest.mark.asyncio
    async def test_search_security_validation_comprehensive(self) -> None:
        """测试全面的安全性验证"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试文件
            (Path(temp_dir) / "safe.py").write_text("print('safe')")
            
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            await workspace_index.initialize()
            
            # 测试各种路径遍历攻击
            malicious_test_cases = [
                # 查询路径遍历
                ("../etc/passwd", ""),
                ("..\\windows\\system32", ""),
                ("src/../../../etc/passwd", ""),
                ("./../etc/passwd", ""),
                
                # 上下文路径遍历
                ("safe.py", "../etc"),
                ("safe.py", "..\\windows"),
                ("safe.py", "src/../../../etc"),
                
                # 混合攻击
                ("../safe.py", "../etc"),
                ("..\\safe.py", "..\\windows"),
                
                # 绝对路径攻击
                ("/etc/passwd", ""),
                ("C:\\Windows\\System32", ""),
                ("safe.py", "/etc"),
                
                # 多层嵌套攻击
                ("a/../../etc/passwd", ""),
                ("safe.py", "b/../../etc"),
            ]
            
            for query, context in malicious_test_cases:
                results = await workspace_index.search(query, context)
                assert len(results) == 0, f"Security validation failed for query: '{query}', context: '{context}'"

    @pytest.mark.asyncio
    async def test_search_initialization_states_detailed(self) -> None:
        """测试详细的初始化状态处理"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试文件
            (Path(temp_dir) / "core_file.py").write_text("print('core')")
            (Path(temp_dir) / "dynamic_file.py").write_text("print('dynamic')")
            
            # 设置核心模式
            workspace_index = WorkspaceIndex(
                root_path=temp_dir,
                core_patterns=["core_file.py"],  # 只有core_file.py是核心文件
            )
            
            # 测试未初始化状态
            results = await workspace_index.search("core_file", "")
            assert len(results) == 0
            
            # 开始异步初始化但不等待完成
            init_task = asyncio.create_task(workspace_index.initialize())
            
            # 等待一小段时间确保初始化已开始
            await asyncio.sleep(0.01)  # 减少等待时间，让初始化仍在进行中
            
            # 测试初始化中的状态 - 应该能找到核心文件
            results = await workspace_index.search("core_file", "")
            assert len(results) == 1
            assert results[0].path == "core_file.py"
            assert results[0].is_core is True
            
            # 测试初始化中的状态 - 不应该能找到非核心文件
            # 注意：由于初始化速度很快，这个测试可能会不稳定
            # 我们只检查如果找到了结果，它不应该被标记为核心文件
            results = await workspace_index.search("dynamic_file", "")
            if results:  # 如果找到了结果（初始化可能已经完成）
                assert results[0].is_core is False  # 确保它不被标记为核心文件
            # 如果没有找到结果，那也是正确的（初始化仍在进行中）
            
            # 等待初始化完成
            await init_task
            
            # 测试初始化完成后的状态 - 应该能找到所有文件
            results = await workspace_index.search("dynamic_file", "")
            assert len(results) == 1
            assert results[0].path == "dynamic_file.py"
            assert results[0].is_core is False

    @pytest.mark.asyncio
    async def test_search_fallback_scan_mechanism(self) -> None:
        """测试回退扫描机制"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建初始文件
            (Path(temp_dir) / "existing.py").write_text("print('existing')")
            
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            await workspace_index.initialize()
            
            # 在索引构建后创建新文件和目录
            new_dir = Path(temp_dir) / "new_dir"
            new_dir.mkdir()
            (new_dir / "new_file.py").write_text("print('new')")
            (Path(temp_dir) / "new_global_file.py").write_text("print('global')")
            
            # 测试目录单层扫描回退
            results = await workspace_index.search("new_dir/new_file", "")
            assert len(results) == 1
            assert results[0].path == "new_dir/new_file.py"
            
            # 测试全局扫描回退
            results = await workspace_index.search("new_global_file", "")
            assert len(results) == 1
            assert results[0].path == "new_global_file.py"

    @pytest.mark.asyncio
    async def test_search_concurrent_scan_locking(self) -> None:
        """测试并发扫描的锁机制"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建初始文件
            (Path(temp_dir) / "existing.py").write_text("print('existing')")
            
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            await workspace_index.initialize()
            
            # 在索引构建后创建新文件
            new_file = Path(temp_dir) / "concurrent_test.py"
            new_file.write_text("print('concurrent')")
            
            # 并发执行多个搜索请求
            async def single_search():
                return await workspace_index.search("concurrent_test", "")
            
            # 创建多个并发搜索任务
            tasks = [single_search() for _ in range(10)]
            results_list = await asyncio.gather(*tasks)
            
            # 验证所有任务都返回了正确结果
            for i, results in enumerate(results_list):
                assert len(results) == 1, f"Task {i} failed"
                assert results[0].path == "concurrent_test.py", f"Task {i} returned wrong result"

    @pytest.mark.asyncio
    async def test_search_lru_eviction(self) -> None:
        """测试LRU淘汰机制"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建多个测试文件，使用不会产生前缀冲突的名称
            file_names = [f"unique_file_{i:03d}.py" for i in range(15)]  # 超过默认LRU限制
            for file_name in file_names:
                (Path(temp_dir) / file_name).write_text(f"print('{file_name}')")
            
            # 设置较小的LRU限制用于测试
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            workspace_index.DYNAMIC_NODES_LRU_LIMIT = 5
            
            await workspace_index.initialize()
            
            # 搜索前几个文件，使它们进入LRU
            for i in range(8):
                results = await workspace_index.search(f"unique_file_{i:03d}", "")
                assert len(results) == 1
                assert results[0].path == f"unique_file_{i:03d}.py"
            
            # 检查LRU大小
            assert len(workspace_index.index_data.dynamic_nodes_lru) <= 5
            
            # 访问后面的文件，触发淘汰
            for i in range(8, 12):
                results = await workspace_index.search(f"unique_file_{i:03d}", "")
                assert len(results) == 1
                assert results[0].path == f"unique_file_{i:03d}.py"
            
            # 验证LRU淘汰机制正常工作
            assert len(workspace_index.index_data.dynamic_nodes_lru) <= 5
            
            # 验证最近访问的文件仍在LRU中
            recent_results = await workspace_index.search("unique_file_011", "")
            assert len(recent_results) == 1
            assert recent_results[0].path == "unique_file_011.py"

    @pytest.mark.asyncio
    async def test_search_case_insensitive_comprehensive(self) -> None:
        """测试全面的大小写不敏感搜索"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建各种大小写的测试文件
            test_files = [
                "Main.py",
                "utils.py", 
                "CONFIG.json",
                "ReadMe.md",
                "Test_File.txt"
            ]
            
            for filename in test_files:
                (Path(temp_dir) / filename).write_text(f"Content of {filename}")
            
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            await workspace_index.initialize()
            
            # 测试各种大小写组合的搜索
            test_cases = [
                # (query, expected_matches)
                ("main", ["Main.py"]),
                ("MAIN", ["Main.py"]),
                ("Main", ["Main.py"]),
                ("utils", ["utils.py"]),
                ("UTILS", ["utils.py"]),
                ("config", ["CONFIG.json"]),
                ("Config", ["CONFIG.json"]),
                ("CONFIG", ["CONFIG.json"]),
                ("readme", ["ReadMe.md"]),
                ("README", ["ReadMe.md"]),
                ("test_file", ["Test_File.txt"]),
                ("TEST_FILE", ["Test_File.txt"]),
            ]
            
            for query, expected_matches in test_cases:
                results = await workspace_index.search(query, "")
                paths = [node.path for node in results]
                
                for expected_path in expected_matches:
                    assert expected_path in paths, f"Case insensitive search failed for query: '{query}'"

    @pytest.mark.asyncio
    async def test_search_with_complex_gitignore(self) -> None:
        """测试复杂.gitignore规则的搜索"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建复杂的目录结构
            src_dir = Path(temp_dir) / "src"
            src_dir.mkdir()
            
            test_dir = Path(temp_dir) / "test"
            test_dir.mkdir()
            
            # 创建各种文件
            files_to_create = [
                "src/main.py",
                "src/utils.py", 
                "src/temp.log",
                "test/test_main.py",
                "test/temp.tmp",
                "README.md",
                ".env",
                "build/output.o",
                "dist/app.exe"
            ]
            
            for file_path in files_to_create:
                full_path = Path(temp_dir) / file_path
                full_path.parent.mkdir(parents=True, exist_ok=True)
                full_path.write_text(f"Content of {file_path}")
            
            # 创建复杂的.gitignore文件
            gitignore_content = """# 忽略日志文件
*.log

# 忽略临时文件
temp/
*.tmp

# 忽略构建产物
build/
dist/

# 忽略环境文件
.env

# 但不忽略test目录下的临时文件
!test/temp.tmp
"""
            
            (Path(temp_dir) / ".gitignore").write_text(gitignore_content)
            
            workspace_index = WorkspaceIndex(root_path=temp_dir, use_gitignore=True)
            await workspace_index.initialize()
            
            # 测试被包含的文件
            included_files = [
                "src/main.py",
                "src/utils.py",
                "test/test_main.py",
                "test/temp.tmp",  # 被否定规则包含
                "README.md"
            ]
            
            for file_path in included_files:
                filename = Path(file_path).name
                results = await workspace_index.search(filename, "")
                assert len(results) > 0, f"File {file_path} should be included"
                assert any(node.path == file_path for node in results)
            
            # 测试被排除的文件
            excluded_files = [
                "src/temp.log",
                "test/temp.tmp",  # 这个应该被包含，因为有否定规则
                ".env",
                "build/output.o",
                "dist/app.exe"
            ]
            
            # 注意：test/temp.tmp 应该被包含，所以从排除列表中移除
            truly_excluded = [
                "src/temp.log",
                ".env", 
                "build/output.o",
                "dist/app.exe"
            ]
            
            for file_path in truly_excluded:
                filename = Path(file_path).name
                results = await workspace_index.search(filename, "")
                # 这些文件应该被忽略，不在索引中
                assert not any(node.path == file_path for node in results), f"File {file_path} should be excluded"

    @pytest.mark.asyncio
    async def test_search_core_patterns_comprehensive(self) -> None:
        """测试全面的核心模式功能"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建复杂的目录结构
            src_dir = Path(temp_dir) / "src"
            src_dir.mkdir()
            
            lib_dir = Path(temp_dir) / "lib"
            lib_dir.mkdir()
            
            # 创建各种文件
            files_to_create = [
                "src/main.py",
                "src/utils.py",
                "src/config.json",
                "lib/helper.py",
                "lib/data.json",
                "README.md",
                "LICENSE.txt",
                "setup.py"
            ]
            
            for file_path in files_to_create:
                full_path = Path(temp_dir) / file_path
                full_path.parent.mkdir(parents=True, exist_ok=True)
                full_path.write_text(f"Content of {file_path}")
            
            # 设置复杂的核心模式
            workspace_index = WorkspaceIndex(
                root_path=temp_dir,
                core_patterns=[
                    "*.py",           # 所有Python文件
                    "src/",           # src目录下的所有文件
                    "!src/config.json",  # 但排除src/config.json
                    "README.md"       # README文件
                ]
            )
            
            await workspace_index.initialize()
            
            # 测试核心文件
            core_files = [
                "src/main.py",
                "src/utils.py", 
                "lib/helper.py",
                "setup.py",
                "README.md"
            ]
            
            for file_path in core_files:
                results = await workspace_index.search(Path(file_path).name, "")
                matching_nodes = [node for node in results if node.path == file_path]
                assert len(matching_nodes) == 1, f"File {file_path} should be core"
                assert matching_nodes[0].is_core is True
            
            # 测试非核心文件
            non_core_files = [
                "src/config.json",  # 被否定规则排除
                "lib/data.json",
                "LICENSE.txt"
            ]
            
            for file_path in non_core_files:
                results = await workspace_index.search(Path(file_path).name, "")
                matching_nodes = [node for node in results if node.path == file_path]
                assert len(matching_nodes) == 1, f"File {file_path} should exist"
                assert matching_nodes[0].is_core is False

    @pytest.mark.asyncio
    async def test_search_performance_with_large_index(self) -> None:
        """测试大索引的搜索性能"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建大量文件
            num_files = 100
            for i in range(num_files):
                (Path(temp_dir) / f"file_{i:03d}.py").write_text(f"print('file_{i}')")
            
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            
            # 测试初始化性能
            import time
            start_time = time.time()
            await workspace_index.initialize()
            init_time = time.time() - start_time
            
            # 初始化应该在合理时间内完成
            assert init_time < 5.0, f"Initialization took too long: {init_time}s"
            
            # 测试搜索性能
            start_time = time.time()
            for i in range(0, num_files, 10):  # 搜索每10个文件
                results = await workspace_index.search(f"file_{i:03d}", "")
                assert len(results) == 1
            search_time = time.time() - start_time
            
            # 搜索应该在合理时间内完成
            assert search_time < 2.0, f"Search took too long: {search_time}s"

    @pytest.mark.asyncio
    async def test_search_edge_cases(self) -> None:
        """测试边界情况"""
        with tempfile.TemporaryDirectory() as temp_dir:
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            await workspace_index.initialize()
            
            # 测试空字符串查询
            results = await workspace_index.search("", "")
            assert len(results) == 0
            
            # 测试只有空格的查询
            results = await workspace_index.search("   ", "")
            assert len(results) == 0
            
            # 测试特殊字符查询
            special_chars = ["*", "?", "[", "]", "{", "}", "(", ")", "$", "^"]
            for char in special_chars:
                results = await workspace_index.search(char, "")
                # 特殊字符搜索不应该崩溃
                assert isinstance(results, list)
            
            # 测试超长查询
            long_query = "a" * 1000
            results = await workspace_index.search(long_query, "")
            assert isinstance(results, list)
            
            # 测试Unicode字符
            unicode_query = "测试文件"
            results = await workspace_index.search(unicode_query, "")
            assert isinstance(results, list)

    @pytest.mark.asyncio
    async def test_search_directory_listing_edge_cases(self) -> None:
        """测试目录列出的边界情况"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建空目录
            empty_dir = Path(temp_dir) / "empty"
            empty_dir.mkdir()
            
            # 创建包含文件的目录
            full_dir = Path(temp_dir) / "full"
            full_dir.mkdir()
            (full_dir / "file1.py").write_text("print('file1')")
            (full_dir / "file2.py").write_text("print('file2')")
            
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            await workspace_index.initialize()
            
            # 测试空目录列出
            results = await workspace_index.search("empty/", "")
            assert len(results) == 0
            
            # 测试非空目录列出
            results = await workspace_index.search("full/", "")
            assert len(results) == 2
            names = [node.name for node in results]
            assert "file1.py" in names
            assert "file2.py" in names
            
            # 测试不存在的目录列出
            results = await workspace_index.search("nonexistent/", "")
            assert len(results) == 0
            
            # 测试文件作为目录列出（应该返回空）
            results = await workspace_index.search("full/file1.py/", "")
            assert len(results) == 0