import pytest
import tempfile
from pathlib import Path
from one_dragon_agent.core.sys.workspace_index import WorkspaceIndex, IndexNode


class TestWorkspaceIndexCoreBuilding:
    """测试WorkspaceIndex的核心索引构建功能"""

    @pytest.mark.asyncio
    async def test_build_core_index_empty_directory(self) -> None:
        """测试在空目录中构建核心索引"""
        with tempfile.TemporaryDirectory() as temp_dir:
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            
            # 构造PathSpec
            await workspace_index._construct_pathspecs()
            
            # 构建核心索引
            await workspace_index._build_core_index()
            
            # 检查索引是否为空（除了根节点）
            # 根节点应该被创建
            assert "" in workspace_index.index_data.path_to_node
            root_node = workspace_index.index_data.path_to_node[""]
            assert root_node.name == ""
            assert root_node.path == ""
            assert root_node.is_dir is True

    @pytest.mark.asyncio
    async def test_build_core_index_with_files(self) -> None:
        """测试在包含文件的目录中构建核心索引"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试文件和目录结构
            temp_path = Path(temp_dir)
            
            # 创建src目录和文件
            src_dir = temp_path / "src"
            src_dir.mkdir()
            
            test_file = src_dir / "test.py"
            test_file.write_text("print('hello')\n")
            
            # 创建README文件
            readme_file = temp_path / "README.md"
            readme_file.write_text("# Test Project\n")
            
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            
            # 构造PathSpec
            await workspace_index._construct_pathspecs()
            
            # 构建核心索引
            await workspace_index._build_core_index()
            
            # 检查所有文件和目录是否都被索引
            assert "src" in workspace_index.index_data.path_to_node
            assert "src/test.py" in workspace_index.index_data.path_to_node
            assert "README.md" in workspace_index.index_data.path_to_node
            
            # 检查节点属性
            src_node = workspace_index.index_data.path_to_node["src"]
            assert src_node.name == "src"
            assert src_node.is_dir is True
            assert src_node.is_core is False  # 默认不是核心文件
            
            test_file_node = workspace_index.index_data.path_to_node["src/test.py"]
            assert test_file_node.name == "test.py"
            assert test_file_node.is_dir is False
            assert test_file_node.is_core is False
            
            readme_node = workspace_index.index_data.path_to_node["README.md"]
            assert readme_node.name == "README.md"
            assert readme_node.is_dir is False
            assert readme_node.is_core is False

    @pytest.mark.asyncio
    async def test_build_core_index_with_core_patterns(self) -> None:
        """测试使用核心模式构建核心索引"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试文件和目录结构
            temp_path = Path(temp_dir)
            
            # 创建src目录和文件
            src_dir = temp_path / "src"
            src_dir.mkdir()
            
            test_file = src_dir / "test.py"
            test_file.write_text("print('hello')\n")
            
            # 创建README文件
            readme_file = temp_path / "README.md"
            readme_file.write_text("# Test Project\n")
            
            # 使用核心模式
            workspace_index = WorkspaceIndex(
                root_path=temp_dir,
                core_patterns=["*.py", "src/"]
            )
            
            # 构造PathSpec
            await workspace_index._construct_pathspecs()
            
            # 构建核心索引
            await workspace_index._build_core_index()
            
            # 检查Python文件和src目录是否被标记为核心
            src_node = workspace_index.index_data.path_to_node["src"]
            assert src_node.is_core is True
            
            test_file_node = workspace_index.index_data.path_to_node["src/test.py"]
            assert test_file_node.is_core is True
            
            # README文件不应该被标记为核心
            readme_node = workspace_index.index_data.path_to_node["README.md"]
            assert readme_node.is_core is False

    @pytest.mark.asyncio
    async def test_match_pathspec(self) -> None:
        """测试路径匹配功能"""
        with tempfile.TemporaryDirectory() as temp_dir:
            workspace_index = WorkspaceIndex(
                root_path=temp_dir,
                core_patterns=["*.py"],
                ignore_patterns=["*.tmp"]
            )
            
            # 构造PathSpec
            await workspace_index._construct_pathspecs()
            
            # 测试核心文件匹配
            should_index, is_core = workspace_index._match_pathspec("test.py", False)
            assert should_index is True
            assert is_core is True
            
            # 测试忽略文件匹配
            should_index, is_core = workspace_index._match_pathspec("temp.tmp", False)
            assert should_index is False
            assert is_core is False
            
            # 测试普通文件
            should_index, is_core = workspace_index._match_pathspec("README.md", False)
            assert should_index is True
            assert is_core is False