import pytest
import tempfile
from pathlib import Path
from one_dragon_agent.core.sys.workspace_index import WorkspaceIndex


class TestGitIgnorePathSpec:
    """测试.gitignore PathSpec构造功能"""

    @pytest.mark.asyncio
    async def test_construct_gitignore_pathspec_with_subdir(self) -> None:
        """测试在子目录中构造.gitignore PathSpec"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建目录结构
            temp_path = Path(temp_dir)
            src_dir = temp_path / "src"
            src_dir.mkdir()
            
            # 在根目录创建.gitignore
            root_gitignore = temp_path / ".gitignore"
            root_gitignore.write_text("*.log\n.temp/\n")
            
            # 在src目录创建.gitignore
            src_gitignore = src_dir / ".gitignore"
            src_gitignore.write_text("*.tmp\nbuild/\n")
            
            workspace_index = WorkspaceIndex(root_path=temp_dir, use_gitignore=True)

            # 构造基础PathSpec（同步）
            workspace_index._construct_pathspecs()

            # 构造gitignore PathSpec（异步）
            await workspace_index._construct_gitignore_pathspec()
            
            # 检查PathSpec对象是否已创建
            assert workspace_index._ignore_pathspec_git is not None
            
            # 测试根目录的规则是否生效
            assert workspace_index._ignore_pathspec_git.match_file("test.log") is True
            assert workspace_index._ignore_pathspec_git.match_file(".temp/file.txt") is True
            
            # 测试src目录的规则是否生效
            assert workspace_index._ignore_pathspec_git.match_file("src/test.tmp") is True
            assert workspace_index._ignore_pathspec_git.match_file("src/build/output.txt") is True
            
            # 确保src目录的规则不会影响根目录
            assert workspace_index._ignore_pathspec_git.match_file("test.tmp") is False
            assert workspace_index._ignore_pathspec_git.match_file("build/output.txt") is False

    @pytest.mark.asyncio
    async def test_construct_gitignore_pathspec_with_nested_dirs(self) -> None:
        """测试在嵌套目录中构造.gitignore PathSpec"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建嵌套目录结构
            temp_path = Path(temp_dir)
            nested_dir = temp_path / "src" / "core" / "utils"
            nested_dir.mkdir(parents=True)
            
            # 在嵌套目录创建.gitignore
            nested_gitignore = nested_dir / ".gitignore"
            nested_gitignore.write_text("*.cache\n")
            
            workspace_index = WorkspaceIndex(root_path=temp_dir, use_gitignore=True)

            # 构造基础PathSpec（同步）
            workspace_index._construct_pathspecs()

            # 构造gitignore PathSpec（异步）
            await workspace_index._construct_gitignore_pathspec()
            
            # 检查PathSpec对象是否已创建
            assert workspace_index._ignore_pathspec_git is not None
            
            # 测试嵌套目录的规则是否生效
            assert workspace_index._ignore_pathspec_git.match_file("src/core/utils/test.cache") is True
            
            # 确保规则不会影响其他目录
            assert workspace_index._ignore_pathspec_git.match_file("test.cache") is False
            assert workspace_index._ignore_pathspec_git.match_file("src/test.cache") is False