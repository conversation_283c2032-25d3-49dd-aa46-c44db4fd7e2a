import pytest
import tempfile
import asyncio
from pathlib import Path
from one_dragon_agent.core.sys.workspace_index import WorkspaceIndex


class TestDirectoryDeletion:
    """测试目录删除功能"""

    @pytest.mark.asyncio
    async def test_remove_node_and_children_with_nested_files(self) -> None:
        """测试递归删除节点及其所有子节点 - 包含嵌套文件"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建嵌套目录结构
            temp_path = Path(temp_dir)
            nested_dir = temp_path / "dir1" / "dir2" / "dir3"
            nested_dir.mkdir(parents=True)
            
            # 创建文件
            file1 = temp_path / "file1.txt"
            file1.write_text("content1")
            
            file2 = temp_path / "dir1" / "file2.txt"
            file2.write_text("content2")
            
            file3 = nested_dir / "file3.txt"
            file3.write_text("content3")
            
            # 创建WorkspaceIndex实例
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            
            # 初始化索引
            await workspace_index.initialize()
            
            # 验证所有文件和目录都在索引中
            assert "file1.txt" in workspace_index.index_data.path_to_node
            assert "dir1" in workspace_index.index_data.path_to_node
            assert "dir1/file2.txt" in workspace_index.index_data.path_to_node
            assert "dir1/dir2" in workspace_index.index_data.path_to_node
            assert "dir1/dir2/dir3" in workspace_index.index_data.path_to_node
            assert "dir1/dir2/dir3/file3.txt" in workspace_index.index_data.path_to_node
            
            # 递归删除dir1目录
            workspace_index._remove_node_and_children("dir1")
            
            # 验证dir1及其所有子节点都已从索引中移除
            assert "dir1" not in workspace_index.index_data.path_to_node
            assert "dir1/file2.txt" not in workspace_index.index_data.path_to_node
            assert "dir1/dir2" not in workspace_index.index_data.path_to_node
            assert "dir1/dir2/dir3" not in workspace_index.index_data.path_to_node
            assert "dir1/dir2/dir3/file3.txt" not in workspace_index.index_data.path_to_node
            
            # 验证其他节点仍在索引中
            assert "file1.txt" in workspace_index.index_data.path_to_node

    @pytest.mark.asyncio
    async def test_remove_node_and_children_empty_directory(self) -> None:
        """测试递归删除节点及其所有子节点 - 空目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建空目录
            temp_path = Path(temp_dir)
            empty_dir = temp_path / "empty_dir"
            empty_dir.mkdir()
            
            # 创建WorkspaceIndex实例
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            
            # 初始化索引
            await workspace_index.initialize()
            
            # 验证目录在索引中
            assert "empty_dir" in workspace_index.index_data.path_to_node
            
            # 删除空目录
            workspace_index._remove_node_and_children("empty_dir")
            
            # 验证目录已从索引中移除
            assert "empty_dir" not in workspace_index.index_data.path_to_node