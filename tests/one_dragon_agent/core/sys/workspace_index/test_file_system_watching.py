import pytest
import tempfile
import asyncio
from pathlib import Path
from unittest.mock import patch, MagicMock
from one_dragon_agent.core.sys.workspace_index import WorkspaceIndex, IndexFileSystemEventHandler


class TestFileSystemWatching:
    """测试文件系统监听功能"""

    @pytest.mark.asyncio
    async def test_file_system_event_handler_creation(self) -> None:
        """测试文件系统事件处理器创建"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建WorkspaceIndex实例
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            
            # 创建事件处理器
            event_handler = IndexFileSystemEventHandler(workspace_index)
            
            assert event_handler.workspace_index == workspace_index

    @pytest.mark.asyncio
    async def test_file_system_event_handler_on_created(self) -> None:
        """测试文件创建事件处理"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建WorkspaceIndex实例
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            
            # 创建事件处理器
            event_handler = IndexFileSystemEventHandler(workspace_index)
            
            # Mock事件对象
            mock_event = MagicMock()
            mock_event.is_directory = False
            mock_event.src_path = f"{temp_dir}/test.txt"
            
            # Mock workspace_index的方法
            workspace_index._handle_file_created = MagicMock()
            workspace_index._handle_dir_created = MagicMock()
            workspace_index._handle_gitignore_changed = MagicMock()
            
            # 让异步方法返回协程
            async def mock_coroutine(*args, **kwargs):
                return None
            workspace_index._handle_file_created.side_effect = mock_coroutine
            workspace_index._handle_dir_created.side_effect = mock_coroutine
            
            # 触发创建事件
            with patch('pathlib.Path.name', new_callable=lambda: 'test.txt'):
                event_handler.on_created(mock_event)
            
            # 验证调用了正确的方法
            workspace_index._handle_file_created.assert_called_once_with(mock_event.src_path)
            workspace_index._handle_dir_created.assert_not_called()

    @pytest.mark.asyncio
    async def test_file_system_event_handler_on_created_gitignore(self) -> None:
        """测试.gitignore文件创建事件处理"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建WorkspaceIndex实例
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            
            # 创建事件处理器
            event_handler = IndexFileSystemEventHandler(workspace_index)
            
            # Mock事件对象
            mock_event = MagicMock()
            mock_event.is_directory = False
            mock_event.src_path = f"{temp_dir}/.gitignore"
            
            # Mock workspace_index的方法
            workspace_index._handle_file_created = MagicMock()
            workspace_index._handle_gitignore_changed = MagicMock()
            
            # 让异步方法返回协程
            async def mock_coroutine(*args, **kwargs):
                return None
            workspace_index._handle_file_created.side_effect = mock_coroutine
            
            # 触发创建事件
            with patch('pathlib.Path.name', new_callable=lambda: '.gitignore'):
                event_handler.on_created(mock_event)
            
            # 验证调用了正确的方法
            workspace_index._handle_gitignore_changed.assert_called_once()
            workspace_index._handle_file_created.assert_called_once_with(mock_event.src_path)

    @pytest.mark.asyncio
    async def test_file_system_event_handler_on_deleted(self) -> None:
        """测试文件删除事件处理"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建WorkspaceIndex实例
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            
            # 创建事件处理器
            event_handler = IndexFileSystemEventHandler(workspace_index)
            
            # Mock事件对象
            mock_event = MagicMock()
            mock_event.is_directory = False
            mock_event.src_path = f"{temp_dir}/test.txt"
            
            # Mock workspace_index的方法
            workspace_index._handle_file_deleted = MagicMock()
            workspace_index._handle_dir_deleted = MagicMock()
            workspace_index._handle_gitignore_changed = MagicMock()
            
            # 触发删除事件
            with patch('pathlib.Path.name', new_callable=lambda: 'test.txt'):
                event_handler.on_deleted(mock_event)
            
            # 验证调用了正确的方法
            workspace_index._handle_file_deleted.assert_called_once_with(mock_event.src_path)
            workspace_index._handle_dir_deleted.assert_not_called()

    @pytest.mark.asyncio
    async def test_file_system_event_handler_on_modified(self) -> None:
        """测试文件修改事件处理"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建WorkspaceIndex实例
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            
            # 创建事件处理器
            event_handler = IndexFileSystemEventHandler(workspace_index)
            
            # Mock事件对象
            mock_event = MagicMock()
            mock_event.is_directory = False
            mock_event.src_path = f"{temp_dir}/test.txt"
            
            # Mock workspace_index的方法
            workspace_index._handle_file_modified = MagicMock()
            workspace_index._handle_gitignore_changed = MagicMock()
            
            # 触发修改事件
            with patch('pathlib.Path.name', new_callable=lambda: 'test.txt'):
                event_handler.on_modified(mock_event)
            
            # 验证调用了正确的方法
            workspace_index._handle_file_modified.assert_called_once_with(mock_event.src_path)
            workspace_index._handle_gitignore_changed.assert_not_called()

    @pytest.mark.asyncio
    async def test_file_system_event_handler_on_modified_gitignore(self) -> None:
        """测试.gitignore文件修改事件处理"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建WorkspaceIndex实例
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            
            # 创建事件处理器
            event_handler = IndexFileSystemEventHandler(workspace_index)
            
            # Mock事件对象
            mock_event = MagicMock()
            mock_event.is_directory = False
            mock_event.src_path = f"{temp_dir}/.gitignore"
            
            # Mock workspace_index的方法
            workspace_index._handle_file_modified = MagicMock()
            workspace_index._handle_gitignore_changed = MagicMock()
            
            # 触发修改事件
            with patch('pathlib.Path.name', new_callable=lambda: '.gitignore'):
                event_handler.on_modified(mock_event)
            
            # 验证调用了正确的方法
            workspace_index._handle_gitignore_changed.assert_called_once()
            workspace_index._handle_file_modified.assert_called_once_with(mock_event.src_path)

    @pytest.mark.asyncio
    async def test_file_system_event_handler_on_moved(self) -> None:
        """测试文件移动事件处理"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建WorkspaceIndex实例
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            
            # 创建事件处理器
            event_handler = IndexFileSystemEventHandler(workspace_index)
            
            # Mock事件对象
            mock_event = MagicMock()
            mock_event.is_directory = False
            mock_event.src_path = f"{temp_dir}/test.txt"
            mock_event.dest_path = f"{temp_dir}/test2.txt"
            
            # Mock workspace_index的方法
            workspace_index._handle_file_moved = MagicMock()
            workspace_index._handle_dir_moved = MagicMock()
            workspace_index._handle_gitignore_changed = MagicMock()
            
            # 让异步方法返回协程
            async def mock_coroutine(*args, **kwargs):
                return None
            workspace_index._handle_file_moved.side_effect = mock_coroutine
            workspace_index._handle_dir_moved.side_effect = mock_coroutine
            
            # 触发移动事件
            with patch('pathlib.Path.name', new_callable=lambda: 'test.txt'):
                event_handler.on_moved(mock_event)
            
            # 验证调用了正确的方法
            workspace_index._handle_file_moved.assert_called_once_with(
                mock_event.src_path, mock_event.dest_path
            )
            workspace_index._handle_dir_moved.assert_not_called()

    @pytest.mark.asyncio
    async def test_start_file_watching(self) -> None:
        """测试启动文件监听"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试文件
            temp_path = Path(temp_dir)
            test_file = temp_path / "test.txt"
            test_file.write_text("test content")
            
            # 创建WorkspaceIndex实例
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            
            # 初始化workspace_index
            await workspace_index.initialize()
            
            # 验证监听已启动
            assert workspace_index._watching is True
            assert workspace_index._observer is not None
            assert workspace_index._event_handler is not None

    @pytest.mark.asyncio
    async def test_stop_file_watching(self) -> None:
        """测试停止文件监听"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建WorkspaceIndex实例
            workspace_index = WorkspaceIndex(root_path=temp_dir)
            
            # 初始化workspace_index
            await workspace_index.initialize()
            
            # 停止监听
            workspace_index._stop_file_watching()
            
            # 验证监听已停止
            assert workspace_index._watching is False
            assert workspace_index._observer is None
            assert workspace_index._event_handler is None