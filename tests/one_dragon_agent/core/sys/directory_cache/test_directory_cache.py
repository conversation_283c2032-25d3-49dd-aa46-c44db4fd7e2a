"""Tests for directory cache implementation."""

import asyncio
import os
import tempfile
import shutil
import time
from pathlib import Path
import pytest

from one_dragon_agent.core.sys.workspace_index import Directory<PERSON>acheManager, FileNode


class TestDirectoryCacheManager:
    """Test cases for DirectoryCacheManager."""

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for testing."""
        temp_path = tempfile.mkdtemp()
        yield temp_path
        shutil.rmtree(temp_path)

    @pytest.fixture
    def cache_manager(self):
        """Create cache manager instance."""
        return DirectoryCacheManager(max_memory_mb=1)  # Small memory limit for testing

    @pytest.mark.asyncio
    async def test_cache_initialization(self, cache_manager):
        """Test cache manager initialization."""
        assert cache_manager.cache.root_nodes == {}
        assert cache_manager.cache.path_index == {}
        assert cache_manager.cache.name_index == {}
        assert cache_manager.current_memory == 0
        assert not cache_manager._is_running

    @pytest.mark.asyncio
    async def test_scan_directory(self, cache_manager, temp_dir):
        """Test directory scanning."""
        # Create test files and directories
        test_file = os.path.join(temp_dir, "test.txt")
        test_subdir = os.path.join(temp_dir, "subdir")
        test_subfile = os.path.join(test_subdir, "subfile.txt")

        with open(test_file, "w") as f:
            f.write("test content")

        os.makedirs(test_subdir)
        with open(test_subfile, "w") as f:
            f.write("subfile content")

        # Scan directory
        node = await cache_manager._scan_directory(temp_dir, lazy_load=False)

        assert node is not None
        assert node.name == os.path.basename(temp_dir)
        assert node.path == temp_dir
        assert node.is_dir is True
        assert node.children is not None
        assert "test.txt" in node.children
        assert "subdir" in node.children

        # Check file node
        file_node = node.children["test.txt"]
        assert file_node.name == "test.txt"
        assert file_node.is_dir is False

        # Check subdirectory node
        subdir_node = node.children["subdir"]
        assert subdir_node.name == "subdir"
        assert subdir_node.is_dir is True
        assert subdir_node.children is not None
        assert "subfile.txt" in subdir_node.children

    @pytest.mark.asyncio
    async def test_lazy_loading(self, cache_manager, temp_dir):
        """Test lazy loading functionality."""
        # Create nested directory structure
        deep_dir = os.path.join(temp_dir, "level1", "level2", "level3")
        os.makedirs(deep_dir)

        with open(os.path.join(deep_dir, "deep_file.txt"), "w") as f:
            f.write("deep content")

        # Scan with lazy loading
        node = await cache_manager._scan_directory(temp_dir, lazy_load=True)

        assert node is not None
        assert node.children is not None
        assert "level1" in node.children

        # Check that level2 is not loaded yet
        level1_node = node.children["level1"]
        assert level1_node.children is None

    @pytest.mark.asyncio
    async def test_cache_indexes(self, cache_manager, temp_dir):
        """Test cache indexing functionality."""
        # Create test files
        test_file1 = os.path.join(temp_dir, "test1.txt")
        test_file2 = os.path.join(temp_dir, "test2.py")

        with open(test_file1, "w") as f:
            f.write("content1")
        with open(test_file2, "w") as f:
            f.write("content2")

        # Scan directory
        await cache_manager._scan_directory(temp_dir, lazy_load=False)

        # Check path index
        assert test_file1 in cache_manager.cache.path_index
        assert test_file2 in cache_manager.cache.path_index

        # Check name index
        assert "test1.txt" in cache_manager.cache.name_index
        assert "test2.py" in cache_manager.cache.name_index
        assert len(cache_manager.cache.name_index["test1.txt"]) == 1
        assert len(cache_manager.cache.name_index["test2.py"]) == 1

    @pytest.mark.asyncio
    async def test_search_functionality(self, cache_manager, temp_dir):
        """Test search functionality."""
        # Create test files
        files = [
            ("test_file.txt", "content"),
            ("another_test.py", "content"),
            ("example.md", "content"),
            ("subdir/test_sub.txt", "content"),
        ]

        for file_path, content in files:
            full_path = os.path.join(temp_dir, file_path)
            os.makedirs(os.path.dirname(full_path), exist_ok=True)
            with open(full_path, "w") as f:
                f.write(content)

        # Scan directory
        await cache_manager._scan_directory(temp_dir, lazy_load=False)

        # Test search
        results = await cache_manager.search("test")
        assert len(results) >= 3  # test_file.txt, another_test.py, test_sub.txt

        # Test search with base path
        subdir_path = os.path.join(temp_dir, "subdir")
        results = await cache_manager.search("test", base_path=subdir_path)
        assert len(results) == 1
        assert results[0]["name"] == "test_sub.txt"

        # Test search with file type filter
        results = await cache_manager.search("test", file_types=[".py"])
        assert len(results) == 1
        assert results[0]["name"] == "another_test.py"

    @pytest.mark.asyncio
    async def test_memory_eviction(self, cache_manager, temp_dir):
        """Test memory eviction functionality."""
        # Set very small memory limit
        cache_manager.max_memory = 1000  # 1KB
        cache_manager.current_memory = 0

        # Create many files to trigger eviction
        for i in range(100):
            file_path = os.path.join(temp_dir, f"file_{i}.txt")
            with open(file_path, "w") as f:
                f.write(f"content {i}")

        # Scan directory
        await cache_manager._scan_directory(temp_dir, lazy_load=False)

        # Check that eviction occurred
        assert cache_manager.current_memory <= cache_manager.max_memory

    @pytest.mark.asyncio
    async def test_file_creation_event(self, cache_manager, temp_dir):
        """Test file creation event handling."""
        # Start cache manager
        await cache_manager.start()

        # Scan the directory first to set up watching
        await cache_manager._scan_directory(temp_dir, lazy_load=False)

        # Initially no files with this name
        results = await cache_manager.search("new_file")
        assert len(results) == 0

        # Create new file
        new_file = os.path.join(temp_dir, "new_file.txt")
        with open(new_file, "w") as f:
            f.write("new content")

        # Wait for event processing (longer wait)
        await asyncio.sleep(0.5)

        # Check if file is found
        results = await cache_manager.search("new_file")
        assert len(results) == 1
        assert results[0]["name"] == "new_file.txt"

        await cache_manager.stop()

    @pytest.mark.asyncio
    async def test_file_deletion_event(self, cache_manager, temp_dir):
        """Test file deletion event handling."""
        # Create test file
        test_file = os.path.join(temp_dir, "delete_test.txt")
        with open(test_file, "w") as f:
            f.write("test content")

        # Start cache manager and scan
        await cache_manager.start()
        await cache_manager._scan_directory(temp_dir, lazy_load=False)

        # Verify file exists in cache
        results = await cache_manager.search("delete_test")
        assert len(results) == 1

        # Delete file
        os.remove(test_file)

        # Wait for event processing (longer wait)
        await asyncio.sleep(0.5)

        # Check if file is removed from cache
        results = await cache_manager.search("delete_test")
        assert len(results) == 0

        await cache_manager.stop()

    @pytest.mark.asyncio
    async def test_file_modification_event(self, cache_manager, temp_dir):
        """Test file modification event handling."""
        # Create test file
        test_file = os.path.join(temp_dir, "modify_test.txt")
        with open(test_file, "w") as f:
            f.write("original content")

        original_time = os.path.getmtime(test_file)

        # Start cache manager and scan
        await cache_manager.start()
        await cache_manager._scan_directory(temp_dir, lazy_load=False)

        # Get original node
        node = cache_manager.cache.path_index[test_file]
        original_node_time = node.modified_time

        # Modify file
        time.sleep(0.2)  # Ensure time difference
        with open(test_file, "a") as f:
            f.write(" modified")

        # Wait for event processing (longer wait)
        await asyncio.sleep(0.5)

        # Check if modification time is updated
        node = cache_manager.cache.path_index[test_file]
        assert node.modified_time > original_node_time

        await cache_manager.stop()

    @pytest.mark.asyncio
    async def test_file_move_event(self, cache_manager, temp_dir):
        """Test file move event handling."""
        # Create test file
        old_file = os.path.join(temp_dir, "old_name.txt")
        with open(old_file, "w") as f:
            f.write("test content")

        # Start cache manager and scan
        await cache_manager.start()
        await cache_manager._scan_directory(temp_dir, lazy_load=False)

        # Verify old file exists
        results = await cache_manager.search("old_name")
        assert len(results) == 1

        # Move file
        new_file = os.path.join(temp_dir, "new_name.txt")
        shutil.move(old_file, new_file)

        # Wait for event processing (longer wait)
        await asyncio.sleep(0.5)

        # Check if old file is removed
        results = await cache_manager.search("old_name")
        assert len(results) == 0

        # Check if new file exists
        results = await cache_manager.search("new_name")
        assert len(results) == 1
        assert results[0]["name"] == "new_name.txt"

        await cache_manager.stop()

    @pytest.mark.asyncio
    async def test_start_stop(self, cache_manager):
        """Test start and stop functionality."""
        assert not cache_manager._is_running

        await cache_manager.start()
        assert cache_manager._is_running
        assert cache_manager.update_queue is not None

        await cache_manager.stop()
        assert not cache_manager._is_running

    @pytest.mark.asyncio
    async def test_nonexistent_directory(self, cache_manager):
        """Test scanning non-existent directory."""
        node = await cache_manager._scan_directory("/nonexistent/path")
        assert node is None

    @pytest.mark.asyncio
    async def test_permission_error_handling(self, cache_manager, temp_dir):
        """Test handling of permission errors."""
        # Create directory with restricted permissions (if possible)
        restricted_dir = os.path.join(temp_dir, "restricted")
        os.makedirs(restricted_dir)

        try:
            # Try to make directory inaccessible (this may not work on all systems)
            os.chmod(restricted_dir, 0o000)

            # Should not raise exception
            node = await cache_manager._scan_directory(restricted_dir)
            # Node might be created but children should be empty
            assert node is not None
            assert node.children == {}

        finally:
            # Restore permissions for cleanup
            os.chmod(restricted_dir, 0o755)
