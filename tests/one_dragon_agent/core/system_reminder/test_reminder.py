"""Tests for ReminderManager."""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from one_dragon_agent.core.system_reminder.reminder import Reminder<PERSON>anager
from one_dragon_agent.core.event.event import Event
from one_dragon_agent.core.model.message import (
    OdaModelMessage,
    OdaModelMessageRole,
    OdaMessageTextContent,
)


class TestReminderManager:
    """Test cases for ReminderManager."""

    @pytest.fixture
    def reminder_manager(self):
        """Create a ReminderManager instance for testing."""
        return ReminderManager(session_id="test_session")

    @pytest.mark.timeout(5)
    @pytest.mark.asyncio
    async def test_init(self, reminder_manager):
        """Test that the reminder manager is initialized correctly."""
        assert reminder_manager.session_id == "test_session"

    @pytest.mark.timeout(5)
    @pytest.mark.asyncio
    async def test_close(self, reminder_manager):
        """Test closing the reminder manager."""
        # Add some reminders
        from dataclasses import dataclass
        from typing import Dict, Any, List
        
        @dataclass
        class TodoUpdatedEvent(Event):
            """Event for todo updates."""
            data: Dict[str, Any]
            
        event = TodoUpdatedEvent(
            event_type="todo.updated",
            data={
                "session_id": "test_session",
                "item_count": 1,
                "content": [
                    {
                        "content": "Test task",
                        "status": "pending",
                        "priority": "high",
                        "id": "1",
                    }
                ],
            },
        )

        await reminder_manager.handle_event(event)

        # Close the reminder manager
        await reminder_manager.close()

        # Getting pending reminders should return empty list
        reminders = await reminder_manager.get_pending_reminders()
        assert len(reminders) == 0
