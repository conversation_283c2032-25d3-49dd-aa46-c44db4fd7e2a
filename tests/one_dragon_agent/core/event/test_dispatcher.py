"""Tests for EventDispatcher."""

import pytest
import asyncio
from dataclasses import dataclass
from typing import Any, Dict
from unittest.mock import Mock, AsyncMock
from one_dragon_agent.core.event.dispatcher import EventDispatcher
from one_dragon_agent.core.event.event import Event
from one_dragon_agent.core.event.handler import EventHandler


@dataclass
class TestEvent(Event):
    """A test event with data."""

    data: Dict[str, Any]


class EventHandlerForTest(EventHandler):
    """Test event handler implementation."""

    def __init__(self, event_type_val: str = "test.event"):
        self._event_type_val = event_type_val
        self.handled_events = []

    async def handle(self, event: Event) -> None:
        """Handle an event."""
        self.handled_events.append(event)

    @property
    def event_type(self) -> str:
        """Get the event type this handler is interested in."""
        return self._event_type_val


class TestEventDispatcher:
    """Test cases for EventDispatcher."""

    @pytest.fixture
    def event_dispatcher(self):
        """Create an EventDispatcher instance for testing."""
        return EventDispatcher()

    @pytest.mark.asyncio
    async def test_init(self, event_dispatcher):
        """Test that the event dispatcher is initialized correctly."""
        assert event_dispatcher is not None

    @pytest.mark.asyncio
    async def test_subscribe_and_unsubscribe(self, event_dispatcher):
        """Test subscribing and unsubscribing from events."""
        handler = EventHandlerForTest()

        # Subscribe to events
        subscription_id = event_dispatcher.subscribe("test.event", handler)
        assert subscription_id is not None

        # Unsubscribe from events
        result = event_dispatcher.unsubscribe(subscription_id)
        assert result is True

        # Try to unsubscribe again (should fail)
        result = event_dispatcher.unsubscribe(subscription_id)
        assert result is False

    @pytest.mark.asyncio
    async def test_publish_and_handle_event(self, event_dispatcher):
        """Test publishing and handling events."""
        handler = EventHandlerForTest()
        event_dispatcher.subscribe("test.event", handler)

        # Create and publish an event
        event = TestEvent("test.event", {"test_data": "value"})
        await event_dispatcher.publish(event)

        # Wait a bit for async processing
        await asyncio.sleep(0.1)

        # Check that the event was handled
        assert len(handler.handled_events) == 1
        assert handler.handled_events[0].event_type == "test.event"
        assert handler.handled_events[0].data["test_data"] == "value"

    @pytest.mark.asyncio
    async def test_publish_multiple_handlers(self, event_dispatcher):
        """Test publishing events to multiple handlers."""
        handler1 = EventHandlerForTest()
        handler2 = EventHandlerForTest()
        event_dispatcher.subscribe("test.event", handler1)
        event_dispatcher.subscribe("test.event", handler2)

        # Create and publish an event
        event = TestEvent("test.event", {"test_data": "value"})
        await event_dispatcher.publish(event)

        # Wait a bit for async processing
        await asyncio.sleep(0.1)

        # Check that both handlers received the event
        assert len(handler1.handled_events) == 1
        assert len(handler2.handled_events) == 1

    @pytest.mark.asyncio
    async def test_wildcard_subscription(self, event_dispatcher):
        """Test wildcard event subscription."""
        handler = EventHandlerForTest("*")
        event_dispatcher.subscribe("*", handler)

        # Create and publish different types of events
        event1 = TestEvent("test.event1", {"data": "value1"})
        event2 = TestEvent("test.event2", {"data": "value2"})
        await event_dispatcher.publish(event1)
        await event_dispatcher.publish(event2)

        # Wait a bit for async processing
        await asyncio.sleep(0.1)

        # Check that the handler received both events
        assert len(handler.handled_events) == 2
        assert handler.handled_events[0].event_type == "test.event1"
        assert handler.handled_events[1].event_type == "test.event2"

    @pytest.mark.asyncio
    async def test_close(self, event_dispatcher):
        """Test closing the event dispatcher."""
        handler = EventHandlerForTest()
        event_dispatcher.subscribe("test.event", handler)

        # Close the event dispatcher
        await event_dispatcher.close()

        # Try to publish an event (should not cause errors)
        event = TestEvent("test.event", {"test_data": "value"})
        await event_dispatcher.publish(event)

        # Handler should not receive the event after closing
        assert len(handler.handled_events) == 0
