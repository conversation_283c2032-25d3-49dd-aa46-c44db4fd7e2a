import asyncio
from typing import AsyncIterator
from one_dragon_agent.core.model.client_factory import ModelClientFactory
from one_dragon_agent.core.sys.config import create_session_config_from_env
from one_dragon_agent.core.sys.message import OdaMessage, OdaMessageSource
from one_dragon_agent.core.model.message import (
    OdaModelMessageRole,
    OdaMessageTextContent,
)
from one_dragon_agent.core.sys.session import OdaSession


async def debug_chat_completion_stream(user_input: str):
    """Debug function to test chat completion streaming with tools.

    Args:
        user_input: The user's input message as a string
    """

    # Create an async generator that yields our user message
    async def create_input_stream(message_text: str) -> AsyncIterator[OdaMessage]:
        yield OdaMessage(
            source=OdaMessageSource.USER,
            role=OdaModelMessageRole.USER,
            content=OdaMessageTextContent(text=message_text),
        )
        # Yield None to signal end of stream
        yield None

    # Create session config and OdaSession
    session_config = create_session_config_from_env()
    session = OdaSession(
        input_stream=create_input_stream(user_input), config=session_config
    )

    # Access the LLM client from the session
    llm_client = session._llm_factory.create_model_client(
        session_config.common_llm_config
    )

    # Create message from user input
    messages = [
        OdaMessage(
            source=OdaMessageSource.USER,
            role=OdaModelMessageRole.USER,
            content=OdaMessageTextContent(text=user_input),
        )
    ]

    # Get tools from session
    tools = session._tools

    try:
        print(f"Starting chat completion stream with tools for input: '{user_input}'")

        # Stream the response
        async for content in llm_client.chat_completion_stream(
            messages=messages, tools=tools
        ):
            if hasattr(content, "text"):
                print(f"Text: {content.text}")
            elif hasattr(content, "tool_calls"):
                print("Tool calls detected:")
                for tool_call in content.tool_calls:
                    print(f"  - Tool: {tool_call.tool_name}")
                    print(f"    ID: {tool_call.tool_call_id}")
                    print(f"    Args: {tool_call.tool_args}")

        print("Stream completed.")

    except Exception as e:
        print(f"Error occurred: {e}")
    finally:
        await llm_client.close()


if __name__ == "__main__":
    # Example usage with a sample input
    asyncio.run(
        debug_chat_completion_stream("Create a todo item to implement the debug tool")
    )
