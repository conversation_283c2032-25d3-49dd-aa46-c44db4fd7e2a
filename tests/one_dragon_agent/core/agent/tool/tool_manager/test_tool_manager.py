from one_dragon_agent.core.agent.tool import <PERSON><PERSON><PERSON><PERSON><PERSON>
from one_dragon_agent.core.agent.tool.todo_write import TodoWriteTool
from one_dragon_agent.core.agent.tool.permission.permission_manager import PermissionManager


class TestToolManager:
    """Test suite for the ToolManager class."""

    def test_initialization(self):
        """
        Tests that the ToolManager initializes correctly and registers tools.
        """
        permission_manager = PermissionManager()
        tool_manager = ToolManager(permission_manager)
        assert tool_manager is not None, "ToolManager should be instantiated."
        assert (
            len(tool_manager.tools) > 0
        ), "ToolManager should register at least one tool."

    def test_discover_todowrite_tool(self):
        """
        Tests that the TodoWriteTool is correctly registered by the ToolManager.
        """
        permission_manager = PermissionManager()
        tool_manager = ToolManager(permission_manager)
        tools = tool_manager.tools

        assert "TodoWrite" in tools, "TodoWrite tool should be in the registered tools."
        assert isinstance(
            tools["TodoWrite"], TodoWriteTool
        ), "The tool should be an instance of TodoWriteTool."

    def test_get_tool_definitions(self):
        """
        Tests that tool definitions are correctly generated for the LLM.
        """
        permission_manager = PermissionManager()
        tool_manager = ToolManager(permission_manager)
        definitions = tool_manager.get_tool_definitions()

        assert isinstance(definitions, list), "Definitions should be a list."
        assert len(definitions) == 2, "There should be two tool definitions."

        # Check that both tools are present
        tool_names = [tool_def["name"] for tool_def in definitions]
        assert "TodoWrite" in tool_names
        assert "read_file" in tool_names

        # Check TodoWrite tool definition
        todo_write_def = next(
            (tool_def for tool_def in definitions if tool_def["name"] == "TodoWrite"), None
        )
        assert todo_write_def is not None
        assert "description" in todo_write_def
        assert "input_schema" in todo_write_def
        assert isinstance(todo_write_def["input_schema"], dict)

        # Check read_file tool definition
        read_file_def = next(
            (tool_def for tool_def in definitions if tool_def["name"] == "read_file"), None
        )
        assert read_file_def is not None
        assert "description" in read_file_def
        assert "input_schema" in read_file_def
        assert isinstance(read_file_def["input_schema"], dict)
