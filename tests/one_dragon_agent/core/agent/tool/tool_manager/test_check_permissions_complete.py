import asyncio
from unittest.mock import Mock, AsyncMock

import pytest

from one_dragon_agent.core.agent.tool.context import ToolExecutionContext
from one_dragon_agent.core.agent.tool.tool import OdaTool
from one_dragon_agent.core.agent.tool.tool_manager import Tool<PERSON>anager
from one_dragon_agent.core.event.dispatcher import EventDispatcher
from one_dragon_agent.core.agent.tool.permission.permission_manager import PermissionManager


@pytest.fixture
def mock_permission_manager():
    """Fixture for a mocked PermissionManager."""
    return AsyncMock(spec=PermissionManager)


@pytest.fixture
def mock_event_dispatcher():
    """Fixture for a mocked EventDispatcher."""
    return AsyncMock(spec=EventDispatcher)


@pytest.fixture
def tool_manager(mock_permission_manager):
    """Fixture for a ToolManager with a mocked PermissionManager."""
    # We need to bypass the __init__ of ToolManager to avoid real tool registration
    manager = ToolManager.__new__(ToolManager)
    manager._permission_manager = mock_permission_manager
    return manager


@pytest.fixture
def mock_tool():
    """Fixture for a mocked OdaTool."""
    tool = Mock(spec=OdaTool)
    tool.name = "mock_tool"
    tool.required_permissions = set()
    return tool


@pytest.fixture
def tool_execution_context(mock_event_dispatcher):
    """Fixture for a ToolExecutionContext."""
    context = Mock(spec=ToolExecutionContext)
    context.session_id = "test_session"
    context.event_dispatcher = mock_event_dispatcher
    return context


@pytest.mark.asyncio
class TestCheckPermissions:
    """Test suite for the ToolManager._check_permissions method."""

    async def test_no_permissions_required(
        self, tool_manager, mock_tool, tool_execution_context
    ):
        """
        Tests that the check passes immediately if the tool requires no permissions.
        """
        mock_tool.required_permissions = set()

        is_authorized, message = await tool_manager._check_permissions(
            mock_tool, tool_execution_context
        )

        assert is_authorized is True
        assert message is None

    async def test_permission_already_granted(
        self,
        tool_manager,
        mock_tool,
        tool_execution_context,
        mock_permission_manager,
    ):
        """
        Tests that the check passes if the required permission is already granted.
        """
        mock_tool.required_permissions = {"filesystem.read"}
        mock_permission_manager.is_granted.return_value = True

        is_authorized, message = await tool_manager._check_permissions(
            mock_tool, tool_execution_context
        )

        assert is_authorized is True
        assert message is None
        mock_permission_manager.is_granted.assert_called_once_with(
            "test_session", "filesystem.read"
        )

    async def test_permission_requested_and_granted(
        self,
        tool_manager,
        mock_tool,
        tool_execution_context,
        mock_permission_manager,
        mock_event_dispatcher,
    ):
        """
        Tests the full flow where a permission is requested and the user grants it.
        """
        mock_tool.required_permissions = {"filesystem.write"}
        mock_permission_manager.is_granted.return_value = False
        mock_permission_manager.get_response_signal.return_value = True

        is_authorized, message = await tool_manager._check_permissions(
            mock_tool, tool_execution_context
        )

        assert is_authorized is True
        assert message is None
        mock_permission_manager.is_granted.assert_called_once_with(
            "test_session", "filesystem.write"
        )
        mock_event_dispatcher.publish.assert_called_once()
        mock_permission_manager.get_response_signal.assert_called_once_with(
            "test_session", "filesystem.write"
        )

    async def test_permission_requested_and_denied(
        self,
        tool_manager,
        mock_tool,
        tool_execution_context,
        mock_permission_manager,
        mock_event_dispatcher,
    ):
        """
        Tests the flow where a permission is requested and the user denies it.
        """
        mock_tool.required_permissions = {"filesystem.write"}
        mock_permission_manager.is_granted.return_value = False
        mock_permission_manager.get_response_signal.return_value = False

        is_authorized, message = await tool_manager._check_permissions(
            mock_tool, tool_execution_context
        )

        assert is_authorized is False
        assert message is not None
        assert "user has denied" in message
        assert "filesystem.write" in message
        assert "mock_tool" in message

        mock_permission_manager.is_granted.assert_called_once_with(
            "test_session", "filesystem.write"
        )
        mock_event_dispatcher.publish.assert_called_once()
        mock_permission_manager.get_response_signal.assert_called_once_with(
            "test_session", "filesystem.write"
        )

    async def test_multiple_permissions_all_granted(
        self,
        tool_manager,
        mock_tool,
        tool_execution_context,
        mock_permission_manager,
    ):
        """
        Tests that the check passes if all required permissions are granted.
        """
        required_perms = {"filesystem.read", "network.access"}
        mock_tool.required_permissions = required_perms
        mock_permission_manager.is_granted.return_value = True

        is_authorized, message = await tool_manager._check_permissions(
            mock_tool, tool_execution_context
        )

        assert is_authorized is True
        assert message is None
        # Check that is_granted was called for each permission
        assert mock_permission_manager.is_granted.call_count == len(required_perms)
        for perm in required_perms:
            mock_permission_manager.is_granted.assert_any_call("test_session", perm)

    async def test_multiple_permissions_one_denied(
        self,
        tool_manager,
        mock_tool,
        tool_execution_context,
        mock_permission_manager,
    ):
        """
        Tests that the check fails immediately if any one of multiple required
        permissions is denied.
        """
        required_perms = ["filesystem.read", "network.access"]  # Use list to ensure order
        mock_tool.required_permissions = required_perms

        # Simulate 'filesystem.read' is granted, but 'network.access' is not
        async def is_granted_side_effect(session_id, permission):
            if permission == "filesystem.read":
                return True
            if permission == "network.access":
                return False
            return False
        
        mock_permission_manager.is_granted.side_effect = is_granted_side_effect
        mock_permission_manager.get_response_signal.return_value = False # User denies

        is_authorized, message = await tool_manager._check_permissions(
            mock_tool, tool_execution_context
        )

        assert is_authorized is False
        assert message is not None
        assert "network.access" in message # Should be the one that was denied

        # is_granted should have been called for both permissions in order
        assert mock_permission_manager.is_granted.call_count == 2
        calls = mock_permission_manager.is_granted.call_args_list
        assert calls[0] == (("test_session", "filesystem.read"),)
        assert calls[1] == (("test_session", "network.access"),)
        # get_response_signal should only be called for the one that needed a request (network.access)
        mock_permission_manager.get_response_signal.assert_called_once_with(
            "test_session", "network.access"
        )

    async def test_permission_request_event_format(
        self,
        tool_manager,
        mock_tool,
        tool_execution_context,
        mock_permission_manager,
        mock_event_dispatcher,
    ):
        """
        Tests that the permission request event is published with correct data.
        """
        mock_tool.name = "test_tool_name"
        mock_tool.required_permissions = {"test.permission"}
        mock_permission_manager.is_granted.return_value = False
        mock_permission_manager.get_response_signal.return_value = True

        await tool_manager._check_permissions(mock_tool, tool_execution_context)

        # Check that the event was published with correct data
        mock_event_dispatcher.publish.assert_called_once()
        published_event = mock_event_dispatcher.publish.call_args[0][0]
        
        assert published_event.event_type == "permission.request"
        assert published_event.session_id == "test_session"
        assert published_event.tool_name == "test_tool_name"
        assert published_event.permission == "test.permission"