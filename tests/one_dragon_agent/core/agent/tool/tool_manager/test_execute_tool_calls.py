import pytest
from unittest.mock import MagicMock, AsyncMock

from one_dragon_agent.core.model.message import (
    OdaToolCall,
    OdaModelMessage,
    OdaModelMessageRole,
    OdaMessageToolResponseContent,
    OdaMessageTextContent,
)
from one_dragon_agent.core.agent.tool.context import ToolExecution<PERSON>ontext
from one_dragon_agent.core.agent.tool import Too<PERSON><PERSON><PERSON>ger
from one_dragon_agent.core.agent.tool.result import Tool<PERSON><PERSON><PERSON>, ToolExecutionStatus
from one_dragon_agent.core.agent.tool.permission.permission_manager import PermissionManager


@pytest.mark.asyncio
async def test_execute_tool_calls_success():
    """Test successful execution of a tool call."""
    permission_manager = PermissionManager()
    tool_manager = ToolManager(permission_manager)
    context = ToolExecutionContext(
        session_id="test_session", event_dispatcher=AsyncMock(), permission_manager=permission_manager
    )

    # Mock the tool's call method to return ToolResult
    mock_tool = tool_manager.tools["TodoWrite"]
    mock_tool.call = AsyncMock()

    # Create a mock ToolResult
    mock_tool_result = ToolResult(
        status=ToolExecutionStatus.SUCCESS,
        event_data="Task created successfully",
        model_message="Task created successfully",  # Tool now provides its own message
    )
    mock_tool.call.return_value = mock_tool_result

    # Mock event creation methods
    mock_tool.create_start_event = MagicMock()
    mock_tool.create_complete_event = MagicMock()

    tool_calls = [OdaToolCall(tool_call_id="1", tool_name="TodoWrite", tool_args="{}")]

    results = [
        result async for result in tool_manager.execute_tool_calls(tool_calls, context)
    ]

    assert len(results) == 1
    result = results[0]
    assert isinstance(result, OdaModelMessage)
    assert result.role == OdaModelMessageRole.TOOL
    assert isinstance(result.content, OdaMessageToolResponseContent)
    assert result.content.tool_call_id == "1"
    assert "Task created successfully" in result.content.response

    # Verify events were created and sent
    mock_tool.create_start_event.assert_called_once()
    mock_tool.create_complete_event.assert_called_once()
    # publish should be called twice (start event + result event)
    assert context.event_dispatcher.publish.call_count == 2


@pytest.mark.asyncio
async def test_execute_tool_calls_tool_not_found():
    """Test tool not found error during execution."""
    permission_manager = PermissionManager()
    tool_manager = ToolManager(permission_manager)
    context = ToolExecutionContext(
        session_id="test_session", event_dispatcher=AsyncMock(), permission_manager=permission_manager
    )

    tool_calls = [
        OdaToolCall(tool_call_id="1", tool_name="NonExistentTool", tool_args="{}")
    ]

    results = [
        result async for result in tool_manager.execute_tool_calls(tool_calls, context)
    ]

    assert len(results) == 1
    result = results[0]
    assert isinstance(result, OdaModelMessage)
    assert result.role == OdaModelMessageRole.TOOL
    assert isinstance(result.content, OdaMessageToolResponseContent)
    assert result.content.tool_call_id == "1"
    assert "not found" in result.content.response


@pytest.mark.asyncio
async def test_execute_tool_calls_execution_error():
    """Test error during tool execution."""
    permission_manager = PermissionManager()
    tool_manager = ToolManager(permission_manager)
    context = ToolExecutionContext(
        session_id="test_session", event_dispatcher=AsyncMock(), permission_manager=permission_manager
    )

    # Mock the tool's call method to raise an exception
    mock_tool = tool_manager.tools["TodoWrite"]
    mock_tool.call = AsyncMock(side_effect=Exception("Execution failed"))

    # Mock event creation methods
    mock_tool.create_start_event = MagicMock()
    mock_tool.create_complete_event = MagicMock()

    tool_calls = [OdaToolCall(tool_call_id="1", tool_name="TodoWrite", tool_args="{}")]

    results = [
        result async for result in tool_manager.execute_tool_calls(tool_calls, context)
    ]

    assert len(results) == 1
    result = results[0]
    assert isinstance(result, OdaModelMessage)
    assert result.role == OdaModelMessageRole.TOOL
    assert isinstance(result.content, OdaMessageToolResponseContent)
    assert result.content.tool_call_id == "1"
    assert "Error executing tool" in result.content.response
    assert "Execution failed" in result.content.response

    # Verify error event was created and sent
    mock_tool.create_complete_event.assert_called_once()
    # Should be called twice: start event + result event
    assert context.event_dispatcher.publish.call_count == 2
