"""Tests for todo message events in OneDragon-Agent."""

# Correct import path for src-layout
from one_dragon_agent.core.agent.tool.todo_message import (
    TodoListUpdatedEvent,
    TodoUpdateFailedEvent,
    TodoEventType,
)
from one_dragon_agent.core.agent.tool.todo_manager import (
    TodoItem,
    TaskStatus,
    TaskPriority,
)


class TestTodoMessageEvents:
    """Test cases for todo message events."""

    def test_todo_list_updated_event(self) -> None:
        """Test TodoListUpdatedEvent creation and attributes."""
        session_id = "test_session_123"

        # Create some test todo items
        todo1 = TodoItem(
            content="Test task 1",
            status=TaskStatus.PENDING,
            priority=TaskPriority.HIGH,
            id="todo-1",
        )
        todo2 = TodoItem(
            content="Test task 2",
            status=TaskStatus.IN_PROGRESS,
            priority=TaskPriority.MEDIUM,
            id="todo-2",
        )
        todos = [todo1, todo2]

        event = TodoListUpdatedEvent(session_id, todos)

        assert event.event_type == TodoEventType.LIST_UPDATED
        assert event.session_id == session_id
        assert len(event.todos) == 2
        assert event.todos[0].content == "Test task 1"
        assert event.todos[1].status == TaskStatus.IN_PROGRESS

    def test_todo_update_failed_event(self) -> None:
        """Test TodoUpdateFailedEvent creation and attributes."""
        session_id = "test_session_456"
        error_message = "Invalid todo item: missing required field"
        event = TodoUpdateFailedEvent(session_id, error_message)

        assert event.event_type == TodoEventType.UPDATE_FAILED
        assert event.session_id == session_id
        assert event.error == error_message

        # Test with structured error
        structured_error = {
            "message": "Validation failed",
            "field": "content",
            "reason": "Content cannot be empty",
        }
        event_structured = TodoUpdateFailedEvent(session_id, structured_error)
        assert event_structured.error == structured_error

    def test_todo_event_types_enum(self) -> None:
        """Test that TodoEventType enum values are correct."""
        assert TodoEventType.LIST_UPDATED == "todo.list_updated"
        assert TodoEventType.UPDATE_FAILED == "todo.update_failed"
