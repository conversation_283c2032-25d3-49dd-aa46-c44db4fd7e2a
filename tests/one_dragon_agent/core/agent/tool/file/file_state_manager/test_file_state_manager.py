import asyncio
import time
import pytest
import pytest_asyncio
from one_dragon_agent.core.agent.tool import (
    FileStateManager,
    FileState,
)


@pytest_asyncio.fixture
async def state_manager() -> FileStateManager:
    """Fixture to provide a clean FileStateManager instance for each test."""
    manager = FileStateManager()
    # Clean up any state from previous tests
    await manager.clear_session("test_session_1")
    await manager.clear_session("test_session_2")
    await manager.clear_session("concurrent_session")
    return manager


@pytest.mark.asyncio
class TestFileStateManager:
    """Test suite for the FileStateManager class."""

    @pytest.mark.timeout(5)
    async def test_singleton_instance(self, state_manager: FileStateManager):
        """Tests that FileStateManager is a singleton."""
        manager1 = FileStateManager()
        manager2 = FileStateManager()
        assert manager1 is manager2
        assert manager1 is state_manager

    @pytest.mark.timeout(5)
    async def test_update_and_get_state_single_session(
        self, state_manager: FileStateManager
    ):
        """Tests updating and retrieving state for a single session."""
        session_id = "test_session_1"
        file_path = "/path/to/file1.txt"
        content = "Hello, World!"
        timestamp = time.time()

        await state_manager.update_state(session_id, file_path, content, timestamp)
        state = await state_manager.get_state(session_id, file_path)

        assert state is not None
        assert isinstance(state, FileState)
        assert state.content == content
        assert state.timestamp == timestamp

    @pytest.mark.timeout(5)
    async def test_get_state_non_existent(self, state_manager: FileStateManager):
        """Tests retrieving a non-existent state returns None."""
        session_id = "test_session_1"
        file_path = "/path/to/non_existent_file.txt"

        state = await state_manager.get_state(session_id, file_path)
        assert state is None

    @pytest.mark.timeout(5)
    async def test_session_isolation(self, state_manager: FileStateManager):
        """Tests that states are isolated between different sessions."""
        session_1 = "test_session_1"
        session_2 = "test_session_2"
        file_path = "/path/to/shared_file.txt"
        content_1 = "Content for session 1"
        timestamp_1 = time.time()

        content_2 = "Content for session 2"
        timestamp_2 = time.time() + 1

        await state_manager.update_state(session_1, file_path, content_1, timestamp_1)
        await state_manager.update_state(session_2, file_path, content_2, timestamp_2)

        state_1 = await state_manager.get_state(session_1, file_path)
        state_2 = await state_manager.get_state(session_2, file_path)

        assert state_1 is not None
        assert state_1.content == content_1
        assert state_1.timestamp == timestamp_1

        assert state_2 is not None
        assert state_2.content == content_2
        assert state_2.timestamp == timestamp_2

        assert state_1 is not state_2

    @pytest.mark.timeout(5)
    async def test_update_existing_state(self, state_manager: FileStateManager):
        """Tests that updating an existing state overwrites it."""
        session_id = "test_session_1"
        file_path = "/path/to/file_to_update.txt"

        # Initial state
        await state_manager.update_state(
            session_id, file_path, "Initial content", time.time()
        )

        # New state
        new_content = "Updated content"
        new_timestamp = time.time() + 5
        await state_manager.update_state(
            session_id, file_path, new_content, new_timestamp
        )

        current_state = await state_manager.get_state(session_id, file_path)
        assert current_state is not None
        assert current_state.content == new_content
        assert current_state.timestamp == new_timestamp

    @pytest.mark.timeout(10)
    async def test_concurrency_safety(self, state_manager: FileStateManager):
        """Tests that concurrent updates and reads are handled safely."""
        session_id = "concurrent_session"
        num_tasks = 50

        async def update_task(i):
            file_path = f"/path/to/concurrent_file_{i}.txt"
            content = f"Content from task {i}"
            timestamp = time.time() + i
            await state_manager.update_state(session_id, file_path, content, timestamp)
            retrieved_state = await state_manager.get_state(session_id, file_path)
            assert retrieved_state is not None
            assert retrieved_state.content == content

        tasks = [update_task(i) for i in range(num_tasks)]
        await asyncio.gather(*tasks)

        # Final check
        for i in range(num_tasks):
            file_path = f"/path/to/concurrent_file_{i}.txt"
            state = await state_manager.get_state(session_id, file_path)
            assert state is not None
            assert state.content == f"Content from task {i}"

    @pytest.mark.timeout(5)
    async def test_clear_session(self, state_manager: FileStateManager):
        """Tests that a session's state can be cleared."""
        session_id = "test_session_1"
        file_path = "/path/to/file_to_clear.txt"

        await state_manager.update_state(
            session_id, file_path, "Some content", time.time()
        )
        state_before_clear = await state_manager.get_state(session_id, file_path)
        assert state_before_clear is not None

        await state_manager.clear_session(session_id)

        state_after_clear = await state_manager.get_state(session_id, file_path)
        assert state_after_clear is None

        # Ensure other sessions are not affected
        session_2 = "test_session_2"
        await state_manager.update_state(
            session_2, file_path, "Other content", time.time()
        )
        state_2 = await state_manager.get_state(session_2, file_path)
        assert state_2 is not None
