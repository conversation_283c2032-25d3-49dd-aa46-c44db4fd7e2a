"""
读取文件工具测试模块

该模块包含了对ReadFileTool类的各种测试用例。
"""

import pytest
import tempfile
import os
import asyncio
from unittest.mock import MagicMock

from one_dragon_agent.core.agent.tool.file.read_file import ReadFileTool
from one_dragon_agent.core.agent.tool.context import ToolExecutionContext
from one_dragon_agent.core.sys.message import OdaMessageSource
from one_dragon_agent.core.model.message import OdaMessageTextContent


# 标记所有测试为异步测试
pytestmark = pytest.mark.asyncio


class TestReadFileTool:
    """读取文件工具测试类"""

    @pytest.fixture
    def tool_context(self):
        """创建工具上下文fixture"""
        context = MagicMock(spec=ToolExecutionContext)
        context.session_id = "test_session"
        # Create a mock event dispatcher with an async publish method
        mock_event_dispatcher = MagicMock()
        mock_event_dispatcher.publish = MagicMock(
            side_effect=lambda event: asyncio.sleep(0)
        )  # Mock async behavior
        context.event_dispatcher = mock_event_dispatcher
        return context

    @pytest.fixture
    def read_file_tool(self):
        """创建读取文件工具fixture"""
        return ReadFileTool()

    @pytest.fixture
    def temp_text_file(self):
        """创建临时文本文件fixture"""
        with tempfile.NamedTemporaryFile(
            mode="w", delete=False, suffix=".txt", encoding="utf-8"
        ) as f:
            # 写入多行测试内容
            f.write("第一行内容\n")
            f.write("第二行内容\n")
            f.write("第三行内容\n")
            f.write("第四行内容\n")
            f.write("第五行内容\n")
            temp_file_path = f.name

        yield temp_file_path

        # 清理临时文件
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)

    @pytest.fixture
    def temp_empty_file(self):
        """创建临时空文件fixture"""
        with tempfile.NamedTemporaryFile(mode="w", delete=False, suffix=".txt") as f:
            temp_file_path = f.name

        yield temp_file_path

        # 清理临时文件
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)

    @pytest.fixture
    def temp_long_line_file(self):
        """创建包含长行的临时文件fixture"""
        with tempfile.NamedTemporaryFile(
            mode="w", delete=False, suffix=".txt", encoding="utf-8"
        ) as f:
            # 写入一个超过2000字符的行
            long_line = "A" * 2500
            f.write(f"正常行\n")
            f.write(f"{long_line}\n")
            f.write("另一正常行\n")
            temp_file_path = f.name

        yield temp_file_path

        # 清理临时文件
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)

    async def test_read_file_tool_initialization(self, read_file_tool):
        """测试读取文件工具初始化"""
        assert read_file_tool.name == "read_file"
        assert "Reads a file from the local filesystem" in read_file_tool.description
        assert "file_path" in read_file_tool.input_schema["properties"]
        assert "file_path" in read_file_tool.input_schema["required"]
        assert read_file_tool.is_read_only() is True
        assert read_file_tool.is_concurrency_safe() is True

    async def test_read_text_file_success(
        self, read_file_tool, tool_context, temp_text_file
    ):
        """测试成功读取文本文件"""
        args = {"file_path": temp_text_file}

        result = await read_file_tool.call(args, tool_context)

        assert result.is_success is True
        assert result.event_data is not None
        assert "# 文件:" in result.event_data
        assert "第一行内容" in result.event_data
        assert "第五行内容" in result.event_data

    async def test_read_text_file_with_offset_and_limit(
        self, read_file_tool, tool_context, temp_text_file
    ):
        """测试使用偏移量和限制读取文本文件"""
        args = {"file_path": temp_text_file, "offset": 1, "limit": 2}

        result = await read_file_tool.call(args, tool_context)

        assert result.is_success is True
        assert result.event_data is not None
        assert "# 显示行 2 到 3" in result.event_data
        assert "第二行内容" in result.event_data
        assert "第三行内容" in result.event_data
        assert "第一行内容" not in result.event_data
        assert "第四行内容" not in result.event_data

    async def test_read_empty_file(self, read_file_tool, tool_context, temp_empty_file):
        """测试读取空文件"""
        args = {"file_path": temp_empty_file}

        result = await read_file_tool.call(args, tool_context)

        assert result.is_success is True
        assert result.event_data is not None
        assert "# 文件:" in result.event_data

    async def test_read_long_line_file(
        self, read_file_tool, tool_context, temp_long_line_file
    ):
        """测试读取包含长行的文件"""
        args = {"file_path": temp_long_line_file}

        result = await read_file_tool.call(args, tool_context)

        assert result.is_success is True
        assert result.event_data is not None
        assert "正常行" in result.event_data
        assert "另一正常行" in result.event_data

    async def test_read_nonexistent_file(self, read_file_tool, tool_context):
        """测试读取不存在的文件"""
        args = {"file_path": "/nonexistent/file.txt"}

        result = await read_file_tool.call(args, tool_context)

        assert result.is_error is True
        assert "文件不存在" in result.error_message

    async def test_read_directory(self, read_file_tool, tool_context):
        """测试读取目录（应该失败）"""
        # 使用一个存在的目录路径
        test_dir = os.path.dirname(__file__)
        args = {"file_path": test_dir}

        result = await read_file_tool.call(args, tool_context)

        assert result.is_error is True
        assert "路径不是文件" in result.error_message

    async def test_read_with_relative_path(self, read_file_tool, tool_context):
        """测试使用相对路径（应该失败）"""
        args = {"file_path": "relative/path.txt"}

        result = await read_file_tool.call(args, tool_context)

        assert result.is_error is True
        assert "文件路径必须是绝对路径" in result.error_message

    async def test_read_without_file_path(self, read_file_tool, tool_context):
        """测试不提供文件路径（应该失败）"""
        args = {}

        result = await read_file_tool.call(args, tool_context)

        assert result.is_error is True
        assert "缺少必需参数: file_path" in result.error_message

    async def test_read_with_negative_offset(
        self, read_file_tool, tool_context, temp_text_file
    ):
        """测试使用负偏移量（应该被修正为0）"""
        args = {"file_path": temp_text_file, "offset": -1, "limit": 2}

        result = await read_file_tool.call(args, tool_context)

        assert result.is_success is True
        assert result.event_data is not None
        assert "第一行内容" in result.event_data
        assert "第二行内容" in result.event_data

    async def test_read_with_offset_beyond_file_length(
        self, read_file_tool, tool_context, temp_text_file
    ):
        """测试使用超出文件长度的偏移量"""
        args = {"file_path": temp_text_file, "offset": 100, "limit": 2}

        result = await read_file_tool.call(args, tool_context)

        assert result.is_success is True
        assert result.event_data is not None
        assert "# 文件为空或偏移量超出范围" in result.event_data

    async def test_read_image_file(self, read_file_tool, tool_context):
        """测试读取图像文件"""
        # 创建一个简单的图像文件（实际上是一个小文件）
        with tempfile.NamedTemporaryFile(mode="wb", delete=False, suffix=".png") as f:
            # 写入一些伪图像数据
            f.write(b"fake_image_data")
            temp_image_path = f.name

        try:
            args = {"file_path": temp_image_path}

            result = await read_file_tool.call(args, tool_context)

            assert result.is_success is True
            assert result.event_data is not None
            assert "type" in result.event_data
            assert "image" in result.event_data
            assert "format" in result.event_data
            assert "png" in result.event_data
        finally:
            # 清理临时文件
            if os.path.exists(temp_image_path):
                os.unlink(temp_image_path)

    async def test_read_pdf_file(self, read_file_tool, tool_context):
        """测试读取PDF文件"""
        # 创建一个简单的PDF文件（实际上是一个小文件）
        with tempfile.NamedTemporaryFile(mode="wb", delete=False, suffix=".pdf") as f:
            # 写入一些伪PDF数据
            f.write(b"fake_pdf_data")
            temp_pdf_path = f.name

        try:
            args = {"file_path": temp_pdf_path}

            result = await read_file_tool.call(args, tool_context)

            assert result.is_success is True
            assert result.event_data is not None
            assert "type" in result.event_data
            assert "pdf" in result.event_data
            assert "path" in result.event_data
            assert "size" in result.event_data
            assert "message" in result.event_data
        finally:
            # 清理临时文件
            if os.path.exists(temp_pdf_path):
                os.unlink(temp_pdf_path)
