import pytest
import pytest_asyncio
from one_dragon_agent.core.agent.tool.permission.permission_manager import Permission<PERSON>anager


@pytest_asyncio.fixture
async def permission_manager():
    """Create a permission manager for testing."""
    return PermissionManager()


@pytest.mark.asyncio
@pytest.mark.timeout(5)
class TestGrant:
    """Tests for the grant method of PermissionManager."""

    async def test_grant_single_permission(self, permission_manager):
        """
        Tests that granting a permission correctly updates the state.
        """
        session_id = "test_session"
        permission = "filesystem.write"
        
        # Grant the permission
        await permission_manager.grant(session_id, permission)
        
        # Check that the permission is granted
        assert await permission_manager.is_granted(session_id, permission)

    async def test_grant_multiple_permissions(self, permission_manager):
        """
        Tests that multiple permissions can be granted to the same session.
        """
        session_id = "test_session"
        permission1 = "filesystem.write"
        permission2 = "filesystem.read"
        permission3 = "network.request"
        
        # Grant multiple permissions
        await permission_manager.grant(session_id, permission1)
        await permission_manager.grant(session_id, permission2)
        await permission_manager.grant(session_id, permission3)
        
        # Check that all permissions are granted
        assert await permission_manager.is_granted(session_id, permission1)
        assert await permission_manager.is_granted(session_id, permission2)
        assert await permission_manager.is_granted(session_id, permission3)