import asyncio
import pytest
import pytest_asyncio
from one_dragon_agent.core.agent.tool.permission.permission_manager import Permission<PERSON><PERSON>ger


@pytest_asyncio.fixture
async def permission_manager():
    """Create a permission manager for testing."""
    return PermissionManager()


@pytest.mark.asyncio
@pytest.mark.timeout(5)
class TestGetResponseSignal:
    """Tests for the get_response_signal method of PermissionManager."""

    async def test_get_response_signal_granted(self, permission_manager):
        """
        Tests the asynchronous signaling mechanism for permission responses when granted.
        """
        session_id = "test_session_signal"
        permission = "shell.execute"
        
        async def waiter():
            # This task will block until the signal is set
            granted = await permission_manager.get_response_signal(session_id, permission)
            return granted

        async def setter():
            # Give the waiter a moment to start waiting
            await asyncio.sleep(0.01)
            # Set the signal with a 'True' response
            await permission_manager.set_response_signal(session_id, permission, True)

        # Run both tasks concurrently
        waiter_task = asyncio.create_task(waiter())
        setter_task = asyncio.create_task(setter())

        # Wait for both to complete
        await asyncio.gather(setter_task, waiter_task)

        # Check that the waiter task received the correct result
        assert waiter_task.result() is True

    async def test_get_response_signal_denied(self, permission_manager):
        """
        Tests the asynchronous signaling mechanism for a denied permission response.
        """
        session_id = "test_session_signal_denied"
        permission = "filesystem.read"

        async def waiter():
            granted = await permission_manager.get_response_signal(session_id, permission)
            return granted

        async def setter():
            await asyncio.sleep(0.01)
            # Set the signal with a 'False' response
            await permission_manager.set_response_signal(session_id, permission, False)

        waiter_task = asyncio.create_task(waiter())
        setter_task = asyncio.create_task(setter())
        await asyncio.gather(setter_task, waiter_task)

        assert waiter_task.result() is False