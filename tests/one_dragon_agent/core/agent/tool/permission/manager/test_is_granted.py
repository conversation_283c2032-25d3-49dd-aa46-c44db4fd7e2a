import pytest
import pytest_asyncio
from one_dragon_agent.core.agent.tool.permission.permission_manager import Permission<PERSON>anager


@pytest_asyncio.fixture
async def permission_manager():
    """Create a permission manager for testing."""
    return PermissionManager()


@pytest.mark.asyncio
@pytest.mark.timeout(5)
class TestIsGranted:
    """Tests for the is_granted method of PermissionManager."""

    async def test_is_granted_initially_false(self, permission_manager):
        """
        Tests that a permission is initially not granted.
        """
        session_id = "test_session"
        permission = "filesystem.write"
        
        # Initially, permission should not be granted
        assert not await permission_manager.is_granted(session_id, permission)

    async def test_is_granted_after_grant(self, permission_manager):
        """
        Tests that is_granted returns True after granting a permission.
        """
        session_id = "test_session"
        permission = "filesystem.write"
        
        # Grant the permission
        await permission_manager.grant(session_id, permission)
        
        # Now, permission should be granted
        assert await permission_manager.is_granted(session_id, permission)