import asyncio
import pytest
import pytest_asyncio
from one_dragon_agent.core.agent.tool.permission.permission_manager import Permission<PERSON>anager


@pytest_asyncio.fixture
async def permission_manager():
    """Create a permission manager for testing."""
    return PermissionManager()


@pytest.mark.asyncio
@pytest.mark.timeout(5)
class TestSessionIsolation:
    """Tests for session isolation in PermissionManager."""

    async def test_session_isolation_permissions(self, permission_manager):
        """
        Tests that permissions granted in one session do not affect another session.
        """
        session_id_1 = "session_A"
        session_id_2 = "session_B"
        permission = "network.request"

        # Grant permission for session_A
        await permission_manager.grant(session_id_1, permission)

        # Check that session_A has the permission
        assert await permission_manager.is_granted(session_id_1, permission)
        # Check that session_B does NOT have the permission
        assert not await permission_manager.is_granted(session_id_2, permission)

    async def test_session_isolation_multiple_sessions(self, permission_manager):
        """
        Tests that multiple sessions can have different permissions.
        """
        session_a = "session_A"
        session_b = "session_B"
        session_c = "session_C"
        perm1 = "filesystem.write"
        perm2 = "filesystem.read"
        perm3 = "network.request"

        # Grant different permissions to different sessions
        await permission_manager.grant(session_a, perm1)
        await permission_manager.grant(session_a, perm2)
        await permission_manager.grant(session_b, perm2)
        await permission_manager.grant(session_b, perm3)
        await permission_manager.grant(session_c, perm1)
        await permission_manager.grant(session_c, perm3)

        # Check session A permissions
        assert await permission_manager.is_granted(session_a, perm1)
        assert await permission_manager.is_granted(session_a, perm2)
        assert not await permission_manager.is_granted(session_a, perm3)

        # Check session B permissions
        assert not await permission_manager.is_granted(session_b, perm1)
        assert await permission_manager.is_granted(session_b, perm2)
        assert await permission_manager.is_granted(session_b, perm3)

        # Check session C permissions
        assert await permission_manager.is_granted(session_c, perm1)
        assert not await permission_manager.is_granted(session_c, perm2)
        assert await permission_manager.is_granted(session_c, perm3)