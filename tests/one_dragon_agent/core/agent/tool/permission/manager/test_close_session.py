import asyncio
import pytest
import pytest_asyncio
from one_dragon_agent.core.agent.tool.permission.permission_manager import Permission<PERSON><PERSON>ger


@pytest_asyncio.fixture
async def permission_manager():
    """Create a permission manager for testing."""
    return PermissionManager()


@pytest.mark.asyncio
@pytest.mark.timeout(5)
class TestCloseSession:
    """Tests for the close_session method of PermissionManager."""

    async def test_close_session_permissions(self, permission_manager):
        """
        Tests that closing a session properly cleans up its permissions.
        """
        session_id = "test_session_close"
        permission = "filesystem.write"

        # Grant permission
        await permission_manager.grant(session_id, permission)
        assert await permission_manager.is_granted(session_id, permission)

        # Close the session
        await permission_manager.close_session(session_id)

        # The permission should now be gone
        assert not await permission_manager.is_granted(session_id, permission)

    async def test_close_session_pending_events(self, permission_manager):
        """
        Tests that closing a session properly cleans up pending events.
        """
        session_id = "test_session_close"
        permission = "filesystem.write"

        # Grant permission
        await permission_manager.grant(session_id, permission)
        assert await permission_manager.is_granted(session_id, permission)

        # Start a waiter task that will be cancelled by the cleanup
        waiter_task = asyncio.create_task(permission_manager.get_response_signal(session_id, "pending_permission"))

        # Give the waiter a moment to start
        await asyncio.sleep(0.01)

        # Close the session
        await permission_manager.close_session(session_id)

        # The permission should now be gone
        assert not await permission_manager.is_granted(session_id, permission)

        # The waiter task should have been cleaned up from the internal dict
        event_key = f"{session_id}_pending_permission"
        assert event_key not in permission_manager._permission_events
        
        # Cancel the task to avoid it running forever in the test suite
        waiter_task.cancel()
        try:
            await waiter_task
        except asyncio.CancelledError:
            pass  # Expected