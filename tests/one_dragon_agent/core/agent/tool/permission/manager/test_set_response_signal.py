import asyncio
import pytest
import pytest_asyncio
from one_dragon_agent.core.agent.tool.permission.permission_manager import Permission<PERSON><PERSON>ger


@pytest_asyncio.fixture
async def permission_manager():
    """Create a permission manager for testing."""
    return PermissionManager()


@pytest.mark.asyncio
@pytest.mark.timeout(5)
class TestSetResponseSignal:
    """Tests for the set_response_signal method of PermissionManager."""

    async def test_set_response_signal_granted(self, permission_manager):
        """
        Tests setting a permission response signal to granted.
        """
        session_id = "test_session"
        permission = "shell.execute"
        
        # Create a task that waits for the permission signal
        async def waiter():
            granted = await permission_manager.get_response_signal(session_id, permission)
            return granted
        
        # Start the waiter task
        waiter_task = asyncio.create_task(waiter())
        
        # Give the waiter a moment to start waiting
        await asyncio.sleep(0.01)
        
        # Set the signal with a 'True' response
        await permission_manager.set_response_signal(session_id, permission, True)
        
        # Wait for the waiter task to complete
        result = await waiter_task
        
        # Check that the waiter task received the correct result
        assert result is True

    async def test_set_response_signal_denied(self, permission_manager):
        """
        Tests setting a permission response signal to denied.
        """
        session_id = "test_session"
        permission = "filesystem.read"
        
        # Create a task that waits for the permission signal
        async def waiter():
            granted = await permission_manager.get_response_signal(session_id, permission)
            return granted
        
        # Start the waiter task
        waiter_task = asyncio.create_task(waiter())
        
        # Give the waiter a moment to start waiting
        await asyncio.sleep(0.01)
        
        # Set the signal with a 'False' response
        await permission_manager.set_response_signal(session_id, permission, False)
        
        # Wait for the waiter task to complete
        result = await waiter_task
        
        # Check that the waiter task received the correct result
        assert result is False