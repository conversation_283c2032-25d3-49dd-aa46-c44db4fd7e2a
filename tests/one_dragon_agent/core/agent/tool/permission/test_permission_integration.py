import asyncio
import pytest
import pytest_asyncio
from unittest.mock import Mock, AsyncMock

from one_dragon_agent.core.agent.tool.permission.permission_manager import PermissionManager
from one_dragon_agent.core.agent.tool.tool_manager import Tool<PERSON>anager
from one_dragon_agent.core.agent.tool.context import ToolExecutionContext
from one_dragon_agent.core.agent.tool.tool import OdaTool
from one_dragon_agent.core.agent.tool.permission import permission
from one_dragon_agent.core.agent.tool.result import ToolR<PERSON>ult, ToolExecutionStatus
from one_dragon_agent.core.event.dispatcher import EventDispatcher


class MockTool(OdaTool):
    """A mock tool for testing permissions."""
    
    def __init__(self, name: str = "mock_tool", required_perms: set = None):
        input_schema = {
            "type": "object",
            "properties": {
                "test_param": {
                    "type": "string",
                    "description": "A test parameter",
                },
            },
            "required": [],
            "additionalProperties": False,
        }
        
        super().__init__(
            name=name,
            description="A mock tool for testing",
            input_schema=input_schema,
        )
        self.required_perms = required_perms or set()
        self.call_count = 0
    
    @property
    def required_permissions(self) -> set[str]:
        return self.required_perms
    
    async def call(self, params: dict, context: ToolExecutionContext) -> ToolResult:
        self.call_count += 1
        return ToolResult(
            status=ToolExecutionStatus.SUCCESS,
            event_data={"result": "success"},
            model_message="Tool executed successfully",
        )
    
    def is_read_only(self) -> bool:
        return True
    
    def is_concurrency_safe(self) -> bool:
        return True


@pytest_asyncio.fixture
async def permission_manager():
    """Create a permission manager for testing."""
    return PermissionManager()


@pytest_asyncio.fixture
async def event_dispatcher():
    """Create a mock event dispatcher for testing."""
    return Mock(spec=EventDispatcher)


@pytest.mark.asyncio
@pytest.mark.timeout(5)
class TestPermissionIntegration:
    """Integration tests for the permission system."""

    async def test_permission_grant_and_check(self, permission_manager):
        """Test granting and checking permissions."""
        session_id = "test_session"
        perm = permission.FILESYSTEM_READ
        
        # Initially, permission should not be granted
        assert not await permission_manager.is_granted(session_id, perm)
        
        # Grant the permission
        await permission_manager.grant(session_id, perm)
        
        # Now, permission should be granted
        assert await permission_manager.is_granted(session_id, perm)

    async def test_permission_session_isolation(self, permission_manager):
        """Test that permissions are isolated between sessions."""
        session_a = "session_A"
        session_b = "session_B"
        perm = permission.FILESYSTEM_WRITE
        
        # Grant permission for session_A
        await permission_manager.grant(session_a, perm)
        
        # Check that session_A has the permission
        assert await permission_manager.is_granted(session_a, perm)
        # Check that session_B does NOT have the permission
        assert not await permission_manager.is_granted(session_b, perm)

    async def test_permission_signal_mechanism(self, permission_manager):
        """Test the permission signal mechanism."""
        session_id = "test_session"
        perm = permission.FILESYSTEM_READ
        
        # Create a task that waits for the permission signal
        async def waiter():
            granted = await permission_manager.get_response_signal(session_id, perm)
            return granted
        
        # Start the waiter task
        waiter_task = asyncio.create_task(waiter())
        
        # Give the waiter a moment to start waiting
        await asyncio.sleep(0.01)
        
        # Set the signal with a 'True' response
        await permission_manager.set_response_signal(session_id, perm, True)
        
        # Wait for the waiter task to complete
        result = await waiter_task
        
        # Check that the waiter task received the correct result
        assert result is True

    async def test_permission_signal_denied(self, permission_manager):
        """Test the permission signal mechanism when denied."""
        session_id = "test_session"
        perm = permission.FILESYSTEM_WRITE
        
        # Create a task that waits for the permission signal
        async def waiter():
            granted = await permission_manager.get_response_signal(session_id, perm)
            return granted
        
        # Start the waiter task
        waiter_task = asyncio.create_task(waiter())
        
        # Give the waiter a moment to start waiting
        await asyncio.sleep(0.01)
        
        # Set the signal with a 'False' response
        await permission_manager.set_response_signal(session_id, perm, False)
        
        # Wait for the waiter task to complete
        result = await waiter_task
        
        # Check that the waiter task received the correct result
        assert result is False

    async def test_session_cleanup(self, permission_manager):
        """Test that session cleanup works properly."""
        session_id = "test_session"
        perm = permission.FILESYSTEM_READ
        
        # Grant permission
        await permission_manager.grant(session_id, perm)
        assert await permission_manager.is_granted(session_id, perm)
        
        # Close the session
        await permission_manager.close_session(session_id)
        
        # The permission should now be gone
        assert not await permission_manager.is_granted(session_id, perm)