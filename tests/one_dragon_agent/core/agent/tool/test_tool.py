import pytest
from typing import Any, Dict, AsyncGenerator
import time

from one_dragon_agent.core.agent.tool.tool import OdaTool
from one_dragon_agent.core.agent.tool.result import ToolR<PERSON>ult, ToolExecutionStatus
from one_dragon_agent.core.model.message import (
    OdaModelMessage,
    OdaModelMessageRole,
    OdaMessageTextContent,
)
from one_dragon_agent.core.event.event import Event


class SampleTool(OdaTool):
    """A test implementation of OdaTool for testing purposes."""

    def __init__(self):
        input_schema = {
            "type": "object",
            "properties": {"param1": {"type": "string"}, "param2": {"type": "number"}},
            "required": ["param1"],
        }
        super().__init__(
            name="test_tool",
            description="A test tool for unit testing",
            input_schema=input_schema,
        )
        self.call_count = 0

    @property
    def required_permissions(self) -> set[str]:
        """Return the set of required permissions for this tool."""
        return set()  # No special permissions required for this test tool

    async def call(self, params: Dict[str, Any], context: Any) -> ToolResult:
        self.call_count += 1
        result = f"Executed with params: {params}"
        return ToolResult(
            status=ToolExecutionStatus.SUCCESS,
            event_data=result,
            model_message=result,
        )

    def is_read_only(self) -> bool:
        return True

    def is_concurrency_safe(self) -> bool:
        return True

    def create_start_event(
        self, tool_call_id: str, params: Dict[str, Any], context: Any
    ) -> Event:
        """Create a start event for testing."""
        # Create a simple event without data
        event = Event(event_type="tool.start")
        event.tool_call_id = tool_call_id
        event.tool_name = self.name
        event.params = params
        event.timestamp = time.time()
        return event

    def create_complete_event(
        self,
        tool_result: ToolResult,
        context: Any,
        tool_call_id: str,
        execution_time: float,
    ) -> Event:
        """Create a result event for testing."""
        # Create a simple event without data
        event = Event(event_type="tool.result")
        event.tool_call_id = tool_call_id
        event.tool_name = self.name
        event.status = tool_result.status.value
        event.execution_time = execution_time
        event.timestamp = time.time()
        return event


class TestOdaTool:
    """Test cases for the OdaTool base class."""

    def test_tool_initialization(self):
        """Test that a tool can be initialized with name and description."""
        tool = SampleTool()
        assert tool.name == "test_tool"
        assert tool.description == "A test tool for unit testing"

    def test_input_schema(self):
        """Test that the input schema can be accessed."""
        tool = SampleTool()
        schema = tool.input_schema
        assert isinstance(schema, dict)
        assert "properties" in schema

    def test_read_only_and_concurrency_checks(self):
        """Test that read-only and concurrency safety flags work."""
        tool = SampleTool()
        assert tool.is_read_only() is True
        assert tool.is_concurrency_safe() is True


@pytest.mark.asyncio
class TestAsyncOdaTool:
    """Async test cases for the OdaTool base class."""

    async def test_tool_execution(self):
        """Test that a tool can be executed and returns results."""
        tool = SampleTool()
        params = {"param1": "test", "param2": 42}
        context = {}

        result = await tool.call(params, context)

        assert isinstance(result, ToolResult)
        assert "Executed with params" in result.event_data
        assert tool.call_count == 1

    async def test_context_manager(self):
        """Test that the tool works as an async context manager."""
        async with SampleTool() as tool:
            assert isinstance(tool, SampleTool)
            # The tool should be automatically disposed when exiting the context


class TestOdaToolNewMethods:
    """Test cases for the new OdaTool methods."""

    def test_create_start_event(self):
        """Test that start events can be created."""
        tool = SampleTool()
        tool_call_id = "test_call_id"
        params = {"param1": "test", "param2": 42}
        context = {}

        event = tool.create_start_event(tool_call_id, params, context)

        assert isinstance(event, Event)
        assert event.event_type == "tool.start"
        assert event.tool_call_id == tool_call_id
        assert event.tool_name == "test_tool"
        assert event.params == params
        assert "timestamp" in event.__dict__

    def test_create_complete_event(self):
        """Test that result events can be created."""
        tool = SampleTool()

        # Create a sample ToolResult
        tool_result = ToolResult(
            status=ToolExecutionStatus.SUCCESS,
            event_data="test result",
            model_message="test message",
        )
        context = {}
        tool_call_id = "test_call_id"
        execution_time = 0.5

        event = tool.create_complete_event(
            tool_result, context, tool_call_id, execution_time
        )

        assert isinstance(event, Event)
        assert event.event_type == "tool.result"
        assert event.tool_call_id == tool_call_id
        assert event.tool_name == "test_tool"
        assert event.status == "success"
        assert "timestamp" in event.__dict__
