"""Tests for TodoWriteTool."""

import pytest
import asyncio
from unittest.mock import Mock
from one_dragon_agent.core.agent.tool.todo_write import TodoWriteTool
from one_dragon_agent.core.agent.tool.todo_manager import TodoManager
from one_dragon_agent.core.agent.tool.context import ToolExecutionContext
from one_dragon_agent.core.agent.tool.permission.permission_manager import PermissionManager
from one_dragon_agent.core.model.message import (
    OdaModelMessage,
    OdaModelMessageRole,
    OdaMessageTextContent,
)


class TestTodoWriteTool:
    """Test cases for TodoWriteTool."""

    @pytest.fixture
    def tool(self):
        """Create a TodoWriteTool instance for testing."""
        # Create a TodoManager for testing
        todo_manager = TodoManager()
        return TodoWriteTool(todo_manager=todo_manager)

    @pytest.fixture
    def context(self):
        """Create a tool execution context for testing."""
        # Create a mock event dispatcher with an async publish method
        mock_event_dispatcher = Mock()
        mock_event_dispatcher.publish = Mock(
            side_effect=lambda event: asyncio.sleep(0)
        )  # Mock async behavior
        
        # Create a permission manager
        permission_manager = PermissionManager()

        context = ToolExecutionContext(
            session_id="test_session", 
            event_dispatcher=mock_event_dispatcher,
            permission_manager=permission_manager
        )
        return context

    @pytest.mark.asyncio
    async def test_tool_initialization(self, tool):
        """Test that the tool is initialized correctly."""
        assert tool.name == "TodoWrite"
        assert tool.is_read_only() is False
        assert tool.is_concurrency_safe() is True

    @pytest.mark.asyncio
    async def test_input_schema(self, tool):
        """Test the input schema."""
        schema = tool.input_schema
        assert schema["type"] == "object"
        assert "todos" in schema["properties"]
        assert schema["required"] == ["todos"]

    @pytest.mark.asyncio
    async def test_call_with_valid_todos(self, tool, context):
        """Test calling the tool with valid todo data."""
        params = {
            "todos": [
                {
                    "content": "Test task 1",
                    "status": "pending",
                    "priority": "high",
                    "id": "task-1",
                },
                {
                    "content": "Test task 2",
                    "status": "in_progress",
                    "priority": "medium",
                    "id": "task-2",
                },
            ]
        }

        # Call the tool
        result = await tool.call(params, context)

        # Verify the response
        assert result.is_success is True
        assert result.event_data is not None
        # event_data is now a list of TodoItem objects
        assert isinstance(result.event_data, list)
        assert len(result.event_data) == 2

        # Verify the todos were saved
        manager = tool._todo_manager  # Use the tool's own todo manager
        todos = await manager.get_todos("test_session")
        assert len(todos) == 2
        assert todos[0].content == "Test task 1"
        assert todos[0].status.value == "pending"
        assert todos[0].priority.value == "high"
        assert todos[0].id == "task-1"
        assert todos[1].content == "Test task 2"
        assert todos[1].status.value == "in_progress"
        assert todos[1].priority.value == "medium"
        assert todos[1].id == "task-2"

    @pytest.mark.asyncio
    async def test_call_with_invalid_todo_data(self, tool, context):
        """Test calling the tool with invalid todo data."""
        params = {
            "todos": [
                {
                    "content": "",  # Invalid: empty content
                    "status": "pending",
                    "priority": "high",
                    "id": "task-1",
                }
            ]
        }

        # Call the tool
        result = await tool.call(params, context)

        # Verify the response contains an error
        assert result.is_error is True
        assert "Todo content cannot be empty" in result.error_message
