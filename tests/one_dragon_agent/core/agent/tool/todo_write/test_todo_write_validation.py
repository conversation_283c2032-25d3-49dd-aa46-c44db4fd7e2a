"""Tests for TodoWriteTool validation."""

from one_dragon_agent.core.agent.tool.todo_write import TodoWriteTool


class TestTodoWriteToolValidation:
    """Test cases for TodoWriteTool validation."""

    def setup_method(self):
        """Set up test fixtures."""
        self.tool = TodoWriteTool()

    def test_validate_todo_item_missing_required_field(self):
        """Test validation fails when required field is missing."""
        # Missing content
        todo_data = {"status": "pending", "priority": "high", "id": "task-1"}
        error = self.tool._validate_todo_item(todo_data)
        assert "Missing required field 'content'" in error

        # Missing status
        todo_data = {"content": "Test task", "priority": "high", "id": "task-1"}
        error = self.tool._validate_todo_item(todo_data)
        assert "Missing required field 'status'" in error

        # Missing priority
        todo_data = {"content": "Test task", "status": "pending", "id": "task-1"}
        error = self.tool._validate_todo_item(todo_data)
        assert "Missing required field 'priority'" in error

        # Missing id
        todo_data = {"content": "Test task", "status": "pending", "priority": "high"}
        error = self.tool._validate_todo_item(todo_data)
        assert "Missing required field 'id'" in error

    def test_validate_todo_item_empty_content(self):
        """Test validation fails when content is empty."""
        # Empty string
        todo_data = {
            "content": "",
            "status": "pending",
            "priority": "high",
            "id": "task-1",
        }
        error = self.tool._validate_todo_item(todo_data)
        assert "Todo content cannot be empty" in error

        # Whitespace only
        todo_data = {
            "content": "   ",
            "status": "pending",
            "priority": "high",
            "id": "task-1",
        }
        error = self.tool._validate_todo_item(todo_data)
        assert "Todo content cannot be empty" in error

    def test_validate_todo_item_invalid_status(self):
        """Test validation fails when status is invalid."""
        todo_data = {
            "content": "Test task",
            "status": "invalid_status",
            "priority": "high",
            "id": "task-1",
        }
        error = self.tool._validate_todo_item(todo_data)
        assert "Invalid status 'invalid_status'" in error
        assert "Must be one of: ['pending', 'in_progress', 'completed']" in error

    def test_validate_todo_item_invalid_priority(self):
        """Test validation fails when priority is invalid."""
        todo_data = {
            "content": "Test task",
            "status": "pending",
            "priority": "invalid_priority",
            "id": "task-1",
        }
        error = self.tool._validate_todo_item(todo_data)
        assert "Invalid priority 'invalid_priority'" in error
        assert "Must be one of: ['high', 'medium', 'low']" in error

    def test_validate_todo_item_valid(self):
        """Test validation passes for valid todo item."""
        todo_data = {
            "content": "Test task",
            "status": "pending",
            "priority": "high",
            "id": "task-1",
        }
        error = self.tool._validate_todo_item(todo_data)
        assert error == ""
