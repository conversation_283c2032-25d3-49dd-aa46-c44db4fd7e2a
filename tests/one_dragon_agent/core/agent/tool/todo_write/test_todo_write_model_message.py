"""Tests for TodoWriteTool model messages."""

import pytest
import asyncio
from unittest.mock import Mock
from one_dragon_agent.core.agent.tool.todo_write import TodoWriteTool
from one_dragon_agent.core.agent.tool.todo_manager import TodoManager
from one_dragon_agent.core.agent.tool.context import ToolExecutionContext
from one_dragon_agent.core.agent.tool.permission.permission_manager import PermissionManager


class TestTodoWriteToolModelMessage:
    """Test cases for TodoWriteTool model messages."""

    @pytest.fixture
    def tool(self):
        """Create a TodoWriteTool instance for testing."""
        # Create a TodoManager for testing
        todo_manager = TodoManager()
        return TodoWriteTool(todo_manager=todo_manager)

    @pytest.fixture
    def context(self):
        """Create a tool execution context for testing."""
        # Create a mock event dispatcher with an async publish method
        mock_event_dispatcher = Mock()
        mock_event_dispatcher.publish = Mock(
            side_effect=lambda event: asyncio.sleep(0)
        )  # Mock async behavior
        
        # Create a permission manager
        permission_manager = PermissionManager()

        context = ToolExecutionContext(
            session_id="test_session", 
            event_dispatcher=mock_event_dispatcher,
            permission_manager=permission_manager
        )
        return context

    @pytest.mark.asyncio
    async def test_model_message_with_todos(self, tool, context):
        """Test that the tool returns a proper message when todos are provided."""
        params = {
            "todos": [
                {
                    "content": "Test task 1",
                    "status": "pending",
                    "priority": "high",
                    "id": "task-1",
                },
                {
                    "content": "Test task 2",
                    "status": "in_progress",
                    "priority": "medium",
                    "id": "task-2",
                },
            ]
        }

        # Call the tool
        result = await tool.call(params, context)

        # Verify the response
        assert result.is_success is True
        assert "Todos have been modified successfully" in result.model_message
        assert "Your todo list has changed" in result.model_message

        # Verify event_data is a list of TodoItem objects
        assert isinstance(result.event_data, list)
        assert len(result.event_data) == 2

    @pytest.mark.asyncio
    async def test_model_message_with_empty_todos(self, tool, context):
        """Test that the tool returns a proper message when no todos are provided."""
        params = {
            "todos": []
        }

        # Call the tool
        result = await tool.call(params, context)

        # Verify the response
        assert result.is_success is True
        assert "Todos have been modified successfully" in result.model_message
        assert "Your todo list has changed" in result.model_message

        # Verify event_data is an empty list
        assert isinstance(result.event_data, list)
        assert len(result.event_data) == 0

    @pytest.mark.asyncio
    async def test_model_message_with_single_todo(self, tool, context):
        """Test that the tool returns a proper message when a single todo is provided."""
        params = {
            "todos": [
                {
                    "content": "Test task 1",
                    "status": "pending",
                    "priority": "high",
                    "id": "task-1",
                }
            ]
        }

        # Call the tool
        result = await tool.call(params, context)

        # Verify the response
        assert result.is_success is True
        assert "Todos have been modified successfully" in result.model_message
        assert "Your todo list has changed" in result.model_message

        # Verify event_data is a list with one TodoItem object
        assert isinstance(result.event_data, list)
        assert len(result.event_data) == 1