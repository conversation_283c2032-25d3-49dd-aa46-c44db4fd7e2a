"""Tests for ToolResult and ToolExecutionStatus."""

import pytest
from one_dragon_agent.core.agent.tool import <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolExecutionStatus
from one_dragon_agent.core.model.message import (
    OdaModelMessage,
    OdaModelMessageRole,
    OdaMessageTextContent,
)


class TestToolExecutionStatus:
    """Test cases for ToolExecutionStatus enum."""

    def test_status_values(self):
        """Test that all expected status values are present."""
        assert ToolExecutionStatus.SUCCESS.value == "success"
        assert ToolExecutionStatus.ERROR.value == "error"
        assert ToolExecutionStatus.NOT_FOUND.value == "not_found"

    def test_status_count(self):
        """Test that we have exactly four status values."""
        assert len(list(ToolExecutionStatus)) == 4


class TestToolResult:
    """Test cases for ToolResult class."""

    @pytest.fixture
    def model_message(self):
        """Fixture for creating a test model message."""
        return OdaModelMessage(
            role=OdaModelMessageRole.TOOL,
            content=OdaMessageTextContent(text="Test tool result"),
        )

    @pytest.fixture
    def success_result(self, model_message):
        """Fixture for creating a successful ToolResult."""
        return ToolResult(
            status=ToolExecutionStatus.SUCCESS,
            event_data={"output": "success"},
            model_message="Test tool result",
        )

    @pytest.fixture
    def error_result(self, model_message):
        """Fixture for creating an error ToolResult."""
        return ToolResult(
            status=ToolExecutionStatus.ERROR,
            event_data=None,
            model_message="Test tool result",
            error_message="Something went wrong",
        )

    @pytest.fixture
    def not_found_result(self, model_message):
        """Fixture for creating a not found ToolResult."""
        return ToolResult(
            status=ToolExecutionStatus.NOT_FOUND,
            event_data=None,
            model_message="Test tool result",
        )

    @pytest.fixture
    def permission_denied_result(self, model_message):
        """Fixture for creating a permission denied ToolResult."""
        return ToolResult(
            status=ToolExecutionStatus.PERMISSION_DENIED,
            event_data=None,
            model_message="Permission denied",
        )

    def test_tool_result_creation(self, success_result):
        """Test that ToolResult can be created with all required fields."""
        assert success_result.status == ToolExecutionStatus.SUCCESS
        assert success_result.event_data == {"output": "success"}
        assert success_result.model_message == "Test tool result"
        assert success_result.error_message is None

    def test_tool_result_with_error_message(self, error_result):
        """Test that ToolResult can be created with error message."""
        assert error_result.error_message == "Something went wrong"
        assert error_result.status == ToolExecutionStatus.ERROR

    def test_is_success_property(self, success_result, error_result, not_found_result):
        """Test the is_success property."""
        assert success_result.is_success is True
        assert error_result.is_success is False
        assert not_found_result.is_success is False

    def test_is_error_property(self, success_result, error_result, not_found_result):
        """Test the is_error property."""
        assert success_result.is_error is False
        assert error_result.is_error is True
        assert not_found_result.is_error is False

    def test_is_not_found_property(
        self, success_result, error_result, not_found_result
    ):
        """Test the is_not_found property."""
        assert success_result.is_not_found is False
        assert error_result.is_not_found is False
        assert not_found_result.is_not_found is True

    def test_is_permission_denied_property(
        self, success_result, error_result, not_found_result, permission_denied_result
    ):
        """Test the is_permission_denied property."""
        assert success_result.is_permission_denied is False
        assert error_result.is_permission_denied is False
        assert not_found_result.is_permission_denied is False
        assert permission_denied_result.is_permission_denied is True

    def test_all_status_properties_covered(
        self, success_result, error_result, not_found_result, permission_denied_result
    ):
        """Test that exactly one status property is True for each result."""
        # Success result
        assert (
            sum(
                [
                    success_result.is_success,
                    success_result.is_error,
                    success_result.is_not_found,
                    success_result.is_permission_denied,
                ]
            )
            == 1
        )

        # Error result
        assert (
            sum(
                [
                    error_result.is_success,
                    error_result.is_error,
                    error_result.is_not_found,
                    error_result.is_permission_denied,
                ]
            )
            == 1
        )

        # Not found result
        assert (
            sum(
                [
                    not_found_result.is_success,
                    not_found_result.is_error,
                    not_found_result.is_not_found,
                    not_found_result.is_permission_denied,
                ]
            )
            == 1
        )

        # Permission denied result
        assert (
            sum(
                [
                    permission_denied_result.is_success,
                    permission_denied_result.is_error,
                    permission_denied_result.is_not_found,
                    permission_denied_result.is_permission_denied,
                ]
            )
            == 1
        )
