"""Tests for tool message events in OneDragon-Agent."""

# Correct import path for src-layout
from one_dragon_agent.core.agent.tool.tool_event import (
    ToolExecutionStartEvent,
    ToolExecutionCompleteEvent,
    ToolEventType,
)


class TestToolMessageEvents:
    """Test cases for tool message events."""

    def test_tool_execution_start_event(self) -> None:
        """Test ToolExecutionStartEvent creation and attributes."""
        session_id = "test_session_123"
        tool_name = "read_file"
        tool_args = {"file_path": "/path/to/file.txt"}
        
        event = ToolExecutionStartEvent(
            session_id=session_id,
            tool_name=tool_name,
            tool_args=tool_args,
        )

        assert event.event_type == ToolEventType.EXECUTION_START
        assert event.session_id == session_id
        assert event.tool_name == tool_name
        assert event.tool_args == tool_args

        # Test with more complex args structure
        complex_args = {
            "file_path": "/path/to/file.txt",
            "options": {"encoding": "utf-8", "mode": "r"},
            "metadata": ["tag1", "tag2"],
        }
        event_complex = ToolExecutionStartEvent(
            session_id=session_id,
            tool_name=tool_name,
            tool_args=complex_args,
        )
        assert event_complex.tool_args == complex_args

    def test_tool_execution_complete_event_success(self) -> None:
        """Test ToolExecutionCompleteEvent creation and attributes for successful execution."""
        session_id = "test_session_456"
        tool_call_id = "test_call_id"
        tool_name = "read_file"
        tool_args = {"file_path": "/path/to/file.txt"}
        result = "File content here"
        execution_time = 0.1
        
        event = ToolExecutionCompleteEvent(
            session_id=session_id,
            tool_call_id=tool_call_id,
            tool_name=tool_name,
            tool_args=tool_args,
            success=True,
            execution_time=execution_time,
            result=result,
        )

        assert event.event_type == ToolEventType.EXECUTION_COMPLETE
        assert event.session_id == session_id
        assert event.tool_call_id == tool_call_id
        assert event.tool_name == tool_name
        assert event.tool_args == tool_args
        assert event.success is True
        assert event.execution_time == execution_time
        assert event.result == result
        assert event.error is None

        # Test with structured result
        structured_result = {
            "content": "File content here",
            "lines_read": 10,
            "file_info": {"size": 1024, "modified": "2023-01-01T00:00:00Z"},
        }
        event_structured = ToolExecutionCompleteEvent(
            session_id=session_id,
            tool_call_id=tool_call_id,
            tool_name=tool_name,
            tool_args=tool_args,
            success=True,
            execution_time=execution_time,
            result=structured_result,
        )
        assert event_structured.result == structured_result

    def test_tool_execution_complete_event_failure(self) -> None:
        """Test ToolExecutionCompleteEvent creation and attributes for failed execution."""
        session_id = "test_session_789"
        tool_call_id = "test_call_id"
        tool_name = "read_file"
        tool_args = {"file_path": "/path/to/nonexistent.txt"}
        error = "文件不存在: /path/to/nonexistent.txt"
        execution_time = 0.1
        
        event = ToolExecutionCompleteEvent(
            session_id=session_id,
            tool_call_id=tool_call_id,
            tool_name=tool_name,
            tool_args=tool_args,
            success=False,
            execution_time=execution_time,
            error=error,
        )

        assert event.event_type == ToolEventType.EXECUTION_COMPLETE
        assert event.session_id == session_id
        assert event.tool_call_id == tool_call_id
        assert event.tool_name == tool_name
        assert event.tool_args == tool_args
        assert event.success is False
        assert event.execution_time == execution_time
        assert event.result is None
        assert event.error == error

        # Test with structured error
        structured_error = {
            "message": "文件不存在",
            "code": 404,
            "path": "/path/to/nonexistent.txt",
        }
        event_structured = ToolExecutionCompleteEvent(
            session_id=session_id,
            tool_call_id=tool_call_id,
            tool_name=tool_name,
            tool_args=tool_args,
            success=False,
            execution_time=execution_time,
            error=structured_error,
        )
        assert event_structured.error == structured_error

    def test_tool_event_types_enum(self) -> None:
        """Test that ToolEventType enum values are correct."""
        assert ToolEventType.EXECUTION_START == "tool.execution_start"
        assert ToolEventType.EXECUTION_COMPLETE == "tool.execution_complete"
