import pytest
from unittest.mock import MagicMock
from one_dragon_agent.core.agent.agent import Agent
from one_dragon_agent.core.model.config import ModelConfig
from one_dragon_agent.core.message_manager.manager import MessageManager
from one_dragon_agent.core.sys.config import OdaSessionConfig
from one_dragon_agent.core.model.message import (
    OdaModelMessage,
    OdaModelMessageRole,
    OdaMessageTextContent,
    OdaMessageToolCallsContent,
    OdaToolCall,
)
from one_dragon_agent.core.model.client_factory import ModelClientFactory


@pytest.fixture
def mock_llm_config():
    """Fixture for a mock LLMConfig."""
    return ModelConfig(model="test-model", api_key="test-key")


@pytest.fixture
def mock_session_config(mock_llm_config):
    """Fixture for a mock OdaSessionConfig."""
    return OdaSessionConfig(common_llm_config=mock_llm_config)


class TestAgentMergeMethods:
    """
    Tests for the Agent._merge_chunk_to_text_message and _merge_chunk_to_tool_calls_message methods.
    """

    def test_merge_chunk_to_text_message(self, mock_session_config):
        """Test _merge_chunk_to_text_message method."""
        # 1. Setup
        command = OdaModelMessage(
            role=OdaModelMessageRole.USER,
            content=OdaMessageTextContent(text="Test command"),
        )
        message_manager = MessageManager()
        mock_factory = MagicMock()
        tool_manager = MagicMock()

        agent = Agent(
            session_config=mock_session_config,
            message_manager=message_manager,
            command=command,
            llm_factory=mock_factory,
            tool_manager=tool_manager,
        )

        # 2. Execute
        text_parts = ["Hello", " ", "world", "!"]
        result = agent._merge_chunk_to_text_message(text_parts)

        # 3. Verify
        assert isinstance(result, OdaModelMessage)
        assert result.role == OdaModelMessageRole.ASSISTANT
        assert isinstance(result.content, OdaMessageTextContent)
        assert result.content.text == "Hello world!"

        # Check that text_parts was cleared
        assert len(text_parts) == 0

    def test_merge_chunk_to_text_message_empty(self, mock_session_config):
        """Test _merge_chunk_to_text_message with empty list."""
        # 1. Setup
        command = OdaModelMessage(
            role=OdaModelMessageRole.USER,
            content=OdaMessageTextContent(text="Test command"),
        )
        message_manager = MessageManager()
        mock_factory = MagicMock()
        tool_manager = MagicMock()

        agent = Agent(
            session_config=mock_session_config,
            message_manager=message_manager,
            command=command,
            llm_factory=mock_factory,
            tool_manager=tool_manager,
        )

        # 2. Execute
        text_parts = []
        result = agent._merge_chunk_to_text_message(text_parts)

        # 3. Verify
        assert isinstance(result, OdaModelMessage)
        assert result.role == OdaModelMessageRole.ASSISTANT
        assert isinstance(result.content, OdaMessageTextContent)
        assert result.content.text == ""

    def test_merge_chunk_to_tool_calls_message(self, mock_session_config):
        """Test _merge_chunk_to_tool_calls_message method."""
        # 1. Setup
        command = OdaModelMessage(
            role=OdaModelMessageRole.USER,
            content=OdaMessageTextContent(text="Test command"),
        )
        message_manager = MessageManager()
        mock_factory = MagicMock()
        tool_manager = MagicMock()

        agent = Agent(
            session_config=mock_session_config,
            message_manager=message_manager,
            command=command,
            llm_factory=mock_factory,
            tool_manager=tool_manager,
        )

        # 2. Execute
        tool_calls = [
            OdaToolCall(
                tool_call_id="call_123",
                tool_name="test_tool_1",
                tool_args='{"param": "value1"}',
            ),
            OdaToolCall(
                tool_call_id="call_456",
                tool_name="test_tool_2",
                tool_args='{"param": "value2"}',
            ),
        ]
        result = agent._merge_chunk_to_tool_calls_message(tool_calls)

        # 3. Verify
        assert isinstance(result, OdaModelMessage)
        assert result.role == OdaModelMessageRole.ASSISTANT
        assert isinstance(result.content, OdaMessageToolCallsContent)
        assert len(result.content.tool_calls) == 2
        assert result.content.tool_calls[0].tool_call_id == "call_123"
        assert result.content.tool_calls[1].tool_call_id == "call_456"

        # Check that tool_calls was cleared
        assert len(tool_calls) == 0

    def test_merge_chunk_to_tool_calls_message_empty(self, mock_session_config):
        """Test _merge_chunk_to_tool_calls_message with empty list."""
        # 1. Setup
        command = OdaModelMessage(
            role=OdaModelMessageRole.USER,
            content=OdaMessageTextContent(text="Test command"),
        )
        message_manager = MessageManager()
        mock_factory = MagicMock()
        tool_manager = MagicMock()

        agent = Agent(
            session_config=mock_session_config,
            message_manager=message_manager,
            command=command,
            llm_factory=mock_factory,
            tool_manager=tool_manager,
        )

        # 2. Execute
        tool_calls = []
        result = agent._merge_chunk_to_tool_calls_message(tool_calls)

        # 3. Verify
        assert isinstance(result, OdaModelMessage)
        assert result.role == OdaModelMessageRole.ASSISTANT
        assert isinstance(result.content, OdaMessageToolCallsContent)
        assert len(result.content.tool_calls) == 0
