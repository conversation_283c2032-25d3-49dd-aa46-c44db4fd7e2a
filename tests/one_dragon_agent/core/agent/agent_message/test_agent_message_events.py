"""Tests for agent message events in OneDragon-Agent."""

import pytest

# Correct import path for src-layout
from one_dragon_agent.core.agent.agent_message import (
    AgentTextStreamStartEvent,
    AgentTextStreamContentEvent,
    AgentTextStreamCompleteEvent,
    AgentEventType,
)


class TestAgentMessageEvents:
    """Test cases for agent message events."""

    def test_agent_text_stream_start_event(self) -> None:
        """Test AgentTextStreamStartEvent creation and attributes."""
        session_id = "test_session_123"
        event = AgentTextStreamStartEvent(session_id)

        assert event.event_type == AgentEventType.TEXT_STREAM_START
        assert event.session_id == session_id

    def test_agent_text_stream_content_event(self) -> None:
        """Test AgentTextStreamContentEvent creation and attributes."""
        session_id = "test_session_456"
        text_chunk = "Hello, this is a test chunk."
        event = AgentTextStreamContentEvent(session_id, text_chunk)

        assert event.event_type == AgentEventType.TEXT_STREAM_CONTENT
        assert event.session_id == session_id
        assert event.text_chunk == text_chunk

    def test_agent_text_stream_complete_event(self) -> None:
        """Test AgentTextStreamCompleteEvent creation and attributes."""
        session_id = "test_session_789"
        full_text = "This is the complete response from the agent."
        event = AgentTextStreamCompleteEvent(session_id, full_text)

        assert event.event_type == AgentEventType.TEXT_STREAM_COMPLETE
        assert event.session_id == session_id
        assert event.full_text == full_text

    def test_agent_event_types_enum(self) -> None:
        """Test that AgentEventType enum values are correct."""
        assert AgentEventType.TEXT_STREAM_START == "agent.ai_text_stream_start"
        assert AgentEventType.TEXT_STREAM_CONTENT == "agent.ai_text_stream_content"
        assert AgentEventType.TEXT_STREAM_COMPLETE == "agent.ai_text_stream_complete"
