import json
import pytest
from pathlib import Path
from unittest.mock import patch, MagicMock

from one_dragon_agent.core.agent.agent import Agent
from one_dragon_agent.core.model.config import ModelConfig
from one_dragon_agent.core.message_manager.manager import MessageManager
from one_dragon_agent.core.sys.config import OdaSessionConfig
from one_dragon_agent.core.model.message import (
    OdaModelMessage,
    OdaModelMessageRole,
    OdaMessageTextContent,
)
from one_dragon_agent.core.model.client_factory import ModelClientFactory


@pytest.fixture
def mock_llm_config():
    """Fixture for a mock LLMConfig."""
    return ModelConfig(model="test-model", api_key="test-key")


@pytest.fixture
def mock_session_config_debug(mock_llm_config, tmp_path):
    """Fixture for a mock OdaSessionConfig with debug enabled."""
    return OdaSessionConfig(
        common_llm_config=mock_llm_config,
        debug=True,
        log_dir=str(tmp_path),
        session_id="test_session_id",
    )


@pytest.fixture
def mock_session_config_no_debug(mock_llm_config):
    """Fixture for a mock OdaSessionConfig with debug disabled."""
    return OdaSessionConfig(common_llm_config=mock_llm_config, debug=False)


class TestAgentLogMessagesForDebug:
    """
    Tests for the Agent._log_messages_for_debug method.
    """

    def test_log_messages_for_debug_enabled(self, mock_session_config_debug):
        """Test _log_messages_for_debug with debug enabled."""
        # 1. Setup
        command = OdaModelMessage(
            role=OdaModelMessageRole.USER,
            content=OdaMessageTextContent(text="Test command"),
        )
        message_manager = MessageManager()
        mock_factory = MagicMock()
        tool_manager = MagicMock()

        agent = Agent(
            session_config=mock_session_config_debug,
            message_manager=message_manager,
            command=command,
            llm_factory=mock_factory,
            tool_manager=tool_manager,
        )

        # Create input and output messages
        inputs = [
            OdaModelMessage(
                role=OdaModelMessageRole.USER,
                content=OdaMessageTextContent(text="Hello"),
            )
        ]
        outputs = [
            OdaModelMessage(
                role=OdaModelMessageRole.ASSISTANT,
                content=OdaMessageTextContent(text="Hi there!"),
            )
        ]

        # 2. Execute
        agent._log_messages_for_debug(inputs, outputs)

        # 3. Verify
        # Check that the log file was created
        log_path = Path(mock_session_config_debug.log_dir) / "messages"
        file_path = log_path / f"{mock_session_config_debug.session_id}.json"

        assert file_path.exists()

        # Check file content
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
            data = json.loads(content)

            # Should have one exchange
            assert len(data) == 1

            # Check the exchange structure
            exchange = data[0]
            assert "inputs" in exchange
            assert "outputs" in exchange
            assert len(exchange["inputs"]) == 1
            assert len(exchange["outputs"]) == 1

            # Check message content
            assert exchange["inputs"][0]["role"] == "user"
            assert exchange["inputs"][0]["content"]["text"] == "Hello"
            assert exchange["outputs"][0]["role"] == "assistant"
            assert exchange["outputs"][0]["content"]["text"] == "Hi there!"

    def test_log_messages_for_debug_disabled(self, mock_session_config_no_debug):
        """Test _log_messages_for_debug with debug disabled."""
        # 1. Setup
        command = OdaModelMessage(
            role=OdaModelMessageRole.USER,
            content=OdaMessageTextContent(text="Test command"),
        )
        message_manager = MessageManager()
        mock_factory = MagicMock()
        tool_manager = MagicMock()

        agent = Agent(
            session_config=mock_session_config_no_debug,
            message_manager=message_manager,
            command=command,
            llm_factory=mock_factory,
            tool_manager=tool_manager,
        )

        # Create input and output messages
        inputs = [
            OdaModelMessage(
                role=OdaModelMessageRole.USER,
                content=OdaMessageTextContent(text="Hello"),
            )
        ]
        outputs = [
            OdaModelMessage(
                role=OdaModelMessageRole.ASSISTANT,
                content=OdaMessageTextContent(text="Hi there!"),
            )
        ]

        # 2. Execute
        agent._log_messages_for_debug(inputs, outputs)

        # 3. Verify
        # No log file should be created
        log_path = Path(mock_session_config_no_debug.log_dir) / "messages"
        file_path = log_path / f"{mock_session_config_no_debug.session_id}.json"

        assert not file_path.exists()

    def test_log_messages_for_debug_multiple_calls(self, mock_session_config_debug):
        """Test _log_messages_for_debug with multiple calls accumulates history."""
        # 1. Setup
        command = OdaModelMessage(
            role=OdaModelMessageRole.USER,
            content=OdaMessageTextContent(text="Test command"),
        )
        message_manager = MessageManager()
        mock_factory = MagicMock()
        tool_manager = MagicMock()

        agent = Agent(
            session_config=mock_session_config_debug,
            message_manager=message_manager,
            command=command,
            llm_factory=mock_factory,
            tool_manager=tool_manager,
        )

        # First call
        inputs1 = [
            OdaModelMessage(
                role=OdaModelMessageRole.USER,
                content=OdaMessageTextContent(text="Hello"),
            )
        ]
        outputs1 = [
            OdaModelMessage(
                role=OdaModelMessageRole.ASSISTANT,
                content=OdaMessageTextContent(text="Hi there!"),
            )
        ]

        # Second call
        inputs2 = [
            OdaModelMessage(
                role=OdaModelMessageRole.USER,
                content=OdaMessageTextContent(text="How are you?"),
            )
        ]
        outputs2 = [
            OdaModelMessage(
                role=OdaModelMessageRole.ASSISTANT,
                content=OdaMessageTextContent(text="I'm doing well!"),
            )
        ]

        # 2. Execute
        agent._log_messages_for_debug(inputs1, outputs1)
        agent._log_messages_for_debug(inputs2, outputs2)

        # 3. Verify
        # Check that the log file was created
        log_path = Path(mock_session_config_debug.log_dir) / "messages"
        file_path = log_path / f"{mock_session_config_debug.session_id}.json"

        assert file_path.exists()

        # Check file content
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
            data = json.loads(content)

            # Should have two exchanges
            assert len(data) == 2

            # Check first exchange
            assert data[0]["inputs"][0]["content"]["text"] == "Hello"
            assert data[0]["outputs"][0]["content"]["text"] == "Hi there!"

            # Check second exchange
            assert data[1]["inputs"][0]["content"]["text"] == "How are you?"
            assert data[1]["outputs"][0]["content"]["text"] == "I'm doing well!"

    @patch("builtins.open", side_effect=IOError("Permission denied"))
    def test_log_messages_for_debug_io_error(
        self, mock_open, mock_session_config_debug
    ):
        """Test _log_messages_for_debug handles IO errors gracefully."""
        # 1. Setup
        command = OdaModelMessage(
            role=OdaModelMessageRole.USER,
            content=OdaMessageTextContent(text="Test command"),
        )
        message_manager = MessageManager()
        mock_factory = MagicMock()
        tool_manager = MagicMock()

        agent = Agent(
            session_config=mock_session_config_debug,
            message_manager=message_manager,
            command=command,
            llm_factory=mock_factory,
            tool_manager=tool_manager,
        )

        # Create input and output messages
        inputs = [
            OdaModelMessage(
                role=OdaModelMessageRole.USER,
                content=OdaMessageTextContent(text="Hello"),
            )
        ]
        outputs = [
            OdaModelMessage(
                role=OdaModelMessageRole.ASSISTANT,
                content=OdaMessageTextContent(text="Hi there!"),
            )
        ]

        # 2. Execute - should not raise exception
        agent._log_messages_for_debug(inputs, outputs)

        # 3. Verify - no exception was raised, test passes
        pass
