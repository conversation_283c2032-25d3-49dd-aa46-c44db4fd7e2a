import asyncio
from unittest.mock import patch, AsyncMock, MagicMock

import pytest
from one_dragon_agent.core.agent.agent import Agent
from one_dragon_agent.core.model.config import ModelConfig
from one_dragon_agent.core.message_manager.manager import MessageManager
from one_dragon_agent.core.sys.config import OdaSessionConfig
from one_dragon_agent.core.model.message import (
    OdaModelMessage,
    OdaModelMessageRole,
    OdaMessageTextContent,
    OdaMessageToolCallsContent,
    OdaToolCall,
)
from one_dragon_agent.core.model.client_factory import ModelClientFactory


@pytest.fixture
def mock_llm_config():
    """Fixture for a mock LLMConfig."""
    return ModelConfig(model="test-model", api_key="test-key")


@pytest.fixture
def mock_session_config(mock_llm_config):
    """Fixture for a mock OdaSessionConfig."""
    return OdaSessionConfig(common_llm_config=mock_llm_config)


@pytest.mark.timeout(10)
@pytest.mark.asyncio
class TestAgentCallLLM:
    """
    Tests for the Agent._call_llm method.
    """

    async def test_call_llm_with_text_stream(self, mock_session_config):
        """Test _call_llm with text streaming and event publishing."""
        # 1. Setup
        mock_chunks = [
            OdaMessageTextContent(text="Hello"),
            OdaMessageTextContent(text=" world"),
            OdaMessageTextContent(text="!"),
        ]

        async def mock_stream(messages, tools=None, **kwargs):
            for chunk in mock_chunks:
                yield chunk

        mock_llm_client = MagicMock()
        mock_llm_client.chat_completion_stream = mock_stream

        # Prepare inputs for the Agent
        command = OdaModelMessage(
            role=OdaModelMessageRole.USER,
            content=OdaMessageTextContent(text="Test command"),
        )
        message_manager = MessageManager()
        message_manager.append_message(command)

        # Create mock event dispatcher
        from one_dragon_agent.core.event.dispatcher import EventDispatcher
        from one_dragon_agent.core.agent.agent_message import (
            AgentTextStreamStartEvent,
            AgentTextStreamContentEvent,
            AgentTextStreamCompleteEvent,
        )

        event_dispatcher = EventDispatcher()
        mock_publish = AsyncMock()
        event_dispatcher.publish = mock_publish

        # Create mock factory and tool manager
        mock_factory = MagicMock(spec=ModelClientFactory)
        tool_manager = MagicMock()

        # Create agent
        agent = Agent(
            session_config=mock_session_config,
            message_manager=message_manager,
            command=command,
            llm_factory=mock_factory,
            tool_manager=tool_manager,
            event_dispatcher=event_dispatcher,
        )

        # 2. Execute
        result = await agent._call_llm(mock_llm_client)

        # 3. Verify
        # Should return one text message
        assert len(result) == 1
        assert isinstance(result[0].content, OdaMessageTextContent)
        assert result[0].content.text == "Hello world!"

        # Check events were published
        assert mock_publish.await_count == 5  # 1 start + 3 content + 1 complete

        # Check event types
        calls = mock_publish.await_args_list
        assert isinstance(calls[0][0][0], AgentTextStreamStartEvent)
        assert isinstance(calls[1][0][0], AgentTextStreamContentEvent)
        assert calls[1][0][0].text_chunk == "Hello"
        assert isinstance(calls[2][0][0], AgentTextStreamContentEvent)
        assert calls[2][0][0].text_chunk == " world"
        assert isinstance(calls[3][0][0], AgentTextStreamContentEvent)
        assert calls[3][0][0].text_chunk == "!"
        assert isinstance(calls[4][0][0], AgentTextStreamCompleteEvent)
        assert calls[4][0][0].full_text == "Hello world!"

    async def test_call_llm_with_tool_calls(self, mock_session_config):
        """Test _call_llm with tool calls."""
        # 1. Setup
        mock_tool_call = OdaToolCall(
            tool_call_id="call_123",
            tool_name="test_tool",
            tool_args='{"param": "value"}',
        )
        mock_tool_content = OdaMessageToolCallsContent(tool_calls=[mock_tool_call])

        async def mock_stream(messages, tools=None, **kwargs):
            yield mock_tool_content

        mock_llm_client = MagicMock()
        mock_llm_client.chat_completion_stream = mock_stream

        # Prepare inputs for the Agent
        command = OdaModelMessage(
            role=OdaModelMessageRole.USER,
            content=OdaMessageTextContent(text="Use tool"),
        )
        message_manager = MessageManager()
        message_manager.append_message(command)

        # Create mock factory and tool manager
        mock_factory = MagicMock(spec=ModelClientFactory)
        tool_manager = MagicMock()

        # Create agent
        agent = Agent(
            session_config=mock_session_config,
            message_manager=message_manager,
            command=command,
            llm_factory=mock_factory,
            tool_manager=tool_manager,
        )

        # 2. Execute
        result = await agent._call_llm(mock_llm_client)

        # 3. Verify
        # Should return one tool calls message
        assert len(result) == 1
        assert isinstance(result[0].content, OdaMessageToolCallsContent)
        assert len(result[0].content.tool_calls) == 1
        assert result[0].content.tool_calls[0].tool_call_id == "call_123"

    async def test_call_llm_with_mixed_content(self, mock_session_config):
        """Test _call_llm with mixed text and tool calls."""
        # 1. Setup
        mock_tool_call = OdaToolCall(
            tool_call_id="call_123",
            tool_name="test_tool",
            tool_args='{"param": "value"}',
        )
        mock_tool_content = OdaMessageToolCallsContent(tool_calls=[mock_tool_call])
        mock_text_content = OdaMessageTextContent(text="Hello")

        async def mock_stream(messages, tools=None, **kwargs):
            yield mock_text_content
            yield mock_tool_content

        mock_llm_client = MagicMock()
        mock_llm_client.chat_completion_stream = mock_stream

        # Prepare inputs for the Agent
        command = OdaModelMessage(
            role=OdaModelMessageRole.USER,
            content=OdaMessageTextContent(text="Mixed command"),
        )
        message_manager = MessageManager()
        message_manager.append_message(command)

        # Create mock event dispatcher
        from one_dragon_agent.core.event.dispatcher import EventDispatcher

        event_dispatcher = EventDispatcher()
        mock_publish = AsyncMock()
        event_dispatcher.publish = mock_publish

        # Create mock factory and tool manager
        mock_factory = MagicMock(spec=ModelClientFactory)
        tool_manager = MagicMock()

        # Create agent
        agent = Agent(
            session_config=mock_session_config,
            message_manager=message_manager,
            command=command,
            llm_factory=mock_factory,
            tool_manager=tool_manager,
            event_dispatcher=event_dispatcher,
        )

        # 2. Execute
        result = await agent._call_llm(mock_llm_client)

        # 3. Verify
        # Should return two messages: one empty tool calls (from transition), one tool calls with actual tool
        assert len(result) == 2
        assert isinstance(result[0].content, OdaMessageToolCallsContent)
        assert (
            len(result[0].content.tool_calls) == 0
        )  # Empty tool calls from text->tool transition
        assert isinstance(result[1].content, OdaMessageToolCallsContent)
        assert len(result[1].content.tool_calls) == 1  # Actual tool call
        assert result[1].content.tool_calls[0].tool_call_id == "call_123"

        # Check events were published for text
        assert mock_publish.await_count == 3  # 1 start + 1 content + 1 complete

    async def test_call_llm_with_reminder_manager(self, mock_session_config):
        """Test _call_llm with reminder manager integration."""
        # 1. Setup
        mock_chunks = [OdaMessageTextContent(text="Response")]

        async def mock_stream(messages, tools=None, **kwargs):
            for chunk in mock_chunks:
                yield chunk

        mock_llm_client = MagicMock()
        mock_llm_client.chat_completion_stream = mock_stream

        # Prepare inputs for the Agent
        command = OdaModelMessage(
            role=OdaModelMessageRole.USER,
            content=OdaMessageTextContent(text="Test command"),
        )
        message_manager = MessageManager()
        message_manager.append_message(command)

        # Create mock reminder manager
        mock_reminder_manager = MagicMock()
        mock_reminder_manager.get_system_reminder_messages = AsyncMock(
            return_value=[
                OdaModelMessage(
                    role=OdaModelMessageRole.SYSTEM,
                    content=OdaMessageTextContent(text="Reminder: Test reminder"),
                )
            ]
        )

        # Create mock factory and tool manager
        mock_factory = MagicMock(spec=ModelClientFactory)
        tool_manager = MagicMock()

        # Create agent
        agent = Agent(
            session_config=mock_session_config,
            message_manager=message_manager,
            command=command,
            llm_factory=mock_factory,
            tool_manager=tool_manager,
            reminder_manager=mock_reminder_manager,
        )

        # 2. Execute
        result = await agent._call_llm(mock_llm_client)

        # 3. Verify
        # Should return one text message
        assert len(result) == 1
        assert isinstance(result[0].content, OdaMessageTextContent)

        # Check that reminder manager was called
        mock_reminder_manager.get_system_reminder_messages.assert_called_once()

    async def test_call_llm_empty_stream(self, mock_session_config):
        """Test _call_llm with empty stream."""

        # 1. Setup
        async def mock_stream(messages, tools=None, **kwargs):
            # Empty stream
            return
            yield  # Make it an async generator

        mock_llm_client = MagicMock()
        mock_llm_client.chat_completion_stream = mock_stream

        # Prepare inputs for the Agent
        command = OdaModelMessage(
            role=OdaModelMessageRole.USER,
            content=OdaMessageTextContent(text="Test command"),
        )
        message_manager = MessageManager()
        message_manager.append_message(command)

        # Create mock factory and tool manager
        mock_factory = MagicMock(spec=ModelClientFactory)
        tool_manager = MagicMock()

        # Create agent
        agent = Agent(
            session_config=mock_session_config,
            message_manager=message_manager,
            command=command,
            llm_factory=mock_factory,
            tool_manager=tool_manager,
        )

        # 2. Execute
        result = await agent._call_llm(mock_llm_client)

        # 3. Verify
        # Should return empty list
        assert len(result) == 0
