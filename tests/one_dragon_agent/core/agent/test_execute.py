from unittest.mock import AsyncMock, MagicMock

import pytest
from one_dragon_agent.core.agent.agent import Agent
from one_dragon_agent.core.model.config import ModelConfig
from one_dragon_agent.core.message_manager.manager import MessageManager
from one_dragon_agent.core.sys.config import OdaSessionConfig
from one_dragon_agent.core.model.message import (
    OdaModelMessage,
    OdaModelMessageRole,
    OdaMessageTextContent,
)
from one_dragon_agent.core.model.client_factory import ModelClientFactory


@pytest.fixture
def mock_llm_config():
    """Fixture for a mock LLMConfig."""
    return ModelConfig(model="test-model", api_key="test-key")


@pytest.fixture
def mock_session_config(mock_llm_config):
    """Fixture for a mock OdaSessionConfig."""
    return OdaSessionConfig(common_llm_config=mock_llm_config)


@pytest.mark.timeout(10)
@pytest.mark.asyncio
class TestAgentExecute:
    """
    Tests for the Agent.execute method.
    """

    async def test_execute_streams_llm_response(self, mock_session_config):
        """
        Verifies that the execute method correctly initializes an LLM client,
        sends a request, and yields the text chunks from the response stream.
        """
        # 1. Setup
        # Create mock response chunks (OdaMessageContent objects)
        mock_chunks = [
            OdaMessageTextContent(text="Hello"),
            OdaMessageTextContent(text=","),
            OdaMessageTextContent(text=" world"),
            OdaMessageTextContent(text="!"),
        ]

        # Create an async generator from the mock chunks
        async def mock_stream(messages, tools=None, **kwargs):
            for chunk in mock_chunks:
                yield chunk

        # Create a mock LLM client factory
        mock_factory = MagicMock(spec=ModelClientFactory)
        mock_client_instance = MagicMock()
        mock_client_instance.chat_completion_stream = mock_stream
        mock_factory.create_model_client.return_value = mock_client_instance

        # Prepare inputs for the Agent
        command = OdaModelMessage(
            role=OdaModelMessageRole.USER,
            content=OdaMessageTextContent(text="Tell me a joke"),
        )
        message_manager = MessageManager()

        # 2. Instantiate and Execute the Agent
        from one_dragon_agent.core.agent.tool import ToolManager
        from one_dragon_agent.core.event.dispatcher import EventDispatcher
        from one_dragon_agent.core.agent.agent_message import (
            AgentTextStreamStartEvent,
            AgentTextStreamContentEvent,
            AgentTextStreamCompleteEvent,
        )
        from one_dragon_agent.core.agent.tool.permission.permission_manager import PermissionManager

        permission_manager = PermissionManager()
        tool_manager = ToolManager(permission_manager)
        event_dispatcher = EventDispatcher()

        # Mock event dispatcher's publish method to capture events
        mock_publish = AsyncMock()
        event_dispatcher.publish = mock_publish

        agent = Agent(
            session_config=mock_session_config,
            message_manager=message_manager,
            command=command,
            llm_factory=mock_factory,
            tool_manager=tool_manager,
            event_dispatcher=event_dispatcher,
        )

        # Execute the agent (it's now a coroutine, not an async generator)
        await agent.execute()

        # 3. Verify the results
        # Check that events were published (1 start + 4 content + 1 complete = 6 events)
        assert mock_publish.await_count == 6

        # Check the calls in order
        calls = mock_publish.await_args_list

        # First call should be AgentTextStreamStartEvent
        event1 = calls[0][0][0]
        assert isinstance(event1, AgentTextStreamStartEvent)

        # Next 4 calls should be AgentTextStreamContentEvent
        for i in range(1, 5):
            event = calls[i][0][0]
            assert isinstance(event, AgentTextStreamContentEvent)
            assert event.text_chunk == mock_chunks[i - 1].text

        # Last call should be AgentTextStreamCompleteEvent
        event6 = calls[5][0][0]
        assert isinstance(event6, AgentTextStreamCompleteEvent)
        assert event6.full_text == "Hello, world!"

        # Check that the LLM client was created correctly
        mock_factory.create_model_client.assert_called_once_with(
            mock_session_config.common_llm_config
        )

        # Note: We can't easily check if the function was called with our current mock setup
        # The important thing is that the test runs without errors and processes the chunks
        pass

    async def test_execute_handles_llm_tool_calls(self, mock_session_config):
        """
        Verifies that the execute method correctly handles tool calls from the LLM.
        """
        # 1. Setup
        # Create mock response with tool calls
        from one_dragon_agent.core.model.message import (
            OdaToolCall,
            OdaMessageToolCallsContent,
        )

        mock_tool_call = OdaToolCall(
            tool_call_id="call_123",
            tool_name="test_tool",
            tool_args='{"param": "value"}',
        )

        mock_response = OdaMessageToolCallsContent(tool_calls=[mock_tool_call])

        # Create a counter to track calls
        call_count = 0

        # Create an async generator from the mock response
        async def mock_stream(messages, tools=None, **kwargs):
            nonlocal call_count
            call_count += 1

            # First call returns tool calls
            if call_count == 1:
                yield mock_response
            # Second call returns a text response to end the loop
            else:
                yield OdaMessageTextContent(text="Task completed")

        # Create a mock LLM client factory
        mock_factory = MagicMock(spec=ModelClientFactory)
        mock_client_instance = MagicMock()
        mock_client_instance.chat_completion_stream = mock_stream
        # Create a mock for checking call count
        mock_client_instance.chat_completion_stream_mock = mock_stream
        mock_factory.create_model_client.return_value = mock_client_instance

        # Prepare inputs for the Agent
        command = OdaModelMessage(
            role=OdaModelMessageRole.USER,
            content=OdaMessageTextContent(text="Use a tool"),
        )
        message_manager = MessageManager()

        # Create a mock tool that returns a result
        mock_tool = MagicMock()

        # Create a callable object to track calls
        tool_call_count = 0

        # Create a ToolResult for the tool call result
        from one_dragon_agent.core.agent.tool.result import (
            ToolResult,
            ToolExecutionStatus,
        )

        tool_result = ToolResult(
            status=ToolExecutionStatus.SUCCESS,
            event_data={"result": "Tool result"},
            model_message="Tool result",
        )

        # Create an async function for the tool call result
        async def mock_tool_call_result(args, context):
            nonlocal tool_call_count
            tool_call_count += 1
            return tool_result

        mock_tool.call = mock_tool_call_result
        tools = {"test_tool": mock_tool}

        # 2. Instantiate and Execute the Agent
        from one_dragon_agent.core.agent.tool import ToolManager
        from one_dragon_agent.core.event.dispatcher import EventDispatcher
        from one_dragon_agent.core.agent.tool.permission.permission_manager import PermissionManager

        permission_manager = PermissionManager()
        tool_manager = ToolManager(permission_manager)
        tool_manager._tools = tools

        # Create event dispatcher for tool events
        event_dispatcher = EventDispatcher()
        mock_publish = AsyncMock()
        event_dispatcher.publish = mock_publish

        agent = Agent(
            session_config=mock_session_config,
            message_manager=message_manager,
            command=command,
            llm_factory=mock_factory,
            tool_manager=tool_manager,
            event_dispatcher=event_dispatcher,
        )

        # Execute the agent
        await agent.execute()

        # 3. Verify the results
        # Check that the LLM client was created correctly
        mock_factory.create_model_client.assert_called_once_with(
            mock_session_config.common_llm_config
        )

        # Check that the tool was called
        assert tool_call_count == 1
