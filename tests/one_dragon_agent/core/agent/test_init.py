import pytest
from unittest.mock import MagicMock

from one_dragon_agent.core.agent.agent import Agent
from one_dragon_agent.core.model.config import ModelConfig
from one_dragon_agent.core.message_manager.manager import MessageManager
from one_dragon_agent.core.sys.config import OdaSessionConfig
from one_dragon_agent.core.model.message import (
    OdaModelMessage,
    OdaModelMessageRole,
    OdaMessageTextContent,
)
from one_dragon_agent.core.model.client_factory import ModelClientFactory
from one_dragon_agent.core.agent.tool import ToolManager


@pytest.fixture
def mock_llm_config():
    """Fixture for a mock LLMConfig."""
    return ModelConfig(model="test-model", api_key="test-key")


@pytest.fixture
def mock_session_config(mock_llm_config):
    """Fixture for a mock OdaSessionConfig."""
    return OdaSessionConfig(
        common_llm_config=mock_llm_config, session_id="test_session_id"
    )


class TestAgentInit:
    """
    Tests for the Agent.__init__ method.
    """

    def test_agent_init_basic(self, mock_session_config):
        """Test Agent initialization with basic parameters."""
        # 1. Setup
        command = OdaModelMessage(
            role=OdaModelMessageRole.USER,
            content=OdaMessageTextContent(text="Test command"),
        )
        message_manager = MessageManager()
        mock_factory = MagicMock(spec=ModelClientFactory)
        tool_manager = MagicMock(spec=ToolManager)

        # 2. Execute
        agent = Agent(
            session_config=mock_session_config,
            message_manager=message_manager,
            command=command,
            llm_factory=mock_factory,
            tool_manager=tool_manager,
        )

        # 3. Verify
        assert agent.session_config == mock_session_config
        assert agent.message_manager == message_manager
        assert agent.command == command
        assert agent.llm_factory == mock_factory
        assert agent.tool_manager == tool_manager
        assert agent.tools == tool_manager.tools
        assert agent.event_dispatcher is None
        assert agent.reminder_manager is None
        assert agent.llm_exchange_history == []

        # Check tool context
        assert agent.tool_context.session_id == mock_session_config.session_id
        assert agent.tool_context.event_dispatcher is None

    def test_agent_init_with_event_dispatcher(self, mock_session_config):
        """Test Agent initialization with event dispatcher."""
        # 1. Setup
        command = OdaModelMessage(
            role=OdaModelMessageRole.USER,
            content=OdaMessageTextContent(text="Test command"),
        )
        message_manager = MessageManager()
        mock_factory = MagicMock(spec=ModelClientFactory)
        tool_manager = MagicMock(spec=ToolManager)
        mock_event_dispatcher = MagicMock()

        # 2. Execute
        agent = Agent(
            session_config=mock_session_config,
            message_manager=message_manager,
            command=command,
            llm_factory=mock_factory,
            tool_manager=tool_manager,
            event_dispatcher=mock_event_dispatcher,
        )

        # 3. Verify
        assert agent.event_dispatcher == mock_event_dispatcher
        assert agent.tool_context.event_dispatcher == mock_event_dispatcher

    def test_agent_init_with_reminder_manager(self, mock_session_config):
        """Test Agent initialization with reminder manager."""
        # 1. Setup
        command = OdaModelMessage(
            role=OdaModelMessageRole.USER,
            content=OdaMessageTextContent(text="Test command"),
        )
        message_manager = MessageManager()
        mock_factory = MagicMock(spec=ModelClientFactory)
        tool_manager = MagicMock(spec=ToolManager)
        mock_reminder_manager = MagicMock()

        # 2. Execute
        agent = Agent(
            session_config=mock_session_config,
            message_manager=message_manager,
            command=command,
            llm_factory=mock_factory,
            tool_manager=tool_manager,
            reminder_manager=mock_reminder_manager,
        )

        # 3. Verify
        assert agent.reminder_manager == mock_reminder_manager

    def test_agent_init_with_all_optional_params(self, mock_session_config):
        """Test Agent initialization with all optional parameters."""
        # 1. Setup
        command = OdaModelMessage(
            role=OdaModelMessageRole.USER,
            content=OdaMessageTextContent(text="Test command"),
        )
        message_manager = MessageManager()
        mock_factory = MagicMock(spec=ModelClientFactory)
        tool_manager = MagicMock(spec=ToolManager)
        mock_event_dispatcher = MagicMock()
        mock_reminder_manager = MagicMock()

        # 2. Execute
        agent = Agent(
            session_config=mock_session_config,
            message_manager=message_manager,
            command=command,
            llm_factory=mock_factory,
            tool_manager=tool_manager,
            event_dispatcher=mock_event_dispatcher,
            reminder_manager=mock_reminder_manager,
        )

        # 3. Verify
        assert agent.session_config == mock_session_config
        assert agent.message_manager == message_manager
        assert agent.command == command
        assert agent.llm_factory == mock_factory
        assert agent.tool_manager == tool_manager
        assert agent.tools == tool_manager.tools
        assert agent.event_dispatcher == mock_event_dispatcher
        assert agent.reminder_manager == mock_reminder_manager
        assert agent.llm_exchange_history == []

        # Check tool context
        assert agent.tool_context.session_id == mock_session_config.session_id
        assert agent.tool_context.event_dispatcher == mock_event_dispatcher

    def test_agent_init_tool_context_creation(self, mock_session_config):
        """Test that ToolExecutionContext is created with correct parameters."""
        # 1. Setup
        command = OdaModelMessage(
            role=OdaModelMessageRole.USER,
            content=OdaMessageTextContent(text="Test command"),
        )
        message_manager = MessageManager()
        mock_factory = MagicMock(spec=ModelClientFactory)
        tool_manager = MagicMock(spec=ToolManager)
        mock_event_dispatcher = MagicMock()

        # 2. Execute
        agent = Agent(
            session_config=mock_session_config,
            message_manager=message_manager,
            command=command,
            llm_factory=mock_factory,
            tool_manager=tool_manager,
            event_dispatcher=mock_event_dispatcher,
        )

        # 3. Verify
        from one_dragon_agent.core.agent.tool.context import ToolExecutionContext

        assert isinstance(agent.tool_context, ToolExecutionContext)
        assert agent.tool_context.session_id == mock_session_config.session_id
        assert agent.tool_context.event_dispatcher == mock_event_dispatcher
