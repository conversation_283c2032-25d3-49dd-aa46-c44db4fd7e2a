"""Tests for the display manager in OneDragon-Agent CLI."""

import pytest
from unittest.mock import Mock, AsyncMock

from one_dragon_agent.cli.display_manager import DisplayManager
from one_dragon_agent.cli.display import DisplayItemType


class TestDisplayManager:
    """Test cases for DisplayManager."""

    @pytest.fixture
    def mock_app(self):
        """Create a mock CLI app."""
        app = Mock()
        app.chat_container = Mock()
        app.chat_container.mount = AsyncMock()
        app.chat_container.scroll_end = Mock()
        app._is_scrolled_to_bottom = Mock(return_value=True)
        return app

    @pytest.fixture
    def display_manager(self, mock_app):
        """Create a DisplayManager instance."""
        return DisplayManager(mock_app)

    @pytest.mark.asyncio
    async def test_add_item(self, display_manager, mock_app):
        """Test adding a display item."""
        item_id = "test_item"
        item_type = DisplayItemType.SYSTEM_MESSAGE
        content = "Test message"

        await display_manager.add_item(item_id, item_type, content)

        # Verify that the item was added to the internal dictionary
        assert item_id in display_manager._display_items

        # Verify that mount was called on the chat container
        mock_app.chat_container.mount.assert_called_once()

        # Verify that scroll_end was called since we're scrolled to bottom
        mock_app.chat_container.scroll_end.assert_called_once_with(animate=False)

    @pytest.mark.asyncio
    async def test_update_item(self, display_manager, mock_app):
        """Test updating a display item."""
        item_id = "test_item"
        initial_content = "Initial message"
        updated_content = "Updated message"

        # First add an item
        await display_manager.add_item(
            item_id, DisplayItemType.SYSTEM_MESSAGE, initial_content
        )

        # Get the widget
        widget = display_manager.get_item(item_id)
        assert widget is not None
        assert widget.renderable == initial_content

        # Update the item
        await display_manager.update_item(item_id, content=updated_content)

        # Verify that the content was updated
        widget = display_manager.get_item(item_id)
        assert widget is not None
        assert widget.renderable == updated_content

    @pytest.mark.asyncio
    async def test_update_item_not_found(self, display_manager):
        """Test updating a non-existent display item."""
        # Try to update an item that doesn't exist
        await display_manager.update_item("nonexistent_item", content="New content")
        # Should not raise an exception

    @pytest.mark.asyncio
    async def test_remove_item(self, display_manager, mock_app):
        """Test removing a display item."""
        item_id = "test_item"
        content = "Test message"

        # First add an item
        await display_manager.add_item(item_id, DisplayItemType.SYSTEM_MESSAGE, content)
        assert item_id in display_manager._display_items

        # Mock the widget's remove method
        widget = display_manager.get_item(item_id)
        widget.remove = AsyncMock()

        # Remove the item
        await display_manager.remove_item(item_id)

        # Verify that the item was removed from the internal dictionary
        assert item_id not in display_manager._display_items

        # Verify that remove was called on the widget
        widget.remove.assert_called_once()

    @pytest.mark.asyncio
    async def test_remove_item_not_found(self, display_manager):
        """Test removing a non-existent display item."""
        # Try to remove an item that doesn't exist
        await display_manager.remove_item("nonexistent_item")
        # Should not raise an exception

    def test_get_item(self, display_manager):
        """Test getting a display item."""
        item_id = "test_item"
        content = "Test message"

        # Item doesn't exist yet
        assert display_manager.get_item(item_id) is None

        # Mock adding an item directly to the dictionary
        widget = Mock()
        display_manager._display_items[item_id] = widget

        # Now the item should be found
        assert display_manager.get_item(item_id) == widget
