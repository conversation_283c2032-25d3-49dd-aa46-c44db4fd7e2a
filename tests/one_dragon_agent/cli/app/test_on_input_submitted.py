"""Tests for the on_input_submitted method in OneDragon-Agent CLI."""

import asyncio
from unittest.mock import As<PERSON><PERSON><PERSON>, Mo<PERSON>, patch, MagicMock
import pytest
from textual.widgets import Input

from one_dragon_agent.cli.app import OneDragonAgentCL<PERSON>
from one_dragon_agent.core.sys.message import <PERSON>da<PERSON><PERSON><PERSON>, OdaMessageSource
from one_dragon_agent.core.model.message import OdaMessageTextContent


@pytest.mark.asyncio
@pytest.mark.timeout(10)
async def test_on_input_submitted_empty_message():
    """Test that empty messages are ignored."""
    async with OneDragonAgentCLI().run_test() as pilot:
        app = pilot.app
        await pilot.pause()  # Wait for on_mount to complete

        # Mock the session to avoid real processing
        app.agent_session = AsyncMock()
        app.agent_session.send_input = AsyncMock()

        # Get the input widget
        input_widget = app.query_one("#command-input", Input)

        # Test empty message
        input_widget.value = ""
        await pilot.click(input_widget)
        await pilot.press("enter")

        # Verify that send_input was not called
        app.agent_session.send_input.assert_not_called()

        # Test whitespace-only message
        input_widget.value = "   "
        await pilot.click(input_widget)
        await pilot.press("enter")

        # Verify that send_input was not called
        app.agent_session.send_input.assert_not_called()


@pytest.mark.asyncio
@pytest.mark.timeout(10)
async def test_on_input_submitted_valid_message():
    """Test that valid messages are processed correctly."""
    async with OneDragonAgentCLI().run_test() as pilot:
        app = pilot.app
        await pilot.pause()  # Wait for on_mount to complete

        # Mock the session to avoid real processing
        app.agent_session = AsyncMock()
        app.agent_session.send_input = AsyncMock()

        # Get the input widget
        input_widget = app.query_one("#command-input", Input)

        # Test valid message
        test_message = "Hello, AI!"
        input_widget.value = test_message
        await pilot.click(input_widget)
        await pilot.press("enter")

        # Verify that the input was cleared
        await pilot.pause()  # Wait for async processing to complete
        assert input_widget.value == ""

        # Verify that the subtitle was updated
        assert "Processing:" in app.sub_title
        assert test_message[:20] in app.sub_title

        # Verify that send_input was called with the correct message
        app.agent_session.send_input.assert_called_once()
        call_args = app.agent_session.send_input.call_args[0][0]
        assert isinstance(call_args, OdaMessage)
        assert call_args.source == OdaMessageSource.USER
        assert isinstance(call_args.content, OdaMessageTextContent)
        assert call_args.content.text == test_message

        # Verify that user message widget was created
        await pilot.pause()  # Wait for async processing to complete
        user_widgets = app.query(".user-message")
        assert len(user_widgets) == 1
        user_widget = user_widgets.first()
        assert test_message in str(user_widget.renderable)


@pytest.mark.asyncio
@pytest.mark.timeout(10)
async def test_on_input_submitted_no_empty_ai_message():
    """Test that no empty AI message is created on input submission."""
    async with OneDragonAgentCLI().run_test() as pilot:
        app = pilot.app
        await pilot.pause()  # Wait for on_mount to complete

        # Mock the session to avoid real processing
        app.agent_session = AsyncMock()
        app.agent_session.send_input = AsyncMock()

        # Get the input widget
        input_widget = app.query_one("#command-input", Input)

        # Test valid message
        test_message = "Hello, AI!"
        input_widget.value = test_message
        await pilot.click(input_widget)
        await pilot.press("enter")

        # Verify that no empty AI message was created
        await pilot.pause()  # Wait for async processing to complete
        # There should be no AI message widgets since we haven't received any AI response
        ai_widgets = app.query(".ai-message")
        assert len(ai_widgets) == 0

        # Verify that user message widget was created
        user_widgets = app.query(".user-message")
        assert len(user_widgets) == 1


@pytest.mark.asyncio
@pytest.mark.timeout(10)
async def test_on_input_submitted_error_handling():
    """Test that errors during message sending are handled correctly."""
    async with OneDragonAgentCLI().run_test() as pilot:
        app = pilot.app
        await pilot.pause()  # Wait for on_mount to complete

        # Mock the session to raise an exception
        app.agent_session = AsyncMock()
        app.agent_session.send_input.side_effect = Exception("Test error")

        # Get the input widget
        input_widget = app.query_one("#command-input", Input)

        # Test valid message
        test_message = "Hello, AI!"
        input_widget.value = test_message
        await pilot.click(input_widget)
        await pilot.press("enter")

        # Wait for async processing to complete
        await asyncio.sleep(0.1)
        await pilot.pause()  # Wait for UI updates

        # Verify that error message was displayed
        system_widgets = app.query(".system-message")
        assert len(system_widgets) == 1
        system_widget = system_widgets.first()
        assert "Error sending message" in str(system_widget.renderable)
        assert "Test error" in str(system_widget.renderable)


@pytest.mark.asyncio
@pytest.mark.timeout(10)
async def test_on_input_submitted_scroll_behavior():
    """Test that scrolling behavior works correctly."""
    async with OneDragonAgentCLI().run_test() as pilot:
        app = pilot.app
        await pilot.pause()  # Wait for on_mount to complete

        # Mock the session to avoid real processing
        app.agent_session = AsyncMock()
        app.agent_session.send_input = AsyncMock()

        # Mock scroll behavior
        app.chat_container.scroll_end = Mock()

        # Get the input widget
        input_widget = app.query_one("#command-input", Input)

        # Test valid message
        test_message = "Hello, AI!"
        input_widget.value = test_message
        await pilot.click(input_widget)
        await pilot.press("enter")

        await pilot.pause()  # Wait for async processing to complete

        # Verify that scroll_end was called
        app.chat_container.scroll_end.assert_called()
