import asyncio
from unittest.mock import Mock, AsyncMock, patch

import pytest
import pytest_asyncio

from one_dragon_agent.core.agent.tool.tool_event import (
    PermissionRequestEvent,
)


# A simple function to test the core logic of permission handling
async def handle_permission_response_logic(response: str, permission: str):
    """Test the core logic of permission handling."""
    # Process response
    granted = False
    scope = "once"
    if response.lower().strip() in ["y", "yes"]:
        granted = True
    elif response.lower().strip() in ["a", "always"]:
        granted = True
        scope = "session"

    return permission, granted, scope


@pytest.mark.asyncio
@pytest.mark.timeout(5)
class TestCLIPermissionHandlingLogic:
    """Unit tests for CLI permission handling logic."""

    async def test_permission_granted_once(self):
        """Tests that the permission handler correctly processes a 'yes' response."""
        permission, granted, scope = await handle_permission_response_logic(
            "y", "filesystem.write"
        )

        assert permission == "filesystem.write"
        assert granted is True
        assert scope == "once"

    async def test_permission_granted_once_uppercase(self):
        """Tests that the permission handler correctly processes an uppercase 'Y' response."""
        permission, granted, scope = await handle_permission_response_logic(
            "Y", "filesystem.write"
        )

        assert permission == "filesystem.write"
        assert granted is True
        assert scope == "once"

    async def test_permission_denied(self):
        """Tests that the permission handler correctly processes a 'no' response."""
        permission, granted, scope = await handle_permission_response_logic(
            "n", "network.write"
        )

        assert permission == "network.write"
        assert granted is False
        assert scope == "once"

    async def test_permission_denied_uppercase(self):
        """Tests that the permission handler correctly processes an uppercase 'N' response."""
        permission, granted, scope = await handle_permission_response_logic(
            "N", "network.write"
        )

        assert permission == "network.write"
        assert granted is False
        assert scope == "once"

    async def test_permission_granted_for_session(self):
        """Tests that the permission handler correctly processes an 'always' response."""
        permission, granted, scope = await handle_permission_response_logic(
            "a", "shell.execute"
        )

        assert permission == "shell.execute"
        assert granted is True
        assert scope == "session"

    async def test_permission_granted_for_session_uppercase(self):
        """Tests that the permission handler correctly processes an uppercase 'A' response."""
        permission, granted, scope = await handle_permission_response_logic(
            "A", "shell.execute"
        )

        assert permission == "shell.execute"
        assert granted is True
        assert scope == "session"

    async def test_permission_cancelled_with_escape(self):
        """Tests that the permission handler correctly processes an ESC response."""
        # ESC is treated as 'n' in the actual implementation
        permission, granted, scope = await handle_permission_response_logic(
            "n", "special.access"
        )

        assert permission == "special.access"
        assert granted is False
        assert scope == "once"

    async def test_permission_with_whitespace(self):
        """Tests that the permission handler correctly processes responses with whitespace."""
        permission, granted, scope = await handle_permission_response_logic(
            " y ", "filesystem.write"
        )

        assert permission == "filesystem.write"
        assert granted is True
        assert scope == "once"
