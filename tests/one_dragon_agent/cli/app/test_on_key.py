from unittest.mock import MagicMock, AsyncMock, patch

import pytest
from textual.events import Key
from textual.widgets import Input

from one_dragon_agent.cli.app import OneDragonAgentCLI
from one_dragon_agent.core.agent.agent import Agent
from one_dragon_agent.core.model.base import ModelClient
from one_dragon_agent.core.model.config import ModelConfig
from one_dragon_agent.core.model.client_factory import Model<PERSON>lientFactory
from one_dragon_agent.core.sys.config import OdaSessionConfig
from one_dragon_agent.core.sys.message import OdaMessage, OdaMessageSource
from one_dragon_agent.core.model.message import (
    OdaModelMessageRole,
    OdaMessageTextContent,
)
from one_dragon_agent.core.sys.session import OdaSession


class MockModelClientFactory(ModelClientFactory):
    """A mock factory that returns a mock LLM client."""

    def __init__(self):
        super().__init__()
        self.mock_client = MagicMock(spec=ModelClient)

        # Mock the async generator method
        async def mock_stream(*args, **kwargs):
            yield MagicMock(
                choices=[MagicMock(delta=MagicMock(content="Mock response"))]
            )

        self.mock_client.chat_completion_stream = AsyncMock(side_effect=mock_stream)
        self.mock_client.close = AsyncMock()

    def create_model_client(self, config: ModelConfig) -> ModelClient:
        return self.mock_client


@pytest.mark.timeout(10)
class TestOneDragonAgentCLIOnKey:
    """Test cases for the on_key method of OneDragonAgentCLI."""

    @pytest.fixture
    def mock_session_config(self):
        """Fixture for a mock OdaSessionConfig."""
        return OdaSessionConfig(
            common_llm_config=ModelConfig(model="test-model", api_key="test-key")
        )

    @pytest.mark.asyncio
    async def test_first_ctrl_c_sets_message(self):
        """Test that the first Ctrl+C sets the message and increments counter."""
        async with OneDragonAgentCLI().run_test() as pilot:
            app = pilot.app
            assert app.ctrl_c_count == 0
            assert app.sub_title == "CLI Interface"
            event = Key("ctrl+c", character="\x03")
            app.on_key(event)
            assert app.ctrl_c_count == 1
            assert app.sub_title == "Press Ctrl+C again to exit"

    @pytest.mark.asyncio
    async def test_second_ctrl_c_exits_app(self):
        """Test that the second Ctrl+C exits the app."""
        async with OneDragonAgentCLI().run_test() as pilot:
            app = pilot.app
            app.ctrl_c_count = 1
            app.sub_title = "Press Ctrl+C again to exit"
            event = Key("ctrl+c", character="\x03")
            app.on_key(event)
            assert app._exit

    @pytest.mark.asyncio
    async def test_other_key_resets_counter(self):
        """Test that pressing any other key resets the counter."""
        async with OneDragonAgentCLI().run_test() as pilot:
            app = pilot.app
            app.ctrl_c_count = 1
            app.sub_title = "Press Ctrl+C again to exit"
            event = Key("a", character="a")
            app.on_key(event)
            assert app.ctrl_c_count == 0
            assert app.sub_title == "CLI Interface"

    @pytest.mark.asyncio
    async def test_enter_key_submits_input(self, mock_session_config):
        """Test that pressing Enter submits the input and uses a mock agent system."""

        # Create a mock coroutine for the execute method
        async def mock_execute():
            pass  # Mock execute does nothing in this test

        # Mock the Agent class to avoid creating real LLM clients
        with patch.object(
            Agent,
            "__init__",
            lambda self, session_config, message_manager, command, llm_factory=None, tool_manager=None, event_dispatcher=None, reminder_manager=None, permission_manager=None: setattr(
                self, "execute", mock_execute
            )
            or None,
        ):
            # Run the app test
            async with OneDragonAgentCLI().run_test() as pilot:
                app = pilot.app
                await pilot.pause()  # Wait for on_mount to complete

                # Replace the real session with a mocked one
                mock_factory = MockModelClientFactory()
                app.agent_session = OdaSession(config=mock_session_config)
                # Inject the mock factory
                app.agent_session._llm_factory = mock_factory

                # Simulate user input
                input_widget = app.query_one("#command-input", Input)
                input_widget.value = "Test message"
                submitted_event = Input.Submitted(input_widget, "Test message")
                await app.on_input_submitted(submitted_event)
                await pilot.pause()  # Allow processing tasks to run

                # Verify interactions
                assert input_widget.value == ""
                assert app.sub_title == "Processing: Test message..."
