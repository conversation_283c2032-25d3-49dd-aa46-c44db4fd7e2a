"""Tests for CLI event handlers."""

import pytest
from unittest.mock import Mock, AsyncMock

from one_dragon_agent.cli.event_handler import C<PERSON>IEventHandler
from one_dragon_agent.core.agent.agent_message import (
    AgentTextStreamContentEvent,
)
from one_dragon_agent.core.agent.tool.tool_event import (
    ToolExecutionStartEvent,
    ToolExecutionCompleteEvent,
)
from one_dragon_agent.core.agent.tool.todo_message import (
    TodoListUpdatedEvent,
    TodoUpdateFailedEvent,
)
from one_dragon_agent.core.agent.tool.todo_manager import (
    TodoItem,
    TaskStatus,
    TaskPriority,
)
from one_dragon_agent.core.sys.message import OdaMessageSource


class TestCLIEventHandlers:
    """Test cases for CLI event handlers."""

    @pytest.fixture
    def mock_app(self):
        """Create a mock CLI app."""
        app = Mock()
        app.display_manager = Mock()
        app.display_manager.add_item = AsyncMock()
        app.display_manager.update_item = AsyncMock()
        app.display_manager.get_item = Mock()
        app.display_manager.remove_item = AsyncMock()
        app._is_scrolled_to_bottom = Mock(return_value=True)
        app.chat_container = Mock()
        app.chat_container.scroll_end = Mock()
        return app

    @pytest.mark.asyncio
    async def test_cli_agent_event_handler(self, mock_app):
        """Test CLIEventHandler for agent events."""
        handler = CLIEventHandler(mock_app)

        # Test AgentTextStreamContentEvent
        event = AgentTextStreamContentEvent(
            "test_session", "Hello, world!", "test_stream"
        )
        mock_app.display_manager.get_item.return_value = Mock(renderable="")

        await handler.handle(event)

        # Verify that display_manager.add_item was not called (we're testing content event, not start)
        mock_app.display_manager.add_item.assert_not_called()

        # Verify that display_manager.update_item was called
        mock_app.display_manager.update_item.assert_called_once()

    @pytest.mark.asyncio
    async def test_cli_tool_event_handler(self, mock_app):
        """Test CLIEventHandler for tool events."""
        handler = CLIEventHandler(mock_app)

        # Test ToolExecutionStartEvent
        event = ToolExecutionStartEvent(
            session_id="test_session",
            tool_name="read_file",
            tool_args={"file_path": "/test/file.txt"},
            tool_call_id="test_call_id",
        )
        await handler.handle(event)

        # Verify that display_manager.add_item was called
        mock_app.display_manager.add_item.assert_called_once()
        args, kwargs = mock_app.display_manager.add_item.call_args
        assert kwargs["item_id"] == "test_call_id"
        assert "正在执行工具: read_file" in kwargs["content"]
        assert "file_path" in kwargs["content"]

        # Reset mock
        mock_app.display_manager.add_item.reset_mock()

        # Test ToolExecutionCompleteEvent (success)
        event = ToolExecutionCompleteEvent(
            session_id="test_session",
            tool_call_id="test_call_id",
            tool_name="read_file",
            tool_args={},
            success=True,
            execution_time=0.1,
            result="File content here",
        )
        await handler.handle(event)

        # Verify that display_manager.update_item was called
        mock_app.display_manager.update_item.assert_called_once()
        args, kwargs = mock_app.display_manager.update_item.call_args
        assert args[0] == "test_call_id"
        assert "工具执行成功: read_file" in kwargs["content"]
        assert "File content here" in kwargs["content"]

        # Reset mock
        mock_app.display_manager.update_item.reset_mock()

        # Test ToolExecutionCompleteEvent (failure)
        event = ToolExecutionCompleteEvent(
            session_id="test_session",
            tool_call_id="test_call_id",
            tool_name="read_file",
            tool_args={},
            success=False,
            execution_time=0.1,
            error="File not found",
        )
        await handler.handle(event)

        # Verify that display_manager.update_item was called
        mock_app.display_manager.update_item.assert_called_once()
        args, kwargs = mock_app.display_manager.update_item.call_args
        assert args[0] == "test_call_id"
        assert "工具执行失败: read_file" in kwargs["content"]
        assert "File not found" in kwargs["content"]

    @pytest.mark.asyncio
    async def test_cli_todo_event_handler(self, mock_app):
        """Test CLIEventHandler for todo events."""
        handler = CLIEventHandler(mock_app)

        # Test TodoListUpdatedEvent
        todo1 = TodoItem("Task 1", TaskStatus.PENDING, TaskPriority.HIGH, "1")
        todo2 = TodoItem("Task 2", TaskStatus.IN_PROGRESS, TaskPriority.MEDIUM, "2")
        event = TodoListUpdatedEvent("test_session", [todo1, todo2])
        await handler.handle(event)

        # Verify that display_manager.add_item was called
        mock_app.display_manager.add_item.assert_called_once()
        args, kwargs = mock_app.display_manager.add_item.call_args
        assert "待办事项列表已更新:" in kwargs["content"]
        assert "Task 1" in kwargs["content"]
        assert "Task 2" in kwargs["content"]

        # Reset mock
        mock_app.display_manager.add_item.reset_mock()

        # Test TodoUpdateFailedEvent
        event = TodoUpdateFailedEvent("test_session", "Invalid todo item")
        await handler.handle(event)

        # Verify that display_manager.add_item was called
        mock_app.display_manager.add_item.assert_called_once()
        args, kwargs = mock_app.display_manager.add_item.call_args
        assert "待办事项更新失败:" in kwargs["content"]
        assert "Invalid todo item" in kwargs["content"]
