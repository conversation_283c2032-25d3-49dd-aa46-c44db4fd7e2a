"""Final integration test for the OneDragon-Agent CLI."""

import asyncio
from unittest.mock import AsyncMock, patch, MagicMock

import pytest
from textual.widgets import Input

from one_dragon_agent.cli.app import OneDragonAgent<PERSON><PERSON>
from one_dragon_agent.core.agent.agent import Agent
from one_dragon_agent.core.model.base import Model<PERSON>lient
from one_dragon_agent.core.model.config import ModelConfig
from one_dragon_agent.core.model.client_factory import ModelClientFactory
from one_dragon_agent.core.sys.config import OdaSessionConfig
from one_dragon_agent.core.sys.message import OdaMessage, OdaMessageSource
from one_dragon_agent.core.model.message import (
    OdaModelMessageRole,
    OdaMessageTextContent,
)


class MockModelClientFactory(ModelClientFactory):
    """A mock factory that returns a mock LLM client."""

    def __init__(self):
        super().__init__()
        self.mock_client = MagicMock(spec=ModelClient)

        # Mock the async generator method
        async def mock_stream(*args, **kwargs):
            yield MagicMock(
                choices=[
                    MagicMock(
                        delta=MagicMock(
                            role=OdaModelMessageRole.ASSISTANT,
                            content="Test response",
                        )
                    )
                ]
            )

        self.mock_client.stream = mock_stream

    def get_client(self, config: ModelConfig) -> ModelClient:
        """Return the mock client."""
        return self.mock_client


@pytest.mark.asyncio
@pytest.mark.timeout(10)
async def test_cli_integration():
    """Test the complete CLI integration flow."""

    # Create a mock coroutine for the execute method
    async def mock_execute():
        pass  # Mock execute does nothing in this test

    # Mock the Agent class to avoid creating real LLM clients
    with patch.object(
        Agent,
        "__init__",
        lambda self, session_config, message_manager, command, llm_factory=None, tool_manager=None, event_dispatcher=None, reminder_manager=None, permission_manager=None: setattr(
            self, "execute", mock_execute
        )
        or None,
    ):
        # Run the app test
        async with OneDragonAgentCLI().run_test() as pilot:
            app = pilot.app
            await pilot.pause()  # Wait for on_mount to complete

            # Verify the app initialized correctly
            assert app.title == "OneDragon-Agent"
            assert app.sub_title == "CLI Interface"

            # Verify the chat container exists
            assert app.chat_container is not None

            # Verify the command input exists and is focused
            input_widget = app.query_one("#command-input", Input)
            assert input_widget is not None
            # Note: We can't easily check if it's focused in a test environment

            # Test Ctrl+C handling
            assert app.ctrl_c_count == 0
            from textual.events import Key

            event = Key("ctrl+c", character="\x03")
            app.on_key(event)
            assert app.ctrl_c_count == 1
            assert app.sub_title == "Press Ctrl+C again to exit"

            # Test that a second Ctrl+C would exit (but we won't actually exit in the test)
            event = Key("ctrl+c", character="\x03")
            app.on_key(event)
            # We won't assert on exit behavior as it would terminate the test

            # Test resetting the counter with another key
            event = Key("a", character="a")
            app.on_key(event)
            assert app.ctrl_c_count == 0
            assert app.sub_title == "CLI Interface"
