"""Display data models for the OneDragon-Agent CLI."""

from dataclasses import dataclass
from typing import Optional
from enum import StrEnum


class DisplayItemType(StrEnum):
    """Enumeration of display item types."""

    USER_MESSAGE = "user_message"
    AI_MESSAGE = "ai_message"
    AI_STREAMING = "ai_streaming"
    TOOL_START = "tool_start"
    TOOL_RESULT = "tool_result"
    SYSTEM_MESSAGE = "system_message"
    TODO_LIST = "todo_list"
    PERMISSION_REQUEST = "permission_request"
    PROGRESS_INDICATOR = "progress_indicator"


@dataclass
class DisplayItem:
    """A display item for the CLI."""

    item_type: DisplayItemType
    content: str
    item_id: str  # 用于更新特定项的唯一标识符
    status: Optional[str] = None  # 如 "running", "completed", "failed"
    metadata: Optional[dict] = None  # 额外的元数据
