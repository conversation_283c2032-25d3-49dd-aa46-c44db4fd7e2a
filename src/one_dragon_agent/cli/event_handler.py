import asyncio
from typing import TYPE_CHECKING
import uuid

from textual.widgets import Static, Input

from ..core.agent.agent_message import (
    AgentTextStreamStartEvent,
    AgentTextStreamContentEvent,
    AgentTextStreamCompleteEvent,
)
from ..core.agent.tool.tool_event import (
    ToolExecutionStartEvent,
    ToolExecutionCompleteEvent,
    PermissionRequestEvent,
)
from ..core.agent.tool.todo_message import (
    TodoListUpdatedEvent,
    TodoUpdateFailedEvent,
)
from ..core.event.handler import EventHandler
from ..core.event.event import Event
from ..cli.display import DisplayItemType

if TYPE_CHECKING:
    from .app import OneDragonAgentCLI


class CLIEventHandler(EventHandler):
    """Consolidated event handler for all CLI events."""

    def __init__(self, app: "OneDragonAgentCLI") -> None:
        self.app = app

    @property
    def event_type(self) -> str:
        """Handle all events."""
        return "*"

    async def handle(self, event: Event) -> None:
        """Handle all events by type."""
        # Agent text stream events
        if isinstance(event, AgentTextStreamStartEvent):
            await self._handle_agent_text_stream_start(event)
        elif isinstance(event, AgentTextStreamContentEvent):
            await self._handle_agent_text_stream_content(event)
        elif isinstance(event, AgentTextStreamCompleteEvent):
            await self._handle_agent_text_stream_complete(event)

        # Tool execution events
        elif isinstance(event, ToolExecutionStartEvent):
            await self._handle_tool_execution_start(event)
        elif isinstance(event, ToolExecutionCompleteEvent):
            await self._handle_tool_execution_complete(event)

        # Todo events
        elif isinstance(event, TodoListUpdatedEvent):
            await self._handle_todo_list_updated(event)
        elif isinstance(event, TodoUpdateFailedEvent):
            await self._handle_todo_update_failed(event)

        # Permission events
        elif isinstance(event, PermissionRequestEvent):
            await self._handle_permission_request(event)

    async def _handle_agent_text_stream_start(
        self, event: AgentTextStreamStartEvent
    ) -> None:
        """Handle agent text stream start event."""
        stream_id = event.stream_id or f"ai_stream_{uuid.uuid4()}"
        await self.app.display_manager.add_item(
            item_id=stream_id,
            item_type=DisplayItemType.AI_STREAMING,
            content="[b]AI:[/b]\n",
            status="streaming",
        )
        self.app.current_ai_stream_id = stream_id

    async def _handle_agent_text_stream_content(
        self, event: AgentTextStreamContentEvent
    ) -> None:
        """Handle agent text stream content event."""
        content_stream_id = event.stream_id or self.app.current_ai_stream_id
        if content_stream_id:
            current_widget = self.app.display_manager.get_item(content_stream_id)
            if current_widget:
                new_content = f"{current_widget.renderable}{event.text_chunk}"
                await self.app.display_manager.update_item(
                    content_stream_id, content=new_content
                )

                if self.app._is_scrolled_to_bottom():
                    self.app.chat_container.scroll_end(animate=False)

    async def _handle_agent_text_stream_complete(
        self, event: AgentTextStreamCompleteEvent
    ) -> None:
        """Handle agent text stream complete event."""
        complete_stream_id = event.stream_id or self.app.current_ai_stream_id
        if complete_stream_id:
            await self.app.display_manager.update_item(
                complete_stream_id,
                content=f"[b]AI:[/b] {event.full_text}",
                status="completed",
            )
            self.app.current_ai_stream_id = None

    async def _handle_tool_execution_start(
        self, event: ToolExecutionStartEvent
    ) -> None:
        """Handle tool execution start event."""
        if event.tool_name == "TodoWrite":
            return

        message_text = (
            f"正在执行工具: {event.tool_name}"
            f"参数: {event.tool_args}"
        )
        await self.app.display_manager.add_item(
            item_id=event.tool_call_id,
            item_type=DisplayItemType.TOOL_START,
            content=message_text,
            status="running",
        )

    async def _handle_tool_execution_complete(
        self, event: ToolExecutionCompleteEvent
    ) -> None:
        """Handle tool execution complete event."""
        if event.tool_name == "TodoWrite":
            return

        if event.success:
            message_text = (
                f"工具执行成功: {event.tool_name}\n"
                f"结果: {event.result}"
            )
            status = "completed"
        else:
            message_text = (
                f"工具执行失败: {event.tool_name}\n"
                f"错误: {event.error}"
            )
            status = "failed"

        await self.app.display_manager.update_item(
            event.tool_call_id, content=message_text, status=status
        )

    async def _handle_todo_list_updated(self, event: TodoListUpdatedEvent) -> None:
        """Handle todo list updated event."""
        item_id = f"todo_{uuid.uuid4()}"
        todo_items = "\n".join(
            [f"- [{todo.status}] {todo.content}" for todo in event.todos]
        )
        message_text = f"待办事项列表已更新:\n{todo_items}"
        await self.app.display_manager.add_item(
            item_id=item_id,
            item_type=DisplayItemType.TODO_LIST,
            content=message_text,
        )

    async def _handle_todo_update_failed(self, event: TodoUpdateFailedEvent) -> None:
        """Handle todo update failed event."""
        item_id = f"todo_error_{uuid.uuid4()}"
        message_text = f"待办事项更新失败:\n{event.error}"
        await self.app.display_manager.add_item(
            item_id=item_id,
            item_type=DisplayItemType.SYSTEM_MESSAGE,
            content=message_text,
        )

    async def _handle_permission_request(self, event: PermissionRequestEvent) -> None:
        """Handle permission request event."""
        tool_name = event.tool_name
        permission = event.permission

        prompt_text = (
            f"[bold yellow]Permission Request[/bold yellow]\n\n"
            f"Tool [bold]{tool_name}[/bold] is requesting permission for: "
            f"[bold]{permission}[/bold]\n\n"
            f"Allow? ([bold green]y[/bold green]es, [bold red]n[/bold red]o, "
            f"[bold blue]a[/bold]lways for this session, "
            f"[bold]esc[/bold] to cancel)"
        )
        prompt_widget = Static(
            prompt_text, classes="system-message", id="permission-prompt"
        )
        temp_input = Input(placeholder="y/n/a", id="permission-input")

        try:
            main_input = self.app.query_one("#command-input", Input)
            main_input.disabled = True
        except Exception:
            main_input = None

        await self.app.chat_container.mount(prompt_widget)
        await self.app.chat_container.mount(temp_input)
        self.app.chat_container.scroll_end(animate=False)

        try:
            temp_input.focus()
        except Exception:
            pass

        future = asyncio.get_running_loop().create_future()

        def on_submit(message: Input.Submitted) -> None:
            if not future.done():
                future.set_result(message.value)

        def on_key(event) -> None:
            if event.key == "escape":
                if not future.done():
                    future.set_result("n")

        temp_input.submitted = on_submit  # type: ignore[attr-defined]
        temp_input._on_key = on_key  # type: ignore[attr-defined, assignment]

        try:
            response = await asyncio.wait_for(future, timeout=1.0)
        except asyncio.TimeoutError:
            response = "n"
        response = response.lower().strip()

        granted = False
        scope = "once"
        if response in ["y", "yes"]:
            granted = True
        elif response in ["a", "always"]:
            granted = True
            scope = "session"

        try:
            await prompt_widget.remove()
            await temp_input.remove()
            if main_input:
                main_input.disabled = False
                main_input.focus()
        except Exception:
            pass

        await self.app.agent_session.handle_permission_response(
            permission=permission, granted=granted, scope=scope
        )
