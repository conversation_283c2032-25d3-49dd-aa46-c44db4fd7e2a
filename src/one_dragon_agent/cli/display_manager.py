"""Display manager for the OneDragon-Agent CLI."""

from typing import Optional, Dict
from textual.widgets import Static
import uuid


class DisplayManager:
    """Manages the display of items in the CLI."""

    def __init__(self, app):
        self.app = app
        self._display_items: Dict[str, Static] = {}

    async def add_item(
        self, item_id: str, item_type: str, content: str, status: Optional[str] = None
    ) -> None:
        """Add a new display item."""
        # 创建并添加新的显示组件
        widget = self._create_widget(item_type, content, status)
        await self.app.chat_container.mount(widget)
        self._display_items[item_id] = widget

        # 滚动到底部
        if self.app._is_scrolled_to_bottom():
            self.app.chat_container.scroll_end(animate=False)

    async def update_item(
        self, item_id: str, content: Optional[str] = None, status: Optional[str] = None
    ) -> None:
        """Update an existing display item."""
        if item_id in self._display_items:
            widget = self._display_items[item_id]
            new_content = content if content is not None else widget.renderable
            # 更新组件内容
            widget.update(new_content)

            # 如果需要，可以根据状态更新样式
            if status:
                self._update_widget_style(widget, status)

    async def remove_item(self, item_id: str) -> None:
        """Remove a display item."""
        if item_id in self._display_items:
            widget = self._display_items[item_id]
            await widget.remove()
            del self._display_items[item_id]

    def get_item(self, item_id: str) -> Optional[Static]:
        """Get a display item by its ID."""
        return self._display_items.get(item_id)

    def _create_widget(
        self, item_type: str, content: str, status: Optional[str] = None
    ) -> Static:
        """Create a widget based on item type."""
        # 根据不同类型创建不同样式的组件
        classes = self._get_widget_classes(item_type, status)
        return Static(content, classes=classes)

    def _get_widget_classes(self, item_type: str, status: Optional[str] = None) -> str:
        """Get CSS classes for a widget based on its type and status."""
        class_map = {
            "user_message": "user-message",
            "ai_message": "ai-message",
            "ai_streaming": "ai-message",
            "tool_start": "system-message",
            "tool_result": "tool-result-card",
            "system_message": "system-message",
            "todo_list": "system-message",
            "permission_request": "system-message",
            "progress_indicator": "system-message",
        }
        return class_map.get(item_type, "system-message")

    def _update_widget_style(self, widget: Static, status: str) -> None:
        """Update widget style based on status."""
        # 根据状态更新样式，例如添加动画效果等
        pass
