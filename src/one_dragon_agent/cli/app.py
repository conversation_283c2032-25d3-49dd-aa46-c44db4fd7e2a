"""CLI application for OneDragon-Agent using Textual."""

import asyncio

from textual.app import App, ComposeResult
from textual.containers import Container, VerticalScroll
from textual.widgets import <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Input

from ..core.model.message import OdaMessageTextContent
from ..core.sys.config import create_session_config_from_env
from ..core.sys.message import (
    OdaMessage,
    OdaMessageSource,
)
from ..core.sys.session import OdaSession
from .event_handler import CLIEventHandler
from .display_manager import DisplayManager
from .display import DisplayItemType


class OneDragonAgentCLI(App):
    """A Textual app for the OneDragon-Agent CLI."""

    CSS = """
    Screen {
        layout: vertical;
        width: 100%;
        height: 100%;
    }

    #chat-container {
        height: 1fr;
        overflow-y: scroll;
        padding: 0 1;
        width: 100%;
    }

    #input-container {
        height: auto;
        padding: 1 2;
        width: 100%;
    }

    Input {
        width: 100%;
    }

    .welcome-message {
        padding: 1 2;
        margin: 1 0;
        content-align: center middle;
        text-align: center;
        color: #aaaaaa;
    }

    .user-message {
        background: #2a2a2a;
        padding: 1 2;
        margin: 1 0;
        border-left: solid #4a86e8;
    }

    .ai-message {
        background: #3a3a3a;
        padding: 1 2;
        margin: 1 0;
        border-left: solid #34a853;
    }

    .tool-result-card {
        background: #303040;
        border: round #707090;
        padding: 1 2;
        margin: 1 0;
    }
    
    .system-message {
        background: #402020;
        padding: 1 2;
        margin: 1 0;
        border-left: solid #e84a4a;
    }
    """

    def __init__(self):
        super().__init__()
        self.ctrl_c_count = 0
        self.agent_session = None
        self.chat_container = None
        self.display_manager = None  # 新增显示管理器
        self._event_handlers = []
        self.current_ai_stream_id: str | None = None  # 当前AI流ID

    def compose(self) -> ComposeResult:
        """Create child widgets for the app."""
        yield Header()
        self.chat_container = VerticalScroll(id="chat-container")
        yield self.chat_container
        yield Container(
            Input(placeholder="Enter your command...", id="command-input"),
            id="input-container",
        )
        yield Footer()

    async def on_mount(self) -> None:
        """Called when the app is mounted."""
        self.title = "OneDragon-Agent"
        self.sub_title = "CLI Interface"

        # 初始化显示管理器
        self.display_manager = DisplayManager(self)

        welcome_widget = Static(
            "Welcome to One-Dragon-Agent!", classes="welcome-message"
        )
        await self.chat_container.mount(welcome_widget)

        self.query_one("#command-input").focus()

        session_config = create_session_config_from_env()
        self.agent_session = OdaSession(config=session_config)

        # Register single consolidated event handler
        event_handler = CLIEventHandler(self)
        self._event_handlers = [event_handler]

        if self.agent_session._event_dispatcher:
            self.agent_session._event_dispatcher.subscribe(
                event_handler.event_type, event_handler
            )

    def _is_scrolled_to_bottom(self) -> bool:
        """检查是否滚动到最底部"""
        if not self.chat_container:
            return True

        # 获取滚动容器的滚动位置和最大滚动位置
        scroll_offset = self.chat_container.scroll_y
        max_scroll = self.chat_container.max_scroll_y

        # 如果滚动位置在底部附近（允许5像素的误差），认为在底部
        return max_scroll - scroll_offset <= 5

    async def on_input_submitted(self, message: Input.Submitted) -> None:
        """Handle input submission."""
        if not message.value.strip():
            return

        # 立即显示用户输入，提供即时反馈
        user_message_widget = Static(
            f"[b]You:[/b]\n{message.value}", classes="user-message"
        )
        await self.chat_container.mount(user_message_widget)
        self.chat_container.scroll_end(animate=False)

        # 立即清空输入框并更新状态
        message.input.value = ""
        self.sub_title = f"Processing: {message.value[:20]}..."

        # 创建消息对象
        oda_message = OdaMessage(
            source=OdaMessageSource.USER,
            content=OdaMessageTextContent(message.value),
        )

        # 异步发送消息，不阻塞UI
        asyncio.create_task(self._send_message_async(oda_message))

    async def _send_message_async(self, oda_message: OdaMessage) -> None:
        """异步发送消息，避免阻塞UI"""
        try:
            await self.agent_session.send_input(oda_message)
        except Exception as e:
            # 处理发送错误
            await self.display_manager.add_item(
                item_id=f"error_{hash(str(e))}",
                item_type=DisplayItemType.SYSTEM_MESSAGE,
                content=f"[b]System:[/b] Error sending message: {str(e)}",
            )

    def on_key(self, event) -> None:
        """Handle key presses."""
        if event.key == "ctrl+c":
            self.ctrl_c_count += 1
            if self.ctrl_c_count >= 2:
                self.exit()
            else:
                self.bell()
                self.sub_title = "Press Ctrl+C again to exit"
        else:
            self.ctrl_c_count = 0
            self.sub_title = "CLI Interface"

    async def on_unmount(self) -> None:
        """应用卸载时清理资源"""
        # 取消事件订阅
        if self.agent_session and self.agent_session._event_dispatcher:
            for handler in self._event_handlers:
                # Note: In a more robust implementation, we would store
                # subscription IDs and use them to unsubscribe.
                # For now, we rely on session cleanup.
                pass

        # 关闭会话
        if self.agent_session:
            await self.agent_session.close()


def main():
    """Run the OneDragon-Agent CLI."""
    app = OneDragonAgentCLI()
    app.run()
