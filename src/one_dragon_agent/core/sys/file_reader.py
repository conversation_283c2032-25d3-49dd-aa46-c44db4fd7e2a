"""Provides core file reading functionality for the OneDragon-Agent system."""

import os
from pathlib import Path
from typing import Optional, <PERSON><PERSON>


def read_text_file(
    file_path: str, encoding: str = "utf-8", offset: int = 0, limit: int = 2000
) -> Tuple[Optional[str], int]:
    """
    Reads a segment of a text file's content.

    Args:
        file_path: The absolute path to the file to read.
        encoding: The encoding to use when reading the file. Defaults to 'utf-8'.
        offset: The line number to start reading from (0-based). Defaults to 0.
        limit: The maximum number of lines to read. Defaults to 2000.

    Returns:
        A tuple containing:
        - The content of the file segment as a string, or None if an error occurred.
        - The total number of lines in the file.
    """
    try:
        path = Path(file_path)
        with open(path, "r", encoding=encoding) as f:
            lines = f.readlines()

        total_lines = len(lines)

        # Validate offset
        if offset < 0:
            offset = 0
        elif offset >= total_lines:
            return None, total_lines

        # Calculate end line
        end_line = min(offset + limit, total_lines)

        # Extract specified range of lines
        selected_lines = lines[offset:end_line]

        # Build content with line numbers
        content_lines = []
        for i, line in enumerate(selected_lines, start=offset + 1):
            # Truncate lines longer than 2000 characters
            if len(line) > 2000:
                line = line[:2000] + "...[截断]"
            content_lines.append(f"{i:6d}\t{line.rstrip()}")

        content = "\n".join(content_lines)
        return content, total_lines

    except UnicodeDecodeError:
        # If UTF-8 decoding fails, try other common encodings like GBK
        try:
            with open(path, "r", encoding="gbk") as f:
                lines = f.readlines()

            total_lines = len(lines)

            # Validate offset
            if offset < 0:
                offset = 0
            elif offset >= total_lines:
                return None, total_lines

            # Calculate end line
            end_line = min(offset + limit, total_lines)

            # Extract specified range of lines
            selected_lines = lines[offset:end_line]

            # Build content with line numbers
            content_lines = []
            for i, line in enumerate(selected_lines, start=offset + 1):
                # Truncate lines longer than 2000 characters
                if len(line) > 2000:
                    line = line[:2000] + "...[截断]"
                content_lines.append(f"{i:6d}\t{line.rstrip()}")

            content = "\n".join(content_lines)
            return content, total_lines

        except Exception as e:
            print(f"Error reading text file {file_path} with alternative encoding: {e}")
            return None, 0
    except Exception as e:
        print(f"Error reading text file {file_path}: {e}")
        return None, 0


def read_binary_file(file_path: str) -> Optional[bytes]:
    """
    Reads the entire content of a binary file.

    Args:
        file_path: The absolute path to the file to read.

    Returns:
        The content of the file as bytes, or None if an error occurred.
    """
    try:
        path = Path(file_path)
        with open(path, "rb") as f:
            return f.read()
    except Exception as e:
        print(f"Error reading binary file {file_path}: {e}")
        return None


def get_file_info(file_path: str) -> Optional[dict]:
    """
    Gets basic file information like size.

    Args:
        file_path: The absolute path to the file.

    Returns:
        A dictionary containing file information, or None if an error occurred.
    """
    try:
        path = Path(file_path)
        stat = path.stat()
        return {
            "size": stat.st_size,
            # Add more info if needed in the future
        }
    except Exception as e:
        print(f"Error getting file info for {file_path}: {e}")
        return None
