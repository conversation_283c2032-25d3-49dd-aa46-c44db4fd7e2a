import asyncio
import threading
import time
from collections import OrderedDict
from dataclasses import dataclass, field
from typing import Optional, Dict, List, Tuple, TYPE_CHECKING
from pathlib import Path
from queue import Queue, Empty

from pathspec import PathSpec
from pathspec.gitignore import GitIgnoreSpec
from pathspec.patterns import GitWildMatchPattern

from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler, FileSystemEvent

from ..algo.trie import Trie


class IndexFileSystemEventHandler(FileSystemEventHandler):
    """
    文件系统事件处理器，用于监听文件系统变化并更新索引
    """

    def __init__(self, workspace_index: "WorkspaceIndex") -> None:
        """
        初始化文件系统事件处理器

        Args:
            workspace_index: 关联的WorkspaceIndex实例
        """
        self.workspace_index = workspace_index
        super().__init__()

    def on_created(self, event: FileSystemEvent) -> None:
        """
        处理文件/目录创建事件

        Args:
            event: 文件系统事件
        """
        # 确保路径是字符串类型
        src_path_str = str(event.src_path)

        if not event.is_directory:
            # 检查是否为.gitignore文件
            if Path(src_path_str).name == ".gitignore":
                self.workspace_index._enqueue_event('gitignore_changed', None)

            # 文件创建事件 - 放入事件队列
            self.workspace_index._enqueue_event('file_created', src_path_str)
        else:
            # 目录创建事件 - 放入事件队列
            self.workspace_index._enqueue_event('dir_created', src_path_str)

    def on_deleted(self, event: FileSystemEvent) -> None:
        """
        处理文件/目录删除事件

        Args:
            event: 文件系统事件
        """
        # 确保路径是字符串类型
        src_path_str = str(event.src_path)

        if not event.is_directory:
            # 检查是否为.gitignore文件
            if Path(src_path_str).name == ".gitignore":
                self.workspace_index._enqueue_event('gitignore_changed', None)

            # 文件删除事件 - 放入事件队列
            self.workspace_index._enqueue_event('file_deleted', src_path_str)
        else:
            # 目录删除事件 - 放入事件队列
            self.workspace_index._enqueue_event('dir_deleted', src_path_str)

    def on_modified(self, event: FileSystemEvent) -> None:
        """
        处理文件/目录修改事件

        Args:
            event: 文件系统事件
        """
        # 确保路径是字符串类型
        src_path_str = str(event.src_path)

        if not event.is_directory:
            # 检查是否为.gitignore文件
            if Path(src_path_str).name == ".gitignore":
                self.workspace_index._enqueue_event('gitignore_changed', None)

            # 文件修改事件 - 放入事件队列
            self.workspace_index._enqueue_event('file_modified', src_path_str)
        # 目录修改事件通常不需要特殊处理

    def on_moved(self, event: FileSystemEvent) -> None:
        """
        处理文件/目录移动/重命名事件

        Args:
            event: 文件系统事件
        """
        # 确保路径是字符串类型
        src_path_str = str(event.src_path)
        dest_path_str = str(event.dest_path)

        # 检查源路径或目标路径是否为.gitignore文件
        src_is_gitignore = Path(src_path_str).name == ".gitignore"
        dest_is_gitignore = (
            not event.is_directory and Path(dest_path_str).name == ".gitignore"
        )

        if src_is_gitignore or dest_is_gitignore:
            self.workspace_index._enqueue_event('gitignore_changed', None)

        if not event.is_directory:
            # 文件移动/重命名事件 - 放入事件队列
            self.workspace_index._enqueue_event('file_moved', (src_path_str, dest_path_str))
        else:
            # 目录移动/重命名事件 - 放入事件队列
            self.workspace_index._enqueue_event('dir_moved', (src_path_str, dest_path_str))


@dataclass
class IndexNode:
    """
    表示文件系统中一个实体（文件或目录）的核心数据对象
    """

    name: str  # 文件或目录的名称
    path: str  # 从项目根目录出发的、范式化的相对路径
    is_dir: bool  # 标记该节点是否为一个目录
    mtime: float = 0.0  # 文件最后修改时间的Unix时间戳，目录节点为0
    parent: Optional["IndexNode"] = None  # 指向其父节点的直接引用
    children: Dict[str, "IndexNode"] = field(default_factory=dict)  # 子节点映射
    is_core: bool = False  # 标记该节点是否属于"静态核心区"

    def __post_init__(self) -> None:
        """
        初始化后处理
        """
        if self.children is None:
            self.children = {}


class IndexData:
    """
    存放所有索引数据的内部容器
    """

    def __init__(self) -> None:
        """
        初始化IndexData
        """
        # 主索引和数据所有者: 存储项目中所有已知的IndexNode对象实例
        self.path_to_node: Dict[str, IndexNode] = {}

        # 路径前缀搜索加速器
        self.path_trie: Trie = Trie()

        # 名称前缀搜索加速器
        self.name_trie: Trie = Trie()

        # 动态发现区节点的LRU管理器
        self.dynamic_nodes_lru: OrderedDict[str, float] = OrderedDict()


class WorkspaceIndex:
    """
    实时文件索引服务

    为 OneDragon-Agent 提供一个高性能、实时同步的文件和目录索引服务。
    """

    # LRU默认大小限制
    DYNAMIC_NODES_LRU_LIMIT: int = 10000

    def __init__(
        self,
        root_path: str,
        core_patterns: Optional[List[str]] = None,
        ignore_patterns: Optional[List[str]] = None,
        use_gitignore: bool = True,
    ) -> None:
        """
        初始化WorkspaceIndex

        Args:
            root_path: 项目根目录的绝对路径
            core_patterns: 用户定义的核心文件/目录模式列表
            ignore_patterns: 用户定义的忽略文件/目录模式列表
            use_gitignore: 是否使用.gitignore文件定义忽略规则
        """
        # 验证根路径
        root_path_obj = Path(root_path).resolve()
        if not root_path_obj.exists():
            raise ValueError(f"根路径不存在: {root_path}")
        if not root_path_obj.is_dir():
            raise ValueError(f"根路径不是一个目录: {root_path}")

        self.root_path: str = str(root_path_obj)
        self.core_patterns: List[str] = core_patterns or []
        self.ignore_patterns: List[str] = ignore_patterns or []
        self.use_gitignore: bool = use_gitignore

        # 初始化索引数据容器
        self.index_data: IndexData = IndexData()

        # 初始化状态管理
        self._initialized: bool = False
        self._initializing: bool = False
        self._init_lock: asyncio.Lock = asyncio.Lock()

        # PathSpec对象（将在初始化时构造）
        self._core_pathspec: Optional[PathSpec] = None
        self._ignore_pathspec_static: Optional[GitIgnoreSpec] = None
        self._ignore_pathspec_git: Optional[GitIgnoreSpec] = None

        # 文件系统监听相关
        self._observer: Optional[Observer] = None
        self._event_handler: Optional[IndexFileSystemEventHandler] = None
        self._watching: bool = False

        # 全局扫描锁，防止并发昂贵的磁盘扫描
        self._global_scan_lock: asyncio.Lock = asyncio.Lock()
        self._sync_scan_lock: asyncio.Lock = asyncio.Lock()
        
        # 线程安全的事件队列
        self._event_queue: Queue = Queue()
        self._event_processor_task: Optional[asyncio.Task] = None
        self._event_processing_lock = threading.Lock()

    async def initialize(self) -> None:
        """
        异步初始化索引服务
        """
        async with self._init_lock:
            if self._initialized or self._initializing:
                return

            self._initializing = True
            try:
                # 1. PathSpec构造
                self._construct_pathspecs()

                # 2. 核心区索引构造
                await self._build_core_index()

                # 3. 构造.gitignore PathSpec
                if self.use_gitignore:
                    await self._construct_gitignore_pathspec()

                # 4. 启动文件系统监听
                await self._start_file_watching()
                
                # 5. 启动事件处理器
                self._start_event_processor()

                # 6. 标记初始化完成
                self._initialized = True
            except Exception as e:
                # 记录错误并清理状态
                self._initialized = False
                raise e
            finally:
                self._initializing = False

    def _construct_pathspecs(self) -> None:
        """
        构造PathSpec对象
        """
        # 构造core_pathspec
        core_patterns = self.core_patterns.copy()
        if self.use_gitignore:
            # 自动包含.gitignore模式
            core_patterns.append("**/.gitignore")
        self._core_pathspec = PathSpec.from_lines(GitWildMatchPattern, core_patterns)

        # 构造ignore_pathspec_static
        self._ignore_pathspec_static = GitIgnoreSpec.from_lines(self.ignore_patterns)

        # 构造ignore_pathspec_git
        if self.use_gitignore:
            self._construct_gitignore_pathspec()

    def _read_gitignore_file(self, gitignore_path: Path) -> List[str]:
        """
        读取并解析.gitignore文件，返回规则列表

        Args:
            gitignore_path: .gitignore文件的路径

        Returns:
            规则列表，每行一个规则
        """
        try:
            # 直接使用utf-8编码并忽略错误，简化处理
            with open(gitignore_path, "r", encoding="utf-8", errors="ignore") as f:
                lines = f.readlines()

            # 处理每一行，去除注释和空行
            rules = []
            for line in lines:
                line = line.rstrip("\n")
                if line and not line.startswith("#"):
                    rules.append(line)

            return rules
        except Exception:
            # 读取失败时返回空列表
            return []

    def _process_gitignore_rules(
        self, rules: List[str], relative_dir: Path
    ) -> List[str]:
        """
        处理.gitignore规则，为子目录的规则添加正确的前缀

        Args:
            rules: 原始规则列表
            relative_dir: .gitignore文件相对于项目根目录的路径

        Returns:
            处理后的规则列表
        """
        processed_rules = []

        # 为非根目录的.gitignore文件添加路径前缀
        if relative_dir != Path("."):
            dir_prefix = relative_dir.as_posix()
            for rule in rules:
                if rule.startswith("/"):
                    # 绝对路径：/pattern -> /dir/pattern
                    processed_rules.append(f"/{dir_prefix}{rule}")
                elif rule.startswith("!/"):
                    # 否定绝对路径：!/pattern -> !/dir/pattern
                    processed_rules.append(f"!/{dir_prefix}{rule[1:]}")
                elif rule.startswith("!"):
                    # 否定相对路径：!pattern -> !dir/pattern
                    processed_rules.append(f"!{dir_prefix}/{rule[1:]}")
                else:
                    # 相对路径：pattern -> dir/pattern
                    processed_rules.append(f"{dir_prefix}/{rule}")
        else:
            # 根目录的.gitignore文件，直接使用原规则
            processed_rules.extend(rules)

        return processed_rules

    async def _construct_gitignore_pathspec(self) -> None:
        """
        异步构造.gitignore规则的PathSpec对象，正确处理子目录规则的作用域
        """
        loop = asyncio.get_running_loop()
        
        def _scan_gitignore_files():
            """同步扫描.gitignore文件"""
            gitignore_files = []
            # 递归扫描项目根目录，定位所有.gitignore文件
            for gitignore_path in Path(self.root_path).rglob(".gitignore"):
                try:
                    gitignore_files.append(gitignore_path)
                except Exception:
                    # 忽略无法处理的.gitignore文件
                    continue
            return gitignore_files

        def _process_gitignore_files(gitignore_files):
            """处理所有.gitignore文件"""
            all_lines = []
            for gitignore_path in gitignore_files:
                # 获取相对于项目根目录的目录
                try:
                    relative_dir = gitignore_path.parent.relative_to(self.root_path)
                except Exception:
                    # 如果无法计算相对路径，跳过该文件
                    continue

                # 读取.gitignore文件
                rules = self._read_gitignore_file(gitignore_path)

                # 处理规则，添加正确的前缀
                processed_rules = self._process_gitignore_rules(rules, relative_dir)

                # 添加到总规则列表
                all_lines.extend(processed_rules)
            return all_lines

        # 异步扫描.gitignore文件
        gitignore_files = await loop.run_in_executor(None, _scan_gitignore_files)
        
        # 异步处理所有.gitignore文件
        if gitignore_files:
            all_lines = await loop.run_in_executor(None, _process_gitignore_files, gitignore_files)
            self._ignore_pathspec_git = GitIgnoreSpec.from_lines(all_lines)
        else:
            # 如果没有.gitignore文件，创建一个空的PathSpec
            self._ignore_pathspec_git = GitIgnoreSpec.from_lines([])

    async def _build_core_index(self) -> None:
        """
        异步构建核心区索引
        """
        # 首先创建根节点
        root_node = self._create_index_node(
            name="",
            path="",
            is_dir=True,
            mtime=0.0,
            is_core=True,  # 根节点始终是核心节点
        )
        self._add_node_to_index(root_node)

        # 异步扫描项目根目录，识别所有核心区文件和目录
        loop = asyncio.get_running_loop()
        
        def _scan_files():
            """同步扫描文件"""
            results = []
            for file_path in Path(self.root_path).rglob("*"):
                try:
                    # 计算相对于项目根目录的路径
                    relative_path = file_path.relative_to(self.root_path).as_posix()

                    # 检查是否应该索引以及是否为核心区文件
                    should_index, is_core = self._match_pathspec(
                        relative_path, file_path.is_dir()
                    )

                    if should_index:
                        # 创建IndexNode对象
                        node = self._create_index_node(
                            file_path.name,
                            relative_path,
                            file_path.is_dir(),
                            file_path.stat().st_mtime if file_path.is_file() else 0.0,
                            is_core,
                        )
                        results.append(node)
                except Exception:
                    # 忽略无法处理的文件
                    continue
            return results

        # 在executor中执行同步扫描
        nodes = await loop.run_in_executor(None, _scan_files)
        
        # 批量添加到索引结构中
        for node in nodes:
            self._add_node_to_index(node)

        # 启动文件系统监听将在initialize方法中异步完成

    def _match_pathspec(self, relative_path: str, is_dir: bool) -> Tuple[bool, bool]:
        """
        使用PathSpec匹配路径

        Args:
            relative_path: 相对于项目根目录的路径
            is_dir: 是否为目录

        Returns:
            (should_index, is_core) 二元组
        """
        # 确保PathSpec对象已初始化
        if self._core_pathspec is None or self._ignore_pathspec_static is None:
            raise RuntimeError("PathSpec对象尚未初始化")

        # 1. 核心区匹配
        if self._core_pathspec.match_file(relative_path):
            return True, True

        # 2. 静态忽略匹配
        if self._ignore_pathspec_static.match_file(relative_path):
            return False, False

        # 3. Git忽略匹配
        if self._ignore_pathspec_git and self._ignore_pathspec_git.match_file(
            relative_path
        ):
            return False, False

        # 默认情况：应该索引，但不是核心区
        return True, False

    async def _start_file_watching(self) -> None:
        """
        启动文件系统监听
        """
        # 在初始化过程中启动监听（此时_initialized可能还是False）
        # 创建事件处理器
        self._event_handler = IndexFileSystemEventHandler(self)

        # 创建观察者
        self._observer = Observer()

        # 安排观察路径
        self._observer.schedule(self._event_handler, self.root_path, recursive=True)

        # 启动观察者（会自动创建内部线程）
        self._observer.start()

        self._watching = True

    def _stop_file_watching(self) -> None:
        """
        停止文件系统监听
        """
        if self._observer:
            try:
                self._observer.stop()
                self._observer.join(timeout=1.0)  # 等待观察者停止
            except Exception:
                # 忽略停止过程中的错误
                pass
            finally:
                self._observer = None

        self._event_handler = None
        self._watching = False
        
        # 停止事件处理器
        if self._event_processor_task:
            self._event_processor_task.cancel()
            self._event_processor_task = None

    def _start_event_processor(self) -> None:
        """
        启动事件处理器
        """
        self._event_processor_task = asyncio.create_task(self._process_events())

    async def _process_events(self) -> None:
        """
        异步处理文件系统事件
        """
        while True:
            try:
                # 非阻塞获取事件
                try:
                    event_type, file_path = self._event_queue.get_nowait()
                except Empty:
                    await asyncio.sleep(0.01)
                    continue
                
                # 处理事件
                if event_type == 'file_created':
                    await self._async_handle_file_created(file_path)
                elif event_type == 'dir_created':
                    await self._async_handle_dir_created(file_path)
                elif event_type == 'file_deleted':
                    await self._async_handle_file_deleted(file_path)
                elif event_type == 'dir_deleted':
                    await self._async_handle_dir_deleted(file_path)
                elif event_type == 'file_modified':
                    await self._async_handle_file_modified(file_path)
                elif event_type == 'file_moved':
                    await self._async_handle_file_moved(file_path[0], file_path[1])
                elif event_type == 'dir_moved':
                    await self._async_handle_dir_moved(file_path[0], file_path[1])
                elif event_type == 'gitignore_changed':
                    await self._handle_gitignore_changed()
                    
            except asyncio.CancelledError:
                break
            except Exception:
                # 忽略事件处理中的错误
                pass

    def _create_index_node(
        self, name: str, path: str, is_dir: bool, mtime: float, is_core: bool
    ) -> IndexNode:
        """
        创建IndexNode对象并建立父子关系

        Args:
            name: 文件或目录名称
            path: 相对于项目根目录的路径
            is_dir: 是否为目录
            mtime: 修改时间
            is_core: 是否为核心区文件

        Returns:
            创建的IndexNode对象
        """
        # 检查节点是否已存在
        if path in self.index_data.path_to_node:
            # 如果节点已存在，更新其核心属性（如果需要）
            existing_node = self.index_data.path_to_node[path]
            # 如果新节点是核心节点，更新现有节点的核心属性
            if is_core and not existing_node.is_core:
                existing_node.is_core = True
            return existing_node

        # 创建节点
        node = IndexNode(
            name=name, path=path, is_dir=is_dir, mtime=mtime, is_core=is_core
        )

        # 建立父子关系
        if path != "":  # 不是根节点
            # 计算父路径
            if "/" in path:
                parent_path = "/".join(path.split("/")[:-1])
            else:
                parent_path = ""

            # 获取或创建父节点
            if parent_path in self.index_data.path_to_node:
                parent_node = self.index_data.path_to_node[parent_path]
                # 如果子节点是核心节点，父节点也应该是核心节点
                if is_core and not parent_node.is_core:
                    parent_node.is_core = True
            else:
                # 创建父节点（递归）
                parent_parts = parent_path.split("/") if parent_path else []
                parent_name = parent_parts[-1] if parent_parts else ""
                # 父节点的核心属性需要根据子节点来确定
                parent_node = self._create_index_node(
                    parent_name,
                    parent_path,
                    True,  # 父节点是目录
                    0.0,  # 目录的mtime为0
                    is_core,  # 父节点的核心区属性与子节点相同
                )

            # 建立父子关系
            node.parent = parent_node
            parent_node.children[name] = node

        return node

    def _enqueue_event(self, event_type: str, file_path: Optional[str]) -> None:
        """
        将文件系统事件加入处理队列
        
        Args:
            event_type: 事件类型
            file_path: 文件路径或元组（对于移动事件）
        """
        try:
            self._event_queue.put((event_type, file_path), block=False)
        except:
            # 队列满时忽略事件
            pass

    def _add_node_to_index(self, node: IndexNode) -> None:
        """
        将节点添加到所有索引结构中

        Args:
            node: 要添加的IndexNode对象
        """
        # 添加到path_to_node字典
        self.index_data.path_to_node[node.path] = node

        # 添加路径到path_trie
        self.index_data.path_trie.insert(node.path, node.path)

        # 添加名称到name_trie
        # 在name_trie中，数据是同名文件路径的列表
        existing_paths = self.index_data.name_trie.search(node.name)
        if existing_paths is None:
            # 如果名称不存在，创建新的路径列表
            self.index_data.name_trie.insert(node.name, [node.path])
        else:
            # 如果名称已存在，添加路径到列表
            existing_paths.append(node.path)

        # 核心区节点不添加到dynamic_nodes_lru
        if not node.is_core:
            self.index_data.dynamic_nodes_lru[node.path] = time.time()

            # 检查并执行LRU淘汰
            self._check_and_evict_lru()

    async def search(self, query: str, contextual_base_path: str) -> List[IndexNode]:
        """
        搜索文件或目录

        Args:
            query: 用户输入的原始查询字符串（不包含@符号）
            contextual_base_path: 用户发起搜索时所在的上下文目录（相对路径）

        Returns:
            匹配的IndexNode列表
        """
        # 1. 输入规范化处理（两阶段安全性验证）
        normalized_query, normalized_context_path, is_listing_request = (
            self._normalize_input(query, contextual_base_path)
        )

        # 2. 空查询处理
        if not normalized_query:
            return []

        # 3. 处理初始化状态
        if not self._initialized:
            if self._initializing:
                # 初始化中状态：先在内存中搜索，如果未命中则等待初始化完成
                results = self._search_in_memory(
                    normalized_query, normalized_context_path, is_listing_request
                )
                if results:
                    # 更新LRU状态并返回
                    self._update_lru_for_nodes(results)
                    return results

                # 等待初始化完成（最多30秒）
                try:
                    await asyncio.wait_for(
                        self._wait_for_initialization(), timeout=30.0
                    )
                except asyncio.TimeoutError:
                    # 超时，进入降级模式
                    pass

                # 初始化完成后，重新搜索
                results = self._search_in_memory(
                    normalized_query, normalized_context_path, is_listing_request
                )
                if results:
                    self._update_lru_for_nodes(results)
                    return results
            else:
                # 初始化失败状态，进入降级模式
                results = self._search_in_memory(
                    normalized_query, normalized_context_path, is_listing_request
                )
                if results:
                    self._update_lru_for_nodes(results)
                    return results

        # 4. 在内存索引中搜索
        results = self._search_in_memory(
            normalized_query, normalized_context_path, is_listing_request
        )

        # 5. 如果找到结果，更新LRU状态并返回
        if results:
            self._update_lru_for_nodes(results)
            return results

        # 6. 如果未找到结果，触发回退扫描
        results = self._fallback_scan(
            normalized_query, normalized_context_path, is_listing_request
        )

        # 7. 再次更新LRU状态并返回
        if results:
            self._update_lru_for_nodes(results)

        return results

    def _search_directory_listing(self, query: str) -> List[IndexNode]:
        """
        处理目录内容列出请求

        Args:
            query: 规范化后的查询字符串（目录路径）

        Returns:
            目录下的子节点列表
        """
        node = self.index_data.path_to_node.get(query)
        if node and node.is_dir:
            return list(node.children.values())
        return []

    def _search_by_name_or_path(
        self, query: str, contextual_base_path: str
    ) -> List[IndexNode]:
        """
        根据查询类型执行路径前缀搜索或名称前缀搜索

        Args:
            query: 规范化后的查询字符串
            contextual_base_path: 规范化后的上下文路径

        Returns:
            匹配的IndexNode列表
        """
        # 路径前缀搜索
        if "/" in query:
            return self._search_path_prefix(query)

        # 名称前缀搜索（上下文感知的两阶段搜索）
        return self._search_name_prefix(query, contextual_base_path)

    def _search_path_prefix(self, query: str) -> List[IndexNode]:
        """
        执行路径前缀搜索

        Args:
            query: 规范化后的查询字符串（包含路径分隔符）

        Returns:
            匹配的IndexNode列表
        """
        # 首先检查是否查询精确匹配一个目录
        exact_match_node = self.index_data.path_to_node.get(query)
        if exact_match_node and exact_match_node.is_dir:
            # 查询完全匹配一个目录，返回其子节点
            nodes = list(exact_match_node.children.values())
            nodes.sort(key=lambda x: x.path)
            return nodes

        # 使用path_trie进行前缀搜索
        path_results = self.index_data.path_trie.starts_with(query)
        if path_results:
            # 从path_to_node获取对应的IndexNode对象
            nodes = []
            for path in path_results:
                if path in self.index_data.path_to_node:
                    node = self.index_data.path_to_node[path]
                    # 路径前缀搜索应该返回文件节点，而不是目录节点
                    # 但如果查询完全匹配一个目录，则返回该目录下的所有文件
                    if node.is_dir and node.path == query:
                        # 查询完全匹配一个目录，返回其子节点
                        nodes.extend(node.children.values())
                    elif not node.is_dir:
                        # 这是一个文件节点，且路径匹配前缀
                        nodes.append(node)
            # 按路径排序
            nodes.sort(key=lambda x: x.path)
            if nodes:
                return nodes

        # 如果前缀搜索没有找到结果，尝试查找以查询开头的所有路径
        # 这是为了处理Trie可能没有正确索引的情况
        nodes = []
        for path, node in self.index_data.path_to_node.items():
            if path.startswith(query) and path != query:
                # 只返回文件节点，不返回目录节点
                if not node.is_dir:
                    nodes.append(node)
        nodes.sort(key=lambda x: x.path)
        return nodes

    def _search_name_prefix(
        self, query: str, contextual_base_path: str
    ) -> List[IndexNode]:
        """
        执行名称前缀搜索（上下文感知的两阶段搜索）

        Args:
            query: 规范化后的查询字符串（不包含路径分隔符）
            contextual_base_path: 规范化后的上下文路径

        Returns:
            匹配的IndexNode列表
        """
        # 阶段一：上下文搜索
        context_node = self.index_data.path_to_node.get(contextual_base_path)
        if context_node and context_node.is_dir:
            matching_nodes = []
            query_lower = query.lower()
            for child in context_node.children.values():
                if child.name.lower().startswith(query_lower):
                    # 如果匹配的节点是目录且名称完全匹配，返回其子节点
                    if child.is_dir and child.name.lower() == query_lower:
                        matching_nodes.extend(list(child.children.values()))
                    else:
                        matching_nodes.append(child)
            if matching_nodes:
                # 按名称排序
                matching_nodes.sort(key=lambda x: x.name)
                return matching_nodes

        # 阶段二：全局搜索
        # 首先检查是否查询精确匹配一个目录名
        exact_match_node = self.index_data.path_to_node.get(query)
        if exact_match_node and exact_match_node.is_dir:
            # 查询完全匹配一个目录，返回其子节点
            nodes = list(exact_match_node.children.values())
            nodes.sort(key=lambda x: x.path)
            return nodes

        # 使用name_trie进行前缀搜索
        name_results = self.index_data.name_trie.starts_with(query)
        if name_results:
            # name_trie中存储的是路径列表，需要合并去重
            path_set = set()
            for path_list in name_results:
                if isinstance(path_list, list):
                    path_set.update(path_list)

            # 从path_to_node获取对应的IndexNode对象
            nodes = []
            for path in path_set:
                if path in self.index_data.path_to_node:
                    nodes.append(self.index_data.path_to_node[path])
            # 按路径排序
            nodes.sort(key=lambda x: x.path)
            return nodes

        return []

    def _search_in_memory(
        self, query: str, contextual_base_path: str, is_listing_request: bool
    ) -> List[IndexNode]:
        """
        在内存索引中搜索

        Args:
            query: 规范化后的查询字符串
            contextual_base_path: 规范化后的上下文路径
            is_listing_request: 是否为目录列出请求

        Returns:
            匹配的IndexNode列表
        """
        # 1. 目录内容列出
        if is_listing_request:
            return self._search_directory_listing(query)

        # 2. 路径前缀搜索或名称前缀搜索
        return self._search_by_name_or_path(query, contextual_base_path)

    async def _fallback_path_scan(
        self, query: str, contextual_base_path: str, is_listing_request: bool
    ) -> List[IndexNode]:
        """
        路径前缀搜索的回退扫描机制

        Args:
            query: 规范化后的查询字符串（包含路径分隔符）
            contextual_base_path: 规范化后的上下文路径
            is_listing_request: 是否为目录列出请求

        Returns:
            匹配的IndexNode列表
        """
        # 定位父级目录
        parent_path = "/".join(query.split("/")[:-1]) if "/" in query else ""

        # 检查父级目录在磁盘上是否存在
        parent_full_path = Path(self.root_path) / parent_path
        if parent_full_path.exists() and parent_full_path.is_dir():
            # 异步执行单层扫描
            loop = asyncio.get_running_loop()
            
            def _scan_directory():
                """同步扫描目录"""
                results = []
                try:
                    for item in parent_full_path.iterdir():
                        try:
                            relative_path = item.relative_to(self.root_path).as_posix()
                            # 检查是否应该索引以及是否为核心区文件
                            should_index, is_core = self._match_pathspec(
                                relative_path, item.is_dir()
                            )

                            if (
                                should_index
                                and relative_path not in self.index_data.path_to_node
                            ):
                                results.append((
                                    item.name,
                                    relative_path,
                                    item.is_dir(),
                                    item.stat().st_mtime if item.is_file() else 0.0,
                                    is_core
                                ))
                        except Exception:
                            continue
                except Exception:
                    pass
                return results

            # 异步执行扫描
            scan_results = await loop.run_in_executor(None, _scan_directory)
            
            # 批量创建节点并添加到索引
            for name, relative_path, is_dir, mtime, is_core in scan_results:
                node = self._create_index_node(
                    name,
                    relative_path,
                    is_dir,
                    mtime,
                    is_core,
                )
                self._add_node_to_index(node)

        # 重新执行搜索，但限制为仅内存搜索，避免递归调用回退扫描
        if is_listing_request:
            # 如果是目录列出请求，使用目录列出逻辑
            return self._search_directory_listing(query)
        elif "/" in query:
            return self._search_path_prefix(query)
        else:
            return self._search_name_prefix(query, contextual_base_path)

    async def _fallback_name_scan(
        self, query: str, contextual_base_path: str, is_listing_request: bool
    ) -> List[IndexNode]:
        """
        名称前缀搜索的回退扫描机制

        Args:
            query: 规范化后的查询字符串（不包含路径分隔符）
            contextual_base_path: 规范化后的上下文路径
            is_listing_request: 是否为目录列出请求

        Returns:
            匹配的IndexNode列表
        """
        # 检查空查询
        if not query:
            return []
            
        # 检查是否已经有其他任务完成了扫描
        # 先尝试搜索，如果已经有结果就直接返回
        temp_results = self._search_in_memory(
            query, contextual_base_path, is_listing_request
        )
        if temp_results:
            return temp_results

        # 使用异步锁防止并发的昂贵磁盘扫描
        async with self._sync_scan_lock:
            # 再次检查，避免在获取锁期间其他任务已完成扫描
            temp_results = self._search_in_memory(
                query, contextual_base_path, is_listing_request
            )
            if temp_results:
                return temp_results

            # 异步执行全局扫描
            loop = asyncio.get_running_loop()
            
            def _scan_global():
                """同步全局扫描"""
                results = []
                try:
                    query_lower = query.lower()
                    for item in Path(self.root_path).rglob("*"):
                        try:
                            if item.name.lower().startswith(query_lower):
                                relative_path = item.relative_to(self.root_path).as_posix()

                                # 检查是否应该索引以及是否为核心区文件
                                should_index, is_core = self._match_pathspec(
                                    relative_path, item.is_dir()
                                )

                                if (
                                    should_index
                                    and relative_path not in self.index_data.path_to_node
                                ):
                                    results.append((
                                        item.name,
                                        relative_path,
                                        item.is_dir(),
                                        item.stat().st_mtime if item.is_file() else 0.0,
                                        is_core
                                    ))
                        except Exception:
                            continue
                except Exception:
                    pass
                return results

            # 异步执行全局扫描
            scan_results = await loop.run_in_executor(None, _scan_global)
            
            # 批量创建节点并添加到索引
            for name, relative_path, is_dir, mtime, is_core in scan_results:
                node = self._create_index_node(
                    name,
                    relative_path,
                    is_dir,
                    mtime,
                    is_core,
                )
                self._add_node_to_index(node)

        # 重新执行名称前缀搜索，但限制为仅内存搜索，避免递归调用回退扫描
        if "/" in query:
            return self._search_path_prefix(query)
        else:
            return self._search_name_prefix(query, contextual_base_path)

    async def _fallback_scan(
        self, query: str, contextual_base_path: str, is_listing_request: bool
    ) -> List[IndexNode]:
        """
        回退扫描机制

        Args:
            query: 规范化后的查询字符串
            contextual_base_path: 规范化后的上下文路径
            is_listing_request: 是否为目录列出请求

        Returns:
            匹配的IndexNode列表
        """
        # 路径前缀搜索未命中，执行目录单层扫描
        if "/" in query:
            return await self._fallback_path_scan(
                query, contextual_base_path, is_listing_request
            )

        # 名称前缀搜索未命中，执行文件名全局扫描
        return await self._fallback_name_scan(
            query, contextual_base_path, is_listing_request
        )

    async def _handle_gitignore_changed(self) -> None:
        """
        异步处理.gitignore文件变化事件
        """
        try:
            # 重新构造gitignore PathSpec
            await self._construct_gitignore_pathspec()
            # 重新扫描索引以应用新的忽略规则
            self._rescan_index_for_ignore_rules()
        except Exception:
            # 忽略重建过程中的错误
            pass

    def _rescan_index_for_ignore_rules(self) -> None:
        """
        重新扫描索引以应用新的忽略规则
        根据新的.gitignore规则，移除被忽略的文件，添加不再被忽略的文件
        """
        # 创建需要移除的节点列表
        nodes_to_remove = []

        # 遍历所有索引节点
        for path, node in self.index_data.path_to_node.items():
            # 跳过根节点和核心节点
            if path == "" or node.is_core:
                continue

            # 检查节点是否应该被忽略
            try:
                # 使用新的PathSpec匹配路径
                if (
                    self._ignore_pathspec_static
                    and self._ignore_pathspec_static.match_file(path)
                ):
                    nodes_to_remove.append(path)
                    continue

                if self._ignore_pathspec_git and self._ignore_pathspec_git.match_file(
                    path
                ):
                    nodes_to_remove.append(path)
                    continue
            except Exception:
                # 忽略匹配过程中的错误
                continue

        # 移除被忽略的节点
        for path in nodes_to_remove:
            self._remove_node_from_index(path)

        # 注意：对于不再被忽略的文件，我们不主动添加它们
        # 因为它们可能已经被删除了，而且添加它们需要磁盘扫描
        # 这些文件会在用户搜索时通过回退扫描机制重新发现

    def _handle_file_created(self, file_path: str) -> None:
        """
        处理文件创建事件

        Args:
            file_path: 创建的文件路径
        """
        try:
            # 确保路径是字符串类型
            file_path_str = str(file_path)

            # 计算相对于项目根目录的路径
            relative_path = Path(file_path_str).relative_to(self.root_path).as_posix()

            # 检查是否应该索引以及是否为核心区文件
            should_index, is_core = self._match_pathspec(relative_path, False)

            if should_index:
                # 获取文件的修改时间
                mtime = Path(file_path_str).stat().st_mtime

                # 创建IndexNode对象
                node = self._create_index_node(
                    Path(file_path_str).name,
                    relative_path,
                    False,  # 不是目录
                    mtime,
                    is_core,
                )

                # 添加到索引结构中
                self._add_node_to_index(node)
        except Exception:
            # 忽略无法处理的文件
            pass

    def _handle_dir_created(self, dir_path: str) -> None:
        """
        处理目录创建事件

        Args:
            dir_path: 创建的目录路径
        """
        try:
            # 确保路径是字符串类型
            dir_path_str = str(dir_path)

            # 计算相对于项目根目录的路径
            relative_path = Path(dir_path_str).relative_to(self.root_path).as_posix()

            # 检查是否应该索引以及是否为核心区文件
            should_index, is_core = self._match_pathspec(relative_path, True)

            if should_index:
                # 创建IndexNode对象
                node = self._create_index_node(
                    Path(dir_path_str).name,
                    relative_path,
                    True,  # 是目录
                    0.0,  # 目录的mtime为0
                    is_core,
                )

                # 添加到索引结构中
                self._add_node_to_index(node)
        except Exception:
            # 忽略无法处理的目录
            pass

    def _handle_file_deleted(self, file_path: str) -> None:
        """
        处理文件删除事件

        Args:
            file_path: 删除的文件路径
        """
        try:
            # 确保路径是字符串类型
            file_path_str = str(file_path)

            # 计算相对于项目根目录的路径
            relative_path = Path(file_path_str).relative_to(self.root_path).as_posix()

            # 从索引中移除节点
            self._remove_node_from_index(relative_path)
        except Exception:
            # 忽略无法处理的文件
            pass

    def _handle_dir_deleted(self, dir_path: str) -> None:
        """
        处理目录删除事件

        Args:
            dir_path: 删除的目录路径
        """
        try:
            # 确保路径是字符串类型
            dir_path_str = str(dir_path)

            # 计算相对于项目根目录的路径
            relative_path = Path(dir_path_str).relative_to(self.root_path).as_posix()

            # 递归删除目录及其所有子节点
            self._remove_node_and_children(relative_path)
        except Exception:
            # 忽略无法处理的目录
            pass

    def _remove_node_and_children(self, path: str) -> None:
        """
        递归删除节点及其所有子节点

        Args:
            path: 要删除的节点路径
        """
        # 检查节点是否存在
        if path not in self.index_data.path_to_node:
            return

        node = self.index_data.path_to_node[path]

        # 如果是目录，先递归删除所有子节点
        if node.is_dir and node.children:
            # 创建子节点路径列表以避免在迭代时修改字典
            child_paths = []
            for child_name, child_node in node.children.items():
                child_paths.append(child_node.path)

            # 递归删除所有子节点
            for child_path in child_paths:
                self._remove_node_and_children(child_path)

        # 从索引中移除节点
        self._remove_node_from_index(path)

    def _handle_file_modified(self, file_path: str) -> None:
        """
        处理文件修改事件

        Args:
            file_path: 修改的文件路径
        """
        try:
            # 确保路径是字符串类型
            file_path_str = str(file_path)

            # 计算相对于项目根目录的路径
            relative_path = Path(file_path_str).relative_to(self.root_path).as_posix()

            # 检查文件是否在索引中
            if relative_path in self.index_data.path_to_node:
                node = self.index_data.path_to_node[relative_path]
                # 更新文件的修改时间
                node.mtime = Path(file_path_str).stat().st_mtime
        except Exception:
            # 忽略无法处理的文件
            pass

    def _handle_file_moved(self, src_path: str, dest_path: str) -> None:
        """
        处理文件移动/重命名事件

        Args:
            src_path: 源文件路径
            dest_path: 目标文件路径
        """
        try:
            # 确保路径是字符串类型
            src_path_str = str(src_path)
            dest_path_str = str(dest_path)

            # 计算相对于项目根目录的路径
            src_relative_path = (
                Path(src_path_str).relative_to(self.root_path).as_posix()
            )
            dest_relative_path = (
                Path(dest_path_str).relative_to(self.root_path).as_posix()
            )

            # 检查源文件是否在索引中
            if src_relative_path in self.index_data.path_to_node:
                # 移除旧节点
                self._remove_node_from_index(src_relative_path)

                # 检查目标文件是否应该索引
                should_index, is_core = self._match_pathspec(dest_relative_path, False)

                if should_index:
                    # 获取文件的修改时间
                    mtime = Path(dest_path_str).stat().st_mtime

                    # 创建新节点
                    node = self._create_index_node(
                        Path(dest_path_str).name,
                        dest_relative_path,
                        False,  # 不是目录
                        mtime,
                        is_core,
                    )

                    # 添加到索引结构中
                    self._add_node_to_index(node)
        except Exception:
            # 忽略无法处理的文件
            pass

    def _handle_dir_moved(self, src_path: str, dest_path: str) -> None:
        """
        处理目录移动/重命名事件

        Args:
            src_path: 源目录路径
            dest_path: 目标目录路径
        """
        try:
            # 确保路径是字符串类型
            src_path_str = str(src_path)
            dest_path_str = str(dest_path)

            # 计算相对于项目根目录的路径
            src_relative_path = (
                Path(src_path_str).relative_to(self.root_path).as_posix()
            )
            dest_relative_path = (
                Path(dest_path_str).relative_to(self.root_path).as_posix()
            )

            # 检查源目录是否在索引中
            if src_relative_path in self.index_data.path_to_node:
                # 移除旧节点
                self._remove_node_from_index(src_relative_path)

                # 检查目标目录是否应该索引
                should_index, is_core = self._match_pathspec(dest_relative_path, True)

                if should_index:
                    # 创建新节点
                    node = self._create_index_node(
                        Path(dest_path_str).name,
                        dest_relative_path,
                        True,  # 是目录
                        0.0,  # 目录的mtime为0
                        is_core,
                    )

                    # 添加到索引结构中
                    self._add_node_to_index(node)
        except Exception:
            # 忽略无法处理的目录
            pass

    def _remove_node_from_index(self, path: str) -> None:
        """
        从索引中移除节点

        Args:
            path: 要移除的节点路径
        """
        # 检查节点是否存在
        if path not in self.index_data.path_to_node:
            return

        node = self.index_data.path_to_node[path]

        # 从path_to_node字典中移除
        del self.index_data.path_to_node[path]

        # 从path_trie中移除
        self.index_data.path_trie.delete(path)

        # 从name_trie中移除
        existing_paths = self.index_data.name_trie.search(node.name)
        if existing_paths is not None:
            # 从路径列表中移除该路径
            if path in existing_paths:
                existing_paths.remove(path)
            # 如果列表为空，则从name_trie中移除该名称
            if not existing_paths:
                self.index_data.name_trie.delete(node.name)

        # 从dynamic_nodes_lru中移除（如果是动态节点）
        if not node.is_core and path in self.index_data.dynamic_nodes_lru:
            del self.index_data.dynamic_nodes_lru[path]

        # 从父节点的children字典中移除
        if node.parent:
            if node.name in node.parent.children:
                del node.parent.children[node.name]

    def _normalize_input(
        self, query: str, contextual_base_path: str
    ) -> Tuple[str, str, bool]:
        """
        两阶段输入规范化处理，确保所有路径操作都在项目根目录范围内进行

        Args:
            query: 用户输入的原始查询字符串
            contextual_base_path: 用户发起搜索时所在的上下文目录

        Returns:
            (normalized_query, normalized_context_path, is_listing_request) 元组
        """
        # 保存原始query用于判断是否为目录列出请求
        original_query = query

        # 判断搜索类型
        is_listing_request = original_query.strip().endswith("/")

        # 阶段一：contextual_base_path 合法性验证
        # 1. 去除首尾空格
        contextual_base_path = contextual_base_path.strip()

        # 2. 统一路径分隔符
        contextual_base_path = contextual_base_path.replace("\\", "/")

        # 3. 路径拼接与解析
        try:
            # 构建完整路径并解析
            # 特殊处理：如果contextual_base_path以"/"开头，将其视为相对于项目根目录的路径
            if contextual_base_path.startswith("/"):
                # 去掉前导"/"，然后直接相对于项目根目录
                context_relative = contextual_base_path[1:]  # 去掉前导"/"
                full_context_path = Path(self.root_path) / context_relative
            else:
                # 正常处理：相对于项目根目录
                full_context_path = Path(self.root_path) / contextual_base_path
            resolved_context_path = full_context_path.resolve()

            # 4. 安全性验证
            root_path_obj = Path(self.root_path).resolve()
            if not resolved_context_path.is_relative_to(root_path_obj):
                # 路径超出项目根目录范围，拒绝处理
                return "", "", is_listing_request

            # 提取相对于项目根目录的路径
            if resolved_context_path == root_path_obj:
                normalized_context_path = ""
            else:
                normalized_context_path = resolved_context_path.relative_to(
                    root_path_obj
                ).as_posix()

        except (ValueError, RuntimeError):
            # 路径解析失败，拒绝处理
            return "", "", is_listing_request

        # 阶段二：目标路径构建与规范化
        try:
            # 5. 初步清理query
            query = query.strip()
            query = query.replace("\\", "/")

            # 6. 构建目标绝对路径
            # 特殊处理：如果query以"/"开头，将其视为相对于项目根目录的路径
            if query.startswith("/"):
                # 去掉前导"/"，然后直接相对于项目根目录
                query_relative = query[1:]  # 去掉前导"/"
                full_target_path = Path(self.root_path) / query_relative
            else:
                # 正常处理：相对于上下文路径
                full_target_path = (
                    Path(self.root_path) / normalized_context_path / query
                )

            resolved_target_path = full_target_path.resolve()

            # 7. 路径解析与安全验证
            if not resolved_target_path.is_relative_to(root_path_obj):
                # 路径超出项目根目录范围，拒绝处理
                return "", "", is_listing_request

            # 8. 提取规范化查询字符串
            if resolved_target_path == root_path_obj:
                normalized_query = ""
            else:
                normalized_query = resolved_target_path.relative_to(
                    root_path_obj
                ).as_posix()

            # 9. 格式统一：确保无前导/结尾斜杠
            normalized_query = normalized_query.strip("/")
            normalized_context_path = normalized_context_path.strip("/")

        except (ValueError, RuntimeError):
            # 路径解析失败，拒绝处理
            return "", "", is_listing_request

        return normalized_query, normalized_context_path, is_listing_request

    async def _wait_for_initialization(self) -> None:
        """
        等待初始化完成
        """
        while self._initializing:
            await asyncio.sleep(0.1)

    def _update_lru_for_nodes(self, nodes: List[IndexNode]) -> None:
        """
        更新节点的LRU状态

        Args:
            nodes: 需要更新LRU状态的节点列表
        """
        current_time = time.time()
        for node in nodes:
            if not node.is_core:
                if node.path in self.index_data.dynamic_nodes_lru:
                    # 将节点移到LRU末尾（标记为最近访问）
                    del self.index_data.dynamic_nodes_lru[node.path]
                self.index_data.dynamic_nodes_lru[node.path] = current_time

    def _check_and_evict_lru(self) -> None:
        """
        检查并执行LRU淘汰
        """
        dynamic_nodes_count = len(self.index_data.dynamic_nodes_lru)
        if dynamic_nodes_count > self.DYNAMIC_NODES_LRU_LIMIT:
            # 计算需要淘汰的节点数量（超出数量的110%）
            overflow_count = dynamic_nodes_count - self.DYNAMIC_NODES_LRU_LIMIT
            evict_count = int(overflow_count * 1.1)

            # 从LRU字典开头选择最久未访问的节点进行淘汰
            nodes_to_evict = []
            for _ in range(evict_count):
                if self.index_data.dynamic_nodes_lru:
                    oldest_path = next(iter(self.index_data.dynamic_nodes_lru))
                    nodes_to_evict.append(oldest_path)
                    del self.index_data.dynamic_nodes_lru[oldest_path]

            # 从所有索引结构中移除被淘汰的节点
            for path in nodes_to_evict:
                self._remove_node_from_index(path)
