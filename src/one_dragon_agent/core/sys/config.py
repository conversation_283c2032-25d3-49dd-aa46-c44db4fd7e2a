import os
import uuid
from dataclasses import dataclass, field
from ..model.config import ModelConfig
from . import env


@dataclass
class OdaSessionConfig:
    """
    Configuration for an OdaSession.

    Args:
        common_llm_config: Configuration for the common fallback LLM client.
        debug: Enable debug mode for logging and debugging.
        log_dir: Directory to store logs.
        session_id: Unique ID for the session.
        current_working_directory: The current working directory for the session.
    """

    common_llm_config: ModelConfig
    debug: bool = False
    log_dir: str = ""
    session_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    current_working_directory: str = field(default_factory=os.getcwd)


def create_session_config_from_env() -> OdaSessionConfig:
    """
    Creates OdaSessionConfig by reading settings from environment variables.

    Returns:
        An instance of OdaSessionConfig.

    Raises:
        ValueError: If required environment variables are not set.
        NotImplementedError: If a non-supported LLM type is specified.
    """
    llm_type = os.getenv(env.ODA_LLM_COMMON_TYPE, "openai")

    # Fetch required variables for LLMConfig
    model = os.getenv(env.ODA_LLM_COMMON_MODEL)
    api_key = os.getenv(env.ODA_LLM_COMMON_API_KEY)

    # Create LLMConfig with required and optional variables
    common_llm_config = ModelConfig(
        model=model, api_key=api_key, base_url=os.getenv(env.ODA_LLM_COMMON_BASE_URL)
    )

    # Read debug setting from environment
    debug_str = os.getenv(env.ODA_DEBUG, "false").lower()
    debug = debug_str in ("true", "1", "yes", "on")
    log_dir = os.getenv("ODA_LOG_DIR", ".log")

    # Create and return the session config
    return OdaSessionConfig(
        common_llm_config=common_llm_config, debug=debug, log_dir=log_dir
    )
