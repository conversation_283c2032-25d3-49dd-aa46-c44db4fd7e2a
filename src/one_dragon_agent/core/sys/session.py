import asyncio
import logging
import os
import re
import uuid
from typing import AsyncIterator, Optional

from .config import OdaSessionConfig
from .file_reader import read_text_file
from .workspace_index import DirectoryCacheManager
from .message import OdaMessage, OdaMessageSource
from .prompts import get_system_prompts
from ..agent.agent import Agent
from ..agent.tool.tool_event import PermissionResponseEvent
from ..event.dispatcher import EventDispatcher
from ..event.file_events import FileSearchRequestEvent, FileSearchResultsEvent
from ..message_manager.manager import MessageManager
from ..model.client_factory import ModelClientFactory
from ..model.message import (
    OdaModelMessage,
    OdaModelMessageRole,
    OdaMessageTextContent,
)
from ..system_reminder.reminder import ReminderManager
from one_dragon_agent.core.agent.tool.tool_manager import ToolManager
from one_dragon_agent.core.agent.tool.permission.permission_manager import PermissionManager


class OdaSession:
    """
    Represents a session window that interfaces with the CLI or other input
    systems. This class integrates the entire real-time steering system.
    """

    def __init__(self, config: OdaSessionConfig, auto_start: bool = True):
        self.config = config
        self._message_queue: asyncio.Queue[OdaMessage] = asyncio.Queue()
        self._message_manager = MessageManager()
        self._llm_factory = ModelClientFactory()
        self._event_dispatcher = EventDispatcher()
        self._session_id = str(uuid.uuid4())
        self._reminder_manager = ReminderManager(session_id=self._session_id)
        self._permission_manager = PermissionManager()

        # Create and manage all tools via the ToolManager
        self._tool_manager = ToolManager(self._permission_manager)

        # Initialize directory cache manager
        self._directory_cache = DirectoryCacheManager()
        
        # Subscribe to file search events
        self._event_dispatcher.subscribe(
            FileSearchRequestEvent, 
            self._handle_file_search_request
        )

        # Initialize session with system prompts and other setup
        self._initialize_session()

        # 启动后台任务
        self._processing_task = None
        if auto_start:
            self._processing_task = asyncio.create_task(self._process_messages())
            # Start directory cache manager
            asyncio.create_task(self._directory_cache.start())

    def _initialize_session(self) -> None:
        """
        Initialize the session with system prompts and other necessary setup.
        This method can be extended to include additional initialization logic.
        """
        # Add system prompts to message manager
        system_prompts = get_system_prompts(self.config)
        for prompt in system_prompts:
            self._message_manager.append_system_prompt(prompt)

    async def send_input(self, message: OdaMessage) -> None:
        """
        Send an OdaMessage to the message queue.
        This method is meant to be called by the CLI or other input systems.

        Args:
            message: The OdaMessage to send to the message queue
        """
        # 只允许用户消息进入处理队列
        if message.source == OdaMessageSource.USER:
            await self._message_queue.put(message)

    async def _process_messages(self):
        """
        Processes messages from the message queue by creating and executing
        an Agent for each one. It streams the agent's response to the output
        queue and saves the full response to history.
        """
        async for message in self.get_message_stream():
            # Skip None messages (used for signaling end of stream)
            if message is None:
                continue

            # Process file references in the message
            await self._process_file_references(message)

            # Create an Agent
            agent = Agent(
                session_config=self.config,
                message_manager=self._message_manager,
                command=message,
                llm_factory=self._llm_factory,
                tool_manager=self._tool_manager,
                event_dispatcher=self._event_dispatcher,
                reminder_manager=self._reminder_manager,
                permission_manager=self._permission_manager,
            )

            # Execute the agent
            await agent.execute()

    async def handle_permission_response(
        self, permission: str, granted: bool, scope: str
    ) -> None:
        """
        Handles the user's response to a permission request.

        This method is called directly by the CLI to provide the user's decision
        regarding a permission request. It updates the PermissionManager's state
        accordingly.

        Args:
            permission: The permission string that was requested.
            granted: Whether the user granted the permission.
            scope: The scope of the permission ('once' or 'session').
        """
        # If granted for the session, record it in the PermissionManager
        if granted and scope == "session":
            await self._permission_manager.grant(self._session_id, permission)

        # Set the response signal to unblock the waiting tool
        await self._permission_manager.set_response_signal(
            self._session_id, permission, granted
        )

    async def get_message_stream(self) -> AsyncIterator[OdaMessage]:
        """
        Asynchronous generator that yields validated OdaMessage objects
        from the message queue.
        """
        while True:
            message = await self._message_queue.get()
            if message is None:
                break
            yield message

    async def close(self):
        """Clean up session resources."""
        if self._processing_task and not self._processing_task.done():
            self._processing_task.cancel()
            try:
                await self._processing_task
            except asyncio.CancelledError:
                pass

        await self._event_dispatcher.close()
        # 关闭目录缓存管理器
        await self._directory_cache.stop()
        # 关闭所有缓存的LLM客户端
        await self._llm_factory.close_all()

    def abort(self, reason: Optional[str] = None) -> None:
        """Abort processing by cancelling the background tasks."""
        if self._processing_task and not self._processing_task.done():
            self._processing_task.cancel()
        # It's important to also handle the cleanup of resources like the
        # LLM client. This can be done by ensuring close() is called when the
        # app exits. For now, we focus on stopping the tasks.

    async def _process_file_references(self, message: OdaMessage) -> None:
        """
        Process file references in the message content.
        Extracts file paths from <oda-at-file></oda-at-file> tags,
        reads the files, and adds their content to the message manager.

        Args:
            message: The OdaMessage to process for file references
        """
        if not hasattr(message.content, "text"):
            return

        text_content = message.content.text
        if not text_content:
            return

        # Find all file references using regex
        file_pattern = r"<oda-at-file>(.*?)</oda-at-file>"
        file_matches = re.findall(file_pattern, text_content, re.DOTALL)

        if not file_matches:
            return

        # Get current working directory from config
        current_dir = self.config.current_working_directory

        # List to store file processing results for display message
        # Each item is a tuple: (original_path, success_bool)
        file_processing_results: list[tuple[str, bool]] = []

        # Process each file reference
        for file_path in file_matches:
            original_path = file_path.strip()
            if not original_path:
                file_processing_results.append((original_path, False))
                continue

            # Resolve the file path
            resolved_path = self._resolve_file_path(original_path, current_dir)
            if not resolved_path:
                file_processing_results.append((original_path, False))
                continue

            # Read the file content
            try:
                await self._read_and_add_file_content(resolved_path, original_path)
                file_processing_results.append((original_path, True))
            except Exception:
                file_processing_results.append((original_path, False))

        # Clean the message content by removing the file reference tags
        if file_matches:
            cleaned_text = re.sub(file_pattern, "", text_content, flags=re.DOTALL)
            message.content = OdaMessageTextContent(cleaned_text.strip())

        # Send a display message about the files processed
        if file_processing_results:
            await self._send_file_read_display_message(file_processing_results)

    def _resolve_file_path(self, file_path: str, current_dir: str) -> Optional[str]:
        """
        Resolve and validate the file path.

        Args:
            file_path: The file path to resolve
            current_dir: The current working directory

        Returns:
            The resolved absolute path if valid, None otherwise
        """
        try:
            # Convert to absolute path
            if os.path.isabs(file_path):
                resolved_path = file_path
            else:
                resolved_path = os.path.join(current_dir, file_path)

            # Normalize the path
            resolved_path = os.path.normpath(resolved_path)

            # Security check: ensure the path is within the current
            # directory or its subdirectories
            current_dir_abs = os.path.abspath(current_dir)
            resolved_path_abs = os.path.abspath(resolved_path)

            if not resolved_path_abs.startswith(current_dir_abs):
                # Path traversal attempt detected
                return None

            # Check if the path exists and is a file
            if not os.path.exists(resolved_path_abs):
                return None

            if not os.path.isfile(resolved_path_abs):
                return None

            return resolved_path_abs

        except Exception:
            return None

    async def _read_and_add_file_content(
        self, file_path: str, original_path: str
    ) -> None:
        """
        Read file content and add it to the message manager as a system message.

        Args:
            file_path: The absolute path to the file to read.
            original_path: The original path as specified by the user.
        """
        try:
            # Use the new file reader service to get the content
            file_content_result = read_text_file(file_path)

            # read_text_file returns a tuple (content, total_lines)
            if file_content_result is not None:
                file_content, _ = file_content_result
                if file_content is not None:
                    # Create system message with file content
                    content_text = (
                        f"User has provided the following file as context for the conversation:\n"
                        f"File: {original_path}\n"
                        f"Content:\n```\n{file_content}\n```\n"
                        f"(End of file content from {original_path})"
                    )
                    system_message = OdaModelMessage(
                        role=OdaModelMessageRole.SYSTEM,
                        content=OdaMessageTextContent(content_text),
                    )

                    # Add to message manager
                    self._message_manager.append_system_prompt(system_message)
            # If file_content_result is None or file_content is None, error already logged by read_text_file

        except Exception as e:
            # If reading fails, don't add anything to message manager
            print(f"Unexpected error processing file {file_path}: {e}")
            pass

    async def _send_file_read_display_message(
        self, file_processing_results: list[tuple[str, bool]]
    ) -> None:
        """
        Send a display message indicating which files were processed (read or failed).

        Args:
            file_processing_results: A list of tuples, where each tuple contains
                                     the original file path and a boolean indicating
                                     success (True) or failure (False).
        """
        try:
            # Format the list of files for the message
            if not file_processing_results:
                return

            # Process up to the first 4 files
            files_to_display = file_processing_results[:4]
            remaining_count = len(file_processing_results) - 4

            files_list_str = ""
            for original_path, success in files_to_display:
                status = "✅ 成功" if success else "❌ 失败"
                files_list_str += f"  - {original_path} ({status})\n"

            # Remove the trailing newline if there are no more files to list
            if files_list_str.endswith("\n"):
                files_list_str = files_list_str[:-1]

            if remaining_count > 0:
                files_list_str += f"\n  ... 还有 {remaining_count} 个文件."

            content_text = f"文件读取结果:\n{files_list_str}"

            # Create and publish a system event for file read results
            if self._event_dispatcher:
                from ..event.event import Event
                from dataclasses import dataclass
                from typing import List, Dict, Any

                @dataclass
                class FileReadResultsEvent(Event):
                    """Event for file read results."""
                    data: Dict[str, Any]

                event = FileReadResultsEvent(
                    event_type="file.read_results",
                    data={
                        "session_id": self._session_id,
                        "results": file_processing_results,
                    },
                )
                await self._event_dispatcher.publish(event)
        except Exception as e:
            # If sending the display message fails, log it but don't interrupt the flow
            print(f"Error sending file read display message: {e}")
            pass

    async def _handle_file_search_request(self, event: FileSearchRequestEvent) -> None:
        """
        Handle file search request from CLI.
        
        Args:
            event: The file search request event
        """
        try:
            # Use directory cache for search
            results = await self._directory_cache.search(
                query=event.query,
                base_path=event.base_path,
                limit=50
            )
            
            # Convert results to expected format
            formatted_results = []
            for result in results:
                if result['is_dir']:
                    formatted_results.append({
                        "path": result['path'],
                        "type": "folder"
                    })
                else:
                    formatted_results.append({
                        "path": result['path'],
                        "type": "file"
                    })
            
            self._event_dispatcher.publish(FileSearchResultsEvent(
                request_id=event.request_id,
                results=formatted_results,
                from_cache=True
            ))
                
        except Exception as e:
            logging.error(f"Error handling file search request: {e}")
            self._event_dispatcher.publish(FileSearchResultsEvent(
                request_id=event.request_id,
                results=[],
                from_cache=False,
                error=str(e)
            ))
