"""
This module defines constants for environment variable keys used throughout the OneDragon-Agent system.
Using constants helps prevent typos and makes the code easier to manage.
"""

# Environment variable keys for LLM configuration
ODA_LLM_COMMON_TYPE = (
    "ODA_LLM_COMMON_TYPE"  # Specifies the LLM provider type ('openai' or 'anthropic')
)
ODA_LLM_COMMON_BASE_URL = "ODA_LLM_COMMON_BASE_URL"  # The base URL for the LLM API
ODA_LLM_COMMON_MODEL = "ODA_LLM_COMMON_MODEL"  # The specific model name to use
ODA_LLM_COMMON_API_KEY = "ODA_LLM_COMMON_API_KEY"  # The API key for authentication

# Environment variable keys for debug configuration
ODA_DEBUG = "ODA_DEBUG"  # Enable debug mode (True/False)
