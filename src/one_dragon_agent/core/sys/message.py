from enum import StrEnum

from ..model.message import OdaModalMessageContent


class OdaMessageSource(StrEnum):
    """The originator of the message."""

    USER = "user"  # Message from the end-user
    ODA = "oda"  # Message from the ODA system
    AI_RESPONSE = "ai_response"  # Real-time streaming response from the AI
    TOOL_RESULT = "tool_result"  # Result of a tool call


class OdaMessage:
    """
    Message in ODA system
    """

    def __init__(self, source: str, content: OdaModalMessageContent):
        self.source: str = source
        self.content: OdaModalMessageContent = content
