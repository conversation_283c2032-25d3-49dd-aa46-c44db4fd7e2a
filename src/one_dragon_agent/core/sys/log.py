"""
日志配置模块 for OneDragon-Agent

支持通过环境变量控制日志输出：
- ODA_LOG_PATH: 日志文件目录，如果为空则不输出到文件
- ODA_LOG_LEVEL: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
"""

import logging
import os
from pathlib import Path
from typing import Optional


class OdaLogger:
    """OneDragon-Agent 日志管理器"""

    _instance: Optional["OdaLogger"] = None
    _initialized: bool = False

    def __new__(cls) -> "OdaLogger":
        """单例模式"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        """初始化日志管理器"""
        # Note: We don't check _initialized here to allow re-initialization in tests
        # This is needed because tests may set different environment variables

        self.log_path: Optional[str] = None
        self.log_level: str = "INFO"
        self.logger: Optional[logging.Logger] = None

        # We still set a flag to track if we've been initialized at least once
        if not hasattr(self, "_first_init"):
            self._first_init = True
        else:
            self._first_init = False

    def initialize(self) -> None:
        """初始化日志系统"""
        # 从环境变量读取配置
        self.log_path = os.getenv("ODA_LOG_DIR", "").strip()
        self.log_level = os.getenv("ODA_LOG_LEVEL", "INFO").upper()

        # 验证日志级别
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self.log_level not in valid_levels:
            print(f"警告: 无效的日志级别 '{self.log_level}'，使用默认级别 'INFO'")
            self.log_level = "INFO"

        # 创建根日志记录器
        self.logger = logging.getLogger("one_dragon_agent")
        self.logger.setLevel(getattr(logging, self.log_level))

        # 清除现有的处理器
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)

        # 设置日志格式
        log_format = "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
        formatter = logging.Formatter(log_format)

        # 文件处理器（只有在ODA_LOG_PATH不为空时才创建）
        if self.log_path:
            try:
                # 创建日志目录
                log_dir = Path(self.log_path)
                log_dir.mkdir(parents=True, exist_ok=True)

                # 系统日志文件
                system_log_file = log_dir / "oda_log.txt"
                system_file_handler = logging.FileHandler(
                    system_log_file, mode="a", encoding="utf-8"
                )
                system_file_handler.setLevel(logging.DEBUG)  # 文件总是记录DEBUG级别
                system_file_handler.setFormatter(formatter)
                self.logger.addHandler(system_file_handler)

                # 记录初始化信息
                self.logger.info(f"日志系统初始化成功")
                self.logger.info(f"日志目录: {self.log_path}")
                self.logger.info(f"系统日志文件: {system_log_file}")
                self.logger.info(f"日志级别: {self.log_level}")

            except Exception as e:
                print(f"错误: 无法创建日志目录 '{self.log_path}': {e}")
                print("日志将只输出到控制台")
        else:
            self.logger.info("日志系统初始化成功（仅控制台输出）")
            self.logger.info(f"日志级别: {self.log_level}")

        # 设置第三方库的日志级别
        self._setup_third_party_logging()

    def _setup_third_party_logging(self) -> None:
        """设置第三方库的日志级别"""
        if not self.logger:
            return

        # 减少第三方库的日志噪音
        logging.getLogger("httpx").setLevel(logging.WARNING)
        logging.getLogger("httpcore").setLevel(logging.WARNING)
        logging.getLogger("openai").setLevel(logging.INFO)
        logging.getLogger("textual").setLevel(logging.WARNING)
        logging.getLogger("urllib3").setLevel(logging.WARNING)

        # 如果是DEBUG级别，记录更多详细信息
        if self.log_level == "DEBUG":
            self.logger.debug("DEBUG模式已启用，将记录详细日志信息")

    def get_logger(self, name: Optional[str] = None) -> logging.Logger:
        """获取系统日志记录器"""
        if not self.logger:
            self.initialize()

        if name:
            return logging.getLogger(f"one_dragon_agent.{name}")
        return self.logger or logging.getLogger("one_dragon_agent")

    def get_messages_logger(self) -> logging.Logger:
        """获取消息日志记录器"""
        if not self.messages_logger:
            self.initialize()
        return self.messages_logger or logging.getLogger("one_dragon_agent.messages")

    def is_debug_enabled(self) -> bool:
        """检查是否启用了DEBUG级别"""
        return self.log_level == "DEBUG"

    def is_file_logging_enabled(self) -> bool:
        """检查是否启用了文件日志"""
        return bool(self.log_path)


# 全局日志管理器实例
_od_logger = OdaLogger()


def get_logger(name: Optional[str] = None) -> logging.Logger:
    """
    获取OneDragon-Agent系统日志记录器

    Args:
        name: 日志记录器名称，通常使用模块名

    Returns:
        logging.Logger: 配置好的日志记录器
    """
    return _od_logger.get_logger(name)


def get_messages_logger() -> logging.Logger:
    """
    获取OneDragon-Agent消息日志记录器

    Returns:
        logging.Logger: 配置好的消息日志记录器
    """
    return _od_logger.get_messages_logger()


def initialize_logging(force_reinitialize: bool = False) -> None:
    """初始化OneDragon-Agent日志系统"""
    if force_reinitialize:
        # Reset the initialization flag to allow re-initialization
        OdaLogger._initialized = False
    _od_logger.initialize()


def is_debug_enabled() -> bool:
    """检查是否启用了DEBUG级别"""
    return _od_logger.is_debug_enabled()


def is_file_logging_enabled() -> bool:
    """检查是否启用了文件日志"""
    return _od_logger.is_file_logging_enabled()


# 便捷函数
def debug(msg: str, *args, **kwargs) -> None:
    """DEBUG级别日志"""
    get_logger().debug(msg, *args, **kwargs)


def info(msg: str, *args, **kwargs) -> None:
    """INFO级别日志"""
    get_logger().info(msg, *args, **kwargs)


def warning(msg: str, *args, **kwargs) -> None:
    """WARNING级别日志"""
    get_logger().warning(msg, *args, **kwargs)


def error(msg: str, *args, **kwargs) -> None:
    """ERROR级别日志"""
    get_logger().error(msg, *args, **kwargs)


# 模块加载时自动初始化
def _auto_initialize():
    """模块加载时自动初始化日志系统"""
    try:
        initialize_logging()
    except Exception as e:
        print(f"日志系统初始化失败: {e}")


# 自动初始化
_auto_initialize()
