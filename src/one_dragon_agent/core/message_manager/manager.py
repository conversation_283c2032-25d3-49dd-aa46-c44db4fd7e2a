from typing import List

from ..model.message import OdaModelMessage


class MessageManager:
    """
    Manages the message history for an OdaSession.
    This class maintains separate lists for system prompts and message history,
    and provides methods to append new messages.
    """

    def __init__(self):
        """Initialize the message manager with empty system prompts and message history."""
        self._model_system_prompts: List[OdaModelMessage] = []
        self._model_message_history: List[OdaModelMessage] = []

    def set_system_prompts(self, system_prompts: List[OdaModelMessage]) -> None:
        """
        Set the system prompts for the session.

        Args:
            system_prompts: A list of OdaModelMessage objects representing system prompts.
        """
        self._model_system_prompts = system_prompts.copy()

    def append_system_prompt(self, prompt: OdaModelMessage) -> None:
        """
        Append a system prompt to the system prompts list.

        Args:
            prompt: The OdaModelMessage to append to the system prompts.
        """
        self._model_system_prompts.append(prompt)

    def append_message(self, message: OdaModelMessage) -> None:
        """
        Append a message to the message history.

        Args:
            message: The OdaModelMessage to append to the history.
        """
        self._model_message_history.append(message)

    def get_message_use(self) -> List[OdaModelMessage]:
        """
        Get the combined list of system prompts and message history for use in LLM calls.
        This method converts OdaModelMessage objects to LLMessage objects.

        Returns:
            A list of LLMessage objects combining system prompts and message history.
        """
        combined_messages = self._model_system_prompts + self._model_message_history
        llm_messages = []

        for message in combined_messages:
            # Convert OdaModelMessage to LLMessage
            llm_message = OdaModelMessage(role=message.role, content=message.content)
            llm_messages.append(llm_message)

        return llm_messages

    def get_message_history(self) -> List[OdaModelMessage]:
        """
        Get the current message history (without system prompts).

        Returns:
            A list of OdaModelMessage objects representing the message history.
        """
        return self._model_message_history.copy()

    def get_system_prompts(self) -> List[OdaModelMessage]:
        """
        Get the current system prompts.

        Returns:
            A list of OdaModelMessage objects representing the system prompts.
        """
        return self._model_system_prompts.copy()

    def clear_history(self) -> None:
        """Clear all messages from the history (but not system prompts)."""
        self._model_message_history.clear()

    def clear_all(self) -> None:
        """Clear all messages from both history and system prompts."""
        self._model_system_prompts.clear()
        self._model_message_history.clear()
