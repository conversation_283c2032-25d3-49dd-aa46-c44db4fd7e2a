"""
LLM Client abstract interface.
"""

from abc import ABC, abstractmethod
from typing import List, AsyncIterator, Dict

from openai.types.chat import (
    ChatCompletionChunk,
    ChatCompletion,
)

from .config import ModelConfig
from .message import OdaModelMessage
from one_dragon_agent.core.agent.tool import OdaTool


class ModelClient(ABC):
    """
    Abstract base class for LLM clients.
    """

    def __init__(self, config: ModelConfig):
        self.config = config

    @abstractmethod
    async def chat_completion_blocking(
        self,
        messages: List[OdaModelMessage],
        tools: Dict[str, OdaTool] = None,
        **kwargs,
    ) -> ChatCompletion:
        """
        Send a blocking chat completion request.
        """
        pass

    @abstractmethod
    async def chat_completion_stream(
        self,
        messages: List[OdaModelMessage],
        tools: Dict[str, OdaTool] = None,
        **kwargs,
    ) -> AsyncIterator[ChatCompletionChunk]:
        """
        Send a streaming chat completion request.
        """
        # The body of an abstract method is not executed,
        # but we can yield from an empty sequence to satisfy the type checker.
        # This is a neat trick to make the abstract method itself a valid async generator.
        yield ChatCompletionChunk(
            id="", created=0, model="", choices=[], object="chat.completion.chunk"
        )

    @abstractmethod
    async def close(self) -> None:
        """
        Close the client and clean up resources.
        """
        pass

    @abstractmethod
    async def is_closed(self) -> bool:
        pass

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
