from enum import StrEnum
from typing import Union, Any


class OdaModelMessageRole(StrEnum):
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    TOOL = "tool"


class OdaModelMessageType(StrEnum):

    TEXT = "text"
    TOOL_CALLS = "tool_calls"  # llm requests for tool calls


class OdaMessageTextContent:

    def __init__(self, text: str):
        self.text: str = text

    def to_dict(self) -> dict[str, Any]:
        return {"type": "text", "text": self.text}


class OdaToolCall:

    def __init__(self, tool_call_id: str, tool_name: str, tool_args: str):

        self.tool_call_id: str = tool_call_id
        self.tool_name: str = tool_name
        self.tool_args: str = tool_args

    def to_dict(self) -> dict[str, Any]:
        return {
            "tool_call_id": self.tool_call_id,
            "tool_name": self.tool_name,
            "tool_args": self.tool_args,
        }


class OdaMessageToolCallsContent:

    def __init__(self, tool_calls: list[OdaToolCall]):
        self.tool_calls: list[OdaToolCall] = tool_calls

    def to_dict(self) -> dict[str, Any]:
        return {
            "type": "tool_calls",
            "tool_calls": [tc.to_dict() for tc in self.tool_calls],
        }


class OdaMessageToolResponseContent:

    def __init__(self, tool_call_id: str, response: str):
        self.tool_call_id: str = tool_call_id
        self.response: str = response

    def to_dict(self) -> dict[str, Any]:
        return {
            "type": "tool_response",
            "tool_call_id": self.tool_call_id,
            "response": self.response,
        }


OdaModalMessageContent = Union[
    OdaMessageTextContent,
    OdaMessageToolCallsContent,
    OdaMessageToolResponseContent,
]


class OdaModelMessage:

    def __init__(self, role: str, content: OdaModalMessageContent):
        self.role: str = role
        self.content: OdaModalMessageContent = content

    def to_dict(self) -> dict[str, Any]:
        content_dict = {}
        if hasattr(self.content, "to_dict") and callable(self.content.to_dict):
            content_dict = self.content.to_dict()
        else:
            # Fallback for unknown content types
            content_str = str(self.content)
            if len(content_str) > 500:
                content_str = content_str[:500] + "... [truncated]"
            content_dict = {
                "type": "unknown",
                "content_type": str(type(self.content)),
                "content": content_str,
            }

        return {"role": self.role, "content": content_dict}
