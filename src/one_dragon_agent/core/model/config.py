"""
Configuration models for OpenAI-compatible LLM interaction.

This module defines the configuration data structures for OpenAI-compatible
LLM clients using the OpenAI SDK.
"""

from dataclasses import dataclass
from typing import Dict, Optional, Any


@dataclass
class ModelConfig:
    """
    Configuration for OpenAI-compatible LLM client.

    Args:
        model: The model name to use (e.g., "gpt-3.5-turbo", "gpt-4")
        api_key: API key for authentication
        base_url: Custom base URL for OpenAI-compatible API (optional)
        max_tokens: Maximum tokens in response
        temperature: Sampling temperature (0.0 to 2.0)
        timeout: Request timeout in seconds
        organization: OpenAI organization ID (optional)
        project: OpenAI project ID (optional)
    """

    model: str
    api_key: str
    base_url: Optional[str] = None
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    timeout: int = 30
    organization: Optional[str] = None
    project: Optional[str] = None

    def to_openai_kwargs(self) -> Dict[str, Any]:
        """
        Convert to OpenAI client initialization kwargs.

        Returns:
            Dictionary of OpenAI client parameters
        """
        kwargs = {
            "timeout": self.timeout,
            "max_retries": 0,  # We handle retries at a higher level
        }

        if self.api_key:
            kwargs["api_key"] = self.api_key
        if self.base_url:
            kwargs["base_url"] = self.base_url
        if self.organization:
            kwargs["organization"] = self.organization
        if self.project:
            kwargs["project"] = self.project

        return kwargs

    def to_completion_kwargs(self) -> Dict[str, Any]:
        """
        Convert to chat completion request kwargs.

        Returns:
            Dictionary of completion parameters
        """
        kwargs = {
            "model": self.model,
        }

        if self.max_tokens is not None:
            kwargs["max_tokens"] = self.max_tokens
        if self.temperature is not None:
            kwargs["temperature"] = self.temperature

        return kwargs
