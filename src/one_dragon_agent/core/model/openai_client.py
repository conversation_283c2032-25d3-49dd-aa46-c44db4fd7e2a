"""
LLM client for OpenAI-compatible APIs.
"""

from typing import Async<PERSON><PERSON><PERSON>, List, Dict

from openai import Async<PERSON>penA<PERSON>
from openai.types.chat import (
    Chat<PERSON>ompletion,
    ChatCompletionMessageParam,
    ChatCompletionToolParam,
    ChatCompletionUserMessageParam,
    ChatCompletionSystemMessageParam,
    ChatCompletionAssistantMessageParam,
    ChatCompletionMessageToolCallParam,
    ChatCompletionToolMessageParam,
)
from openai.types.shared_params import FunctionDefinition

from .config import ModelConfig
from .message import (
    OdaModelMessage,
    OdaMessageTextContent,
    OdaToolCall,
    OdaMessageToolCallsContent,
    OdaMessageToolResponseContent,
    OdaModalMessageContent,
)
from one_dragon_agent.core.agent.tool import OdaTool
from .base import ModelClient


class OpenAIClient(ModelClient):
    """
    LLM client for OpenAI and compatible APIs.
    """

    def __init__(self, config: ModelConfig):
        ModelClient.__init__(self, config)
        self.client = AsyncOpenAI(**config.to_openai_kwargs())

    def _convert_messages_to_openai(
        self, message_list: list[OdaModelMessage]
    ) -> list[ChatCompletionMessageParam]:
        """
        Convert LLMessage objects to OpenAI SDK message format.

        Args:
            message_list: List of LLMessage objects

        Returns:
            List of OpenAI ChatCompletionMessageParam objects
        """
        openai_messages: list[ChatCompletionMessageParam] = []
        for msg in message_list:
            content = msg.content
            if msg.role == "user":
                message = ChatCompletionUserMessageParam(
                    role=msg.role,
                    content=(
                        content.text
                        if isinstance(content, OdaMessageTextContent)
                        else None
                    ),
                )
            elif msg.role == "system":
                message = ChatCompletionSystemMessageParam(
                    role=msg.role,
                    content=(
                        content.text
                        if isinstance(content, OdaMessageTextContent)
                        else None
                    ),
                )
            elif msg.role == "assistant":
                message = ChatCompletionAssistantMessageParam(
                    role=msg.role,
                    content=(
                        content.text
                        if isinstance(content, OdaMessageTextContent)
                        else None
                    ),
                    tool_calls=(
                        self._convert_tool_calls(content.tool_calls)
                        if isinstance(content, OdaMessageToolCallsContent)
                        else None
                    ),
                )
            elif msg.role == "tool":
                message = ChatCompletionToolMessageParam(
                    role=msg.role,
                    content=(
                        content.response
                        if isinstance(content, OdaMessageToolResponseContent)
                        else None
                    ),
                    tool_call_id=(
                        content.tool_call_id
                        if isinstance(content, OdaMessageToolResponseContent)
                        else None
                    ),
                )
            else:
                continue

            openai_messages.append(message)
        return openai_messages

    def _convert_tools_to_openai(
        self, tools: dict[str, OdaTool]
    ) -> list[ChatCompletionToolParam]:
        """
        Convert tools dictionary to OpenAI tools format.

        Args:
            tools: Dictionary of tool names to OdaTool objects

        Returns:
            List of OpenAI tool definitions
        """
        if not tools:
            return []

        openai_tools = []
        for tool_name, tool_obj in tools.items():
            # Create a tool definition with proper schema and description
            openai_tools.append(
                ChatCompletionToolParam(
                    type="function",
                    function=FunctionDefinition(
                        name=tool_name,
                        description=tool_obj.description,
                        parameters=tool_obj.input_schema,
                    ),
                )
            )
        return openai_tools

    def _convert_tool_calls(
        self, tool_calls: list[OdaToolCall]
    ) -> list[ChatCompletionMessageToolCallParam]:
        pass

    async def chat_completion_stream(
        self,
        messages: List[OdaModelMessage],
        tools: Dict[str, OdaTool] = None,
        **kwargs,
    ) -> AsyncIterator[OdaModalMessageContent]:
        """
        Send a streaming chat completion request and yield OdaMessageContent objects.
        """
        # Convert LLMessage to OpenAI format
        openai_messages = self._convert_messages_to_openai(messages)

        completion_kwargs = self.config.to_completion_kwargs()
        completion_kwargs.update(kwargs)

        stream = await self.client.chat.completions.create(
            messages=openai_messages,
            stream=True,
            tools=self._convert_tools_to_openai(tools),
            **completion_kwargs,
        )

        # Accumulate tool calls from multiple chunks
        accumulated_tool_calls: dict[int, OdaToolCall] = {}

        async for chunk in stream:
            delta = chunk.choices[0].delta
            if delta.tool_calls is not None and len(delta.tool_calls) > 0:
                # Accumulate tool calls from multiple chunks
                for tool_call_chunk in delta.tool_calls:
                    index = tool_call_chunk.index
                    if index not in accumulated_tool_calls:
                        accumulated_tool_calls[index] = OdaToolCall(
                            tool_call_id=tool_call_chunk.id,
                            tool_name=tool_call_chunk.function.name,
                            tool_args=tool_call_chunk.function.arguments,
                        )
                    else:
                        # Update existing tool call with new information
                        if tool_call_chunk.id is not None:
                            accumulated_tool_calls[index].tool_call_id = (
                                tool_call_chunk.id
                            )
                        if tool_call_chunk.function.name is not None:
                            accumulated_tool_calls[index].tool_name = (
                                tool_call_chunk.function.name
                            )
                        if tool_call_chunk.function.arguments is not None:
                            accumulated_tool_calls[
                                index
                            ].tool_args += tool_call_chunk.function.arguments

            elif delta.content is not None:
                # Yield text content immediately
                content = OdaMessageTextContent(text=delta.content)
                yield content

        # Yield accumulated tool calls if any
        if len(accumulated_tool_calls) > 0:
            tool_calls = []
            # Sort by index to maintain order
            for index in sorted(accumulated_tool_calls.keys()):
                tool_calls.append(accumulated_tool_calls[index])
            content = OdaMessageToolCallsContent(tool_calls=tool_calls)
            yield content

    async def chat_completion_blocking(
        self,
        messages: List[OdaModelMessage],
        tools: Dict[str, OdaTool] = None,
        **kwargs,
    ) -> ChatCompletion:
        """
        Send a blocking chat completion request and return the raw SDK object.
        """
        # Convert LLMessage to OpenAI format
        openai_messages = self._convert_messages_to_openai(messages)

        # Convert tools to OpenAI format if provided
        openai_tools = self._convert_tools_to_openai(tools)

        completion_kwargs = self.config.to_completion_kwargs()
        completion_kwargs.update(kwargs)

        # Add tools to completion kwargs if available
        if openai_tools:
            completion_kwargs["tools"] = openai_tools

        return await self.client.chat.completions.create(
            messages=openai_messages, stream=False, **completion_kwargs
        )

    async def close(self):
        """Close the underlying OpenAI client."""
        await self.client.close()

    async def is_closed(self) -> bool:
        return self.client.is_closed()
