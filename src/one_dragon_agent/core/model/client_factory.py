from .base import ModelClient
from .config import ModelConfig
from .openai_client import OpenAIClient


class ModelClientFactory:
    """Factory class for creating LLM clients."""

    def __init__(self):
        self._client_cache = {}  # 缓存LLM客户端实例

    def create_model_client(self, config: ModelConfig) -> ModelClient:
        """
        Creates a concrete LLMClient instance based on the config.
        Currently, it only supports OpenAI-compatible clients.
        Uses caching to avoid repeated initialization overhead.

        Args:
            config: The configuration for the LLM client.

        Returns:
            An instance of OpenAIClient.
        """
        # 创建缓存键，基于配置的主要参数
        cache_key = (
            config.model,
            config.base_url,
            config.api_key[:10],
        )  # 使用API key前10位作为标识

        if cache_key not in self._client_cache:
            self._client_cache[cache_key] = self._create_new_client(config)
        else:
            client = self._client_cache[cache_key]
            if client.is_closed():
                self._client_cache[cache_key] = self._create_new_client(config)

        return self._client_cache[cache_key]

    def _create_new_client(self, config: ModelConfig) -> ModelClient:
        """创建新的LLM客户端实例"""
        # In the future, this could have logic to return different clients
        # based on config properties (e.g., llm_type).
        return OpenAIClient(config=config)

    async def close_all(self):
        """关闭所有缓存的客户端"""
        for client in self._client_cache.values():
            await client.close()
        self._client_cache.clear()
