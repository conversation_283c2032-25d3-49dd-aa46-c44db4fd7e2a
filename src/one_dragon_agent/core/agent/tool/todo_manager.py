"""Todo manager for OneDragonAgent, implementing Claude <PERSON>'s todo functionality."""

from typing import List, Dict, Any
import asyncio
from dataclasses import dataclass, asdict
from enum import Enum


class TaskStatus(Enum):
    """Task status enumeration."""

    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"


class TaskPriority(Enum):
    """Task priority enumeration."""

    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class TodoItem:
    """Represents a single todo item."""

    content: str
    status: TaskStatus
    priority: TaskPriority
    id: str

    def __post_init__(self):
        """Validate the todo item after initialization."""
        if not self.content or not self.content.strip():
            raise ValueError("Todo content cannot be empty")

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "content": self.content,
            "status": self.status.value,
            "priority": self.priority.value,
            "id": self.id,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "TodoItem":
        """Create TodoItem from dictionary."""
        return cls(
            content=data["content"],
            status=TaskStatus(data["status"]),
            priority=TaskPriority(data["priority"]),
            id=data["id"],
        )


class TodoManager:
    """Manages todo lists for OneDragonAgent sessions."""

    _instance = None
    _lock = asyncio.Lock()

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(TodoManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return
        self._initialized = True
        self._todos: Dict[str, List[TodoItem]] = {}  # session_id -> todos

    async def get_todos(self, session_id: str) -> List[TodoItem]:
        """Get todos for a session."""
        async with self._lock:
            return self._todos.get(session_id, []).copy()

    async def update_todos(self, session_id: str, todos: List[TodoItem]) -> None:
        """Update todos for a session."""
        async with self._lock:
            self._todos[session_id] = todos

    async def clear_todos(self, session_id: str) -> None:
        """Clear todos for a session."""
        async with self._lock:
            if session_id in self._todos:
                del self._todos[session_id]
