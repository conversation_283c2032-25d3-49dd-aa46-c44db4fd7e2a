"""Tool message events for OneDragon-Agent."""

from dataclasses import dataclass
from enum import Str<PERSON>num
from typing import Any

from one_dragon_agent.core.event.event import Event


class ToolEventType(StrEnum):
    """Enumeration of tool event types."""

    EXECUTION_START = "tool.execution_start"
    EXECUTION_COMPLETE = "tool.execution_complete"
    PERMISSION_REQUEST = "permission.request"
    PERMISSION_RESPONSE = "permission.response"


class PermissionRequestEvent(Event):
    """Event published when a tool requires user permission to execute."""

    def __init__(self, session_id: str, tool_name: str, permission: str):
        super().__init__(ToolEventType.PERMISSION_REQUEST)
        self.session_id = session_id
        self.tool_name = tool_name
        self.permission = permission
        # 为向后兼容设置data字段
        self.data = {
            "session_id": session_id,
            "tool_name": tool_name,
            "permission": permission,
        }


class PermissionResponseEvent(Event):
    """Event published when a user responds to a permission request."""

    def __init__(self, session_id: str, permission: str, granted: bool, scope: str):
        super().__init__(ToolEventType.PERMISSION_RESPONSE)
        self.session_id = session_id
        self.permission = permission
        self.granted = granted
        self.scope = scope
        # 为向后兼容设置data字段
        self.data = {
            "session_id": session_id,
            "permission": permission,
            "granted": granted,
            "scope": scope,
        }


class ToolExecutionStartEvent(Event):
    """
    Event published when a tool starts executing.
    This indicates the beginning of a tool call.
    """

    def __init__(self, session_id: str, tool_name: str, tool_args: Any, tool_call_id: str = None):
        """
        Initialize a ToolExecutionStartEvent.
        """
        super().__init__(ToolEventType.EXECUTION_START)
        self.session_id = session_id
        self.tool_name = tool_name
        self.tool_args = tool_args
        self.tool_call_id = tool_call_id
        # 为向后兼容设置data字段
        self.data = {
            "session_id": session_id,
            "tool_name": tool_name,
            "tool_args": tool_args,
            "tool_call_id": tool_call_id,
        }


class ToolExecutionCompleteEvent(Event):
    """
    Event published when a tool finishes executing.
    This indicates the end of a tool call and includes the result or error.
    """

    def __init__(self, session_id: str, tool_call_id: str, tool_name: str, tool_args: Any, 
                 success: bool, execution_time: float, result: Any = None, error: Any = None):
        """
        Initialize a ToolExecutionCompleteEvent.
        """
        super().__init__(ToolEventType.EXECUTION_COMPLETE)
        self.session_id = session_id
        self.tool_call_id = tool_call_id
        self.tool_name = tool_name
        self.tool_args = tool_args
        self.success = success
        self.execution_time = execution_time
        self.result = result
        self.error = error
        # 为向后兼容设置data字段
        self.data = {
            "session_id": session_id,
            "tool_call_id": tool_call_id,
            "tool_name": tool_name,
            "tool_args": tool_args,
            "success": success,
            "execution_time": execution_time,
            "result": result,
            "error": error,
        }
