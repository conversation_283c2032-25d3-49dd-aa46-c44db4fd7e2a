"""Todo events for OneDragon-Agent."""

from dataclasses import dataclass
from enum import Str<PERSON>num
from typing import Any, List

from one_dragon_agent.core.event.event import Event
from ..tool.todo_manager import TodoItem


class TodoEventType(StrEnum):
    """Enumeration of todo event types."""

    LIST_UPDATED = "todo.list_updated"
    UPDATE_FAILED = "todo.update_failed"


@dataclass
class TodoListUpdatedEvent(Event):
    """
    Event published when the todo list is successfully updated.
    This indicates a successful modification of the todo list.
    """

    session_id: str
    todos: List[TodoItem]

    def __init__(self, session_id: str, todos: List[TodoItem]):
        """
        Initialize a TodoListUpdatedEvent.

        Args:
            session_id: The ID of the session this event belongs to.
            todos: The updated list of TodoItem objects.
        """
        super().__init__(TodoEventType.LIST_UPDATED)
        self.session_id = session_id
        self.todos = todos
        # 为向后兼容设置data字段
        self.data = {
            "session_id": session_id,
            "todos": [todo.to_dict() for todo in todos],
        }


@dataclass
class TodoUpdateFailedEvent(Event):
    """
    Event published when updating the todo list fails.
    This indicates an error occurred during the update process, typically due to invalid parameters.
    """

    session_id: str
    error: Any

    def __init__(self, session_id: str, error: Any):
        """
        Initialize a TodoUpdateFailedEvent.

        Args:
            session_id: The ID of the session this event belongs to.
            error: The error information. Can be any structured data (e.g., string, dict, exception object).
        """
        super().__init__(TodoEventType.UPDATE_FAILED)
        self.session_id = session_id
        self.error = error
        # 为向后兼容设置data字段
        self.data = {
            "session_id": session_id,
            "error": error,
        }
