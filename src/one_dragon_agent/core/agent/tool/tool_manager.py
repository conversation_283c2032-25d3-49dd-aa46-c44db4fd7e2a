import json
import time
from typing import Dict, Any, AsyncGenerator, <PERSON>, Tuple, Optional

from one_dragon_agent.core.model.message import (
    OdaModelMessage,
    OdaToolCall,
    OdaModelMessageRole,
    OdaMessageToolResponseContent,
)
from one_dragon_agent.core.sys.log import get_logger
from .context import ToolExecutionContext
from .permission.permission_manager import PermissionManager
from .result import ToolResult, ToolExecutionStatus
from .file.read_file import ReadFileTool
from .todo_manager import TodoManager
from .todo_write import TodoWriteTool
from .tool import OdaTool
from .tool_event import (
    PermissionRequestEvent,
)

logger = get_logger(__name__)


class ToolManager:
    """Manages the lifecycle of tools in OneDragonAgent."""

    def __init__(self, permission_manager: PermissionManager):
        # Manage dependencies here
        self._todo_manager = TodoManager()
        self._permission_manager = permission_manager

        # A dictionary to hold all tool instances, keyed by their names.
        self._tools: Dict[str, OdaTool] = {}
        self._register_tools()

    def _register_tools(self):
        """
        Initializes and registers all available tools.
        In the future, this could be automated via discovery.
        """
        # Manually register tools and their dependencies for now.
        todo_write_tool = TodoWriteTool(self._todo_manager)
        read_file_tool = ReadFileTool()

        self._tools[todo_write_tool.name] = todo_write_tool
        self._tools[read_file_tool.name] = read_file_tool

    @property
    def tools(self) -> Dict[str, OdaTool]:
        """
        Returns a dictionary of all available tool instances.

        Returns:
            A dictionary where keys are tool names and values are tool instances.
        """
        return self._tools

    def get_tool_definitions(self) -> list[Dict[str, Any]]:
        """
        Get the definitions for all tools, suitable for an LLM prompt.

        Returns:
            A list of tool definition dictionaries.
        """
        return [
            {
                "name": tool.name,
                "description": tool.description,
                "input_schema": tool.input_schema,
            }
            for tool in self._tools.values()
        ]

    async def _check_permissions(
        self, tool: OdaTool, context: ToolExecutionContext
    ) -> Tuple[bool, Optional[str]]:
        """
        Checks if the required permissions for a tool have been granted.
        If not, it requests them from the user.

        Args:
            tool: The tool to check permissions for.
            context: The execution context.

        Returns:
            A tuple containing:
            - bool: True if all permissions are granted, False otherwise.
            - Optional[str]: A system message if permission is denied, otherwise None.
        """
        for permission in tool.required_permissions:
            if not await self._permission_manager.is_granted(
                context.session_id, permission
            ):
                # Request permission
                await context.event_dispatcher.publish(
                    PermissionRequestEvent(
                        session_id=context.session_id,
                        tool_name=tool.name,
                        permission=permission,
                    )
                )
                # Wait for response
                granted = await self._permission_manager.get_response_signal(
                    context.session_id, permission
                )
                if not granted:
                    denied_message = (
                        f"<system-message>The user has denied the '{permission}' permission "
                        f"for the '{tool.name}' tool. Do not attempt to use this tool "
                        f"for this action again unless the user explicitly asks for it.</system-message>"
                    )
                    return False, denied_message
        return True, None

    async def execute_tool_calls(
        self, tool_calls: List[OdaToolCall], context: ToolExecutionContext
    ) -> AsyncGenerator[OdaModelMessage, None]:
        """
        Execute tool calls and yield the results.

        Args:
            tool_calls: List of tool calls to execute.
            context: The execution context.
        """
        logger.info(f"Running {len(tool_calls)} tool calls")

        for tool_call in tool_calls:
            logger.info(f"Executing tool call: {tool_call.tool_name}")

            try:
                # Parse tool arguments
                try:
                    tool_args = json.loads(tool_call.tool_args)
                except json.JSONDecodeError:
                    tool_args = {}

                # Find the tool
                tool: OdaTool = self._tools.get(tool_call.tool_name)
                if not tool:
                    error_result = ToolResult(
                        status=ToolExecutionStatus.NOT_FOUND,
                        event_data=None,
                        model_message=f"Tool '{tool_call.tool_name}' not found",
                    )
                    yield self._create_error_model_message(tool_call, error_result)
                    continue

                # Check permissions
                is_authorized, denied_message = await self._check_permissions(
                    tool, context
                )
                if not is_authorized:
                    denied_result = ToolResult(
                        status=ToolExecutionStatus.PERMISSION_DENIED,
                        event_data=None,
                        model_message=denied_message,
                    )
                    yield self._create_error_model_message(tool_call, denied_result)
                    continue

                # Execute the tool
                start_event = tool.create_start_event(
                    tool_call.tool_call_id, tool_args, context
                )
                await context.event_dispatcher.publish(start_event)

                start_time = time.time()
                tool_result = await tool.call(tool_args, context)
                execution_time = time.time() - start_time

                result_event = tool.create_complete_event(
                    tool_result,
                    context,
                    tool_call.tool_call_id,
                    execution_time,
                    tool_args,
                )
                await context.event_dispatcher.publish(result_event)

                yield self._create_tool_response_model_message(tool_call, tool_result)

            except Exception as e:
                execution_time = time.time() - start_time if 'start_time' in locals() else 0.0
                logger.error(
                    f"Error executing tool '{tool_call.tool_name}': {str(e)}",
                    exc_info=True,
                )
                error_result = ToolResult(
                    status=ToolExecutionStatus.ERROR,
                    event_data=None,
                    model_message=f"Error executing tool '{tool_call.tool_name}': {str(e)}",
                )
                
                # Create and publish error complete event
                result_event = tool.create_complete_event(
                    error_result,
                    context,
                    tool_call.tool_call_id,
                    execution_time,
                    tool_args,
                )
                await context.event_dispatcher.publish(result_event)
                
                yield self._create_error_model_message(tool_call, error_result)

    

    def _create_tool_response_model_message(
        self, tool_call: OdaToolCall, tool_result: ToolResult
    ) -> OdaModelMessage:
        """
        Create a tool response model message from ToolResult.
        """
        return OdaModelMessage(
            role=OdaModelMessageRole.TOOL,
            content=OdaMessageToolResponseContent(
                tool_call_id=tool_call.tool_call_id,
                response=tool_result.model_message,
            ),
        )

    def _create_error_model_message(
        self, tool_call: OdaToolCall, tool_result: ToolResult
    ) -> OdaModelMessage:
        """
        Create an error model message from ToolResult.
        """
        return OdaModelMessage(
            role=OdaModelMessageRole.TOOL,
            content=OdaMessageToolResponseContent(
                tool_call_id=tool_call.tool_call_id,
                response=tool_result.model_message,
            ),
        )