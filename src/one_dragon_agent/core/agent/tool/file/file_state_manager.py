import asyncio
from dataclasses import dataclass
from typing import Any, Dict, Optional, Union


@dataclass
class FileState:
    """
    Represents the state of a file at a specific moment.

    Attributes:
        content (Union[str, bytes]): The content of the file.
        timestamp (float): The modification timestamp of the file.
    """

    content: Union[str, bytes]
    timestamp: float


class FileStateManager:
    """
    Manages the state of read files for each session in a thread-safe manner.

    This class implements the Singleton pattern to ensure a single instance
    manages file states across the application. It uses an asyncio.Lock to
    handle concurrent access safely.
    """

    _instance: Optional["FileStateManager"] = None
    _lock: asyncio.Lock = asyncio.Lock()

    def __new__(cls) -> "FileStateManager":
        """Ensures only one instance of FileStateManager is created."""
        if cls._instance is None:
            cls._instance = super(FileStateManager, cls).__new__(cls)
            cls._instance._session_states: Dict[str, Dict[str, FileState]] = {}
            cls._instance._session_locks: Dict[str, asyncio.Lock] = {}
        return cls._instance

    async def _get_session_lock(self, session_id: str) -> asyncio.Lock:
        """
        Retrieves or creates a lock for a specific session.

        Args:
            session_id (str): The unique identifier for the session.

        Returns:
            asyncio.Lock: The lock for the given session.
        """
        async with self._lock:
            if session_id not in self._session_locks:
                self._session_locks[session_id] = asyncio.Lock()
            return self._session_locks[session_id]

    async def update_state(
        self,
        session_id: str,
        file_path: str,
        content: Union[str, bytes],
        timestamp: float,
    ) -> None:
        """
        Updates the state of a file for a specific session.

        Args:
            session_id (str): The unique identifier for the session.
            file_path (str): The absolute path of the file.
            content (Union[str, bytes]): The content of the file.
            timestamp (float): The modification timestamp of the file.
        """
        session_lock = await self._get_session_lock(session_id)
        async with session_lock:
            if session_id not in self._session_states:
                self._session_states[session_id] = {}
            self._session_states[session_id][file_path] = FileState(
                content=content, timestamp=timestamp
            )

    async def get_state(self, session_id: str, file_path: str) -> Optional[FileState]:
        """
        Retrieves the state of a file for a specific session.

        Args:
            session_id (str): The unique identifier for the session.
            file_path (str): The absolute path of the file.

        Returns:
            Optional[FileState]: The state of the file, or None if not found.
        """
        session_lock = await self._get_session_lock(session_id)
        async with session_lock:
            return self._session_states.get(session_id, {}).get(file_path)

    async def clear_session(self, session_id: str) -> None:
        """
        Clears all file states and the lock for a specific session.

        Args:
            session_id (str): The unique identifier for the session to clear.
        """
        async with self._lock:
            if session_id in self._session_states:
                del self._session_states[session_id]
            if session_id in self._session_locks:
                del self._session_locks[session_id]
