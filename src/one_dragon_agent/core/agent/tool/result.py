"""Tool execution result data structures for OneDragon-Agent."""

from dataclasses import dataclass
from enum import Str<PERSON><PERSON>
from typing import Any, Optional


class ToolExecutionStatus(StrEnum):
    """Enumeration of possible tool execution statuses."""

    SUCCESS = "success"
    ERROR = "error"
    NOT_FOUND = "not_found"
    PERMISSION_DENIED = "permission_denied"


@dataclass
class ToolResult:
    """Tool execution result data structure.

    This class encapsulates all information about a tool execution,
    including the execution status, timing information, raw result data,
    and the formatted message for the LLM.
    """

    status: ToolExecutionStatus  # Execution status of the tool
    event_data: Any  # Raw result data from the tool
    model_message: str  # Formatted message for the LLM
    error_message: Optional[str] = None  # Error message if execution failed

    @property
    def is_success(self) -> bool:
        """Check if the tool execution was successful."""
        return self.status == ToolExecutionStatus.SUCCESS

    @property
    def is_error(self) -> bool:
        """Check if the tool execution resulted in an error."""
        return self.status == ToolExecutionStatus.ERROR

    @property
    def is_not_found(self) -> bool:
        """Check if the tool was not found."""
        return self.status == ToolExecutionStatus.NOT_FOUND

    @property
    def is_permission_denied(self) -> bool:
        """Check if the tool execution was denied due to permissions."""
        return self.status == ToolExecutionStatus.PERMISSION_DENIED
