"""Tool execution context for OneDragonAgent."""

from dataclasses import dataclass
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from one_dragon_agent.core.event.dispatcher import EventDispatcher
    from one_dragon_agent.core.agent.tool.permission_manager import PermissionManager


@dataclass
class ToolExecutionContext:
    """Context passed to tools during execution."""

    session_id: str
    event_dispatcher: "EventDispatcher"
    permission_manager: "PermissionManager"
    # 可以根据需要添加更多上下文信息
    # agent_instance: Optional[Any] = None
    # message_manager: Optional[Any] = None
