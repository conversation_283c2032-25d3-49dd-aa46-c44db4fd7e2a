import asyncio
from collections import defaultdict
from typing import Dict, Set

from one_dragon_agent.core.sys.log import get_logger

logger = get_logger(__name__)


class PermissionManager:
    """
    A session-aware manager for tracking and handling user-granted permissions for tools.
    This class is designed to be thread-safe and manage permissions on a per-session basis.
    """

    def __init__(self):
        self._granted_permissions: Dict[str, Set[str]] = defaultdict(set)
        self._permission_events: Dict[str, asyncio.Event] = {}
        self._permission_results: Dict[str, bool] = {}
        self._lock = asyncio.Lock()

    async def is_granted(self, session_id: str, permission: str) -> bool:
        """
        Checks if a specific permission has been granted for the given session.

        Args:
            session_id: The unique identifier for the user session.
            permission: The permission string to check (e.g., 'filesystem.write').

        Returns:
            True if the permission has been granted, False otherwise.
        """
        async with self._lock:
            return permission in self._granted_permissions.get(session_id, set())

    async def grant(self, session_id: str, permission: str):
        """
        Records a granted permission for a specific session.

        Args:
            session_id: The unique identifier for the user session.
            permission: The permission string to grant.
        """
        async with self._lock:
            self._granted_permissions[session_id].add(permission)
            logger.info(f"Permission '{permission}' granted for session '{session_id}'.")

    async def get_response_signal(self, session_id: str, permission: str) -> bool:
        """
        Waits for a user's response regarding a permission request.

        This method creates a unique key for the session and permission,
        creates an asyncio.Event, and waits for it to be set by the
        handle_permission_response method.

        Args:
            session_id: The unique identifier for the user session.
            permission: The permission string being requested.

        Returns:
            The boolean result of the user's decision.
        """
        event_key = f"{session_id}_{permission}"
        async with self._lock:
            event = self._permission_events.setdefault(event_key, asyncio.Event())

        await event.wait()  # Wait for the event to be set

        async with self._lock:
            result = self._permission_results.pop(event_key, False)
            self._permission_events.pop(event_key)  # Clean up the event
            return result

    async def set_response_signal(self, session_id: str, permission: str, granted: bool):
        """
        Sets the result for a pending permission request and signals the waiting task.

        Args:
            session_id: The unique identifier for the user session.
            permission: The permission string that was requested.
            granted: The user's decision.
        """
        event_key = f"{session_id}_{permission}"
        async with self._lock:
            self._permission_results[event_key] = granted
            event = self._permission_events.get(event_key)
            if event:
                event.set()
            else:
                logger.warning(f"No waiting task found for permission signal '{event_key}'.")

    async def close_session(self, session_id: str):
        """
        Cleans up all permissions and pending events for a given session.

        Args:
            session_id: The unique identifier for the user session to clean up.
        """
        async with self._lock:
            if session_id in self._granted_permissions:
                del self._granted_permissions[session_id]
                logger.info(f"Cleaned up permissions for session '{session_id}'.")
            
            # Clean up any lingering events and results for the session
            event_keys_to_remove = [key for key in self._permission_events if key.startswith(session_id)]
            for key in event_keys_to_remove:
                del self._permission_events[key]
                if key in self._permission_results:
                    del self._permission_results[key]
                logger.info(f"Cleaned up pending permission event '{key}'.")
