from abc import ABC, abstractmethod
from typing import Any, Dict

from one_dragon_agent.core.agent.tool.result import ToolResult
from one_dragon_agent.core.agent.tool.tool_event import (
    ToolExecutionStartEvent,
    ToolExecutionCompleteEvent,
)


class OdaTool(ABC):
    """
    Base class for OneDragonAgent tools.
    This class is designed to be compatible with Claude Code tool interface.
    """

    def __init__(self, name: str, description: str, input_schema: Dict[str, Any]):
        """
        Initialize the tool with a name and description.

        Args:
            name: The unique identifier for the tool
            description: A human-readable description of the tool
        """
        self.name = name
        self.description = description
        self.input_schema = input_schema

    @abstractmethod
    async def call(self, params: Dict[str, Any], context: Any) -> ToolResult:
        """
        Execute the tool with the given parameters and context.

        Args:
            params: The parameters for the tool execution
            context: The execution context

        Returns:
            ToolResult object containing the execution results
        """
        pass

    def create_start_event(
        self, tool_call_id: str, params: Dict[str, Any], context: Any
    ) -> ToolExecutionStartEvent:
        """
        Create a tool start execution event.

        Args:
            tool_call_id: The ID of the tool call from LLM request
            params: The parameters for the tool execution
            context: The execution context

        Returns:
            An Event object representing the start of tool execution
        """
        return ToolExecutionStartEvent(
            session_id=context.session_id,
            tool_name=self.name,
            tool_args=params,
        )

    def create_complete_event(
        self,
        tool_result: ToolResult,
        context: Any,
        tool_call_id: str,
        execution_time: float,
        params: Dict[str, Any],
    ) -> ToolExecutionCompleteEvent:
        """
        Create a tool execution result event.

        Args:
            tool_result: The ToolResult object containing execution results
            context: The execution context
            tool_call_id: The ID of the tool call from LLM request
            execution_time: Execution time in seconds
            params: The parameters for the tool execution

        Returns:
            A ToolExecutionCompleteEvent object representing the tool execution result
        """
        return ToolExecutionCompleteEvent(
            session_id=context.session_id,
            tool_call_id=tool_call_id,
            tool_name=self.name,
            tool_args=params,
            success=tool_result.is_success,
            execution_time=execution_time,
            result=tool_result.event_data if tool_result.is_success else None,
            error=tool_result.error_message if tool_result.is_error else None,
        )

    @property
    @abstractmethod
    def required_permissions(self) -> set[str]:
        """
        Declares the set of permissions required by this tool.
        e.g., {"filesystem.write", "network.request"}
        An empty set means the tool requires no special permissions.
        """
        pass

    @abstractmethod
    def is_read_only(self) -> bool:
        """
        Check if the tool is read-only (doesn't modify system state).

        Returns:
            True if the tool is read-only, False otherwise
        """
        pass

    @abstractmethod
    def is_concurrency_safe(self) -> bool:
        """
        Check if the tool is safe to execute concurrently.

        Returns:
            True if the tool is concurrency safe, False otherwise
        """
        pass

    async def __aenter__(self):
        """Async context manager entry."""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.dispose()

    async def dispose(self):
        """
        Clean up resources used by the tool.
        Override this method in subclasses if cleanup is needed.
        """
        pass
