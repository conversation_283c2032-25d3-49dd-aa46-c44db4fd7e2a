"""Agent message events for OneDragon-Agent."""

from dataclasses import dataclass
from enum import StrEnum

from ..event.event import Event


class AgentEventType(StrEnum):
    """Enumeration of agent event types."""

    TEXT_STREAM_START = "agent.ai_text_stream_start"
    TEXT_STREAM_CONTENT = "agent.ai_text_stream_content"
    TEXT_STREAM_COMPLETE = "agent.ai_text_stream_complete"
    ERROR = "agent.error"


class AgentTextStreamStartEvent(Event):
    """
    Event published when the agent starts receiving a new stream of text response from the LLM.
    This indicates the beginning of a new text generation phase.
    """

    def __init__(self, session_id: str, stream_id: str = None):
        """
        Initialize an AgentTextStreamStartEvent.

        Args:
            session_id: The ID of the session this event belongs to.
            stream_id: Optional unique identifier for this text stream.
        """
        super().__init__(AgentEventType.TEXT_STREAM_START)
        self.session_id = session_id
        self.stream_id = stream_id


class AgentTextStreamContentEvent(Event):
    """
    Event published when the agent receives a chunk of text content from the LLM stream.
    This is for real-time updates and display.
    """

    def __init__(self, session_id: str, text_chunk: str, stream_id: str = None):
        """
        Initialize an AgentTextStreamContentEvent.

        Args:
            session_id: The ID of the session this event belongs to.
            text_chunk: The text chunk received from the LLM stream.
            stream_id: Optional unique identifier for this text stream.
        """
        super().__init__(AgentEventType.TEXT_STREAM_CONTENT)
        self.session_id = session_id
        self.text_chunk = text_chunk
        self.stream_id = stream_id


class AgentTextStreamCompleteEvent(Event):
    """
    Event published when the agent finishes receiving and processing a complete text response from the LLM.
    This indicates the end of a text generation phase.
    """

    def __init__(self, session_id: str, full_text: str, stream_id: str = None):
        """
        Initialize an AgentTextStreamCompleteEvent.

        Args:
            session_id: The ID of the session this event belongs to.
            full_text: The complete text received from the LLM stream.
            stream_id: Optional unique identifier for this text stream.
        """
        super().__init__(AgentEventType.TEXT_STREAM_COMPLETE)
        self.session_id = session_id
        self.full_text = full_text
        self.stream_id = stream_id


@dataclass
class AgentErrorEvent(Event):
    """
    Event published when the agent encounters an error during execution.
    This allows other components to handle errors appropriately.
    """

    session_id: str
    error_message: str

    def __init__(self, session_id: str, error_message: str):
        """
        Initialize an AgentErrorEvent.

        Args:
            session_id: The ID of the session this event belongs to.
            error_message: The error message describing what went wrong.
        """
        super().__init__(AgentEventType.ERROR)
        self.session_id = session_id
        self.error_message = error_message
