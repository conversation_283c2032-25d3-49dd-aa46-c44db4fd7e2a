from typing import Any

from ..message_manager.manager import Message<PERSON>anager
from ..model.base import Model<PERSON><PERSON>
from ..model.client_factory import ModelClientFactory
from ..model.message import (
    OdaModelMessageRole,
    OdaModelMessageType,
    OdaMessageTextContent,
    OdaMessageToolCallsContent,
    OdaModelMessage,
    OdaToolCall,
)
from ..sys.config import OdaSessionConfig
from ..sys.log import get_logger
from ..sys.message import OdaMessage
from one_dragon_agent.core.agent.tool import ToolExecutionContext
from one_dragon_agent.core.agent.tool.tool_manager import ToolManager
from one_dragon_agent.core.agent.tool.permission.permission_manager import PermissionManager
from .agent_message import (
    AgentTextStreamStartEvent,
    AgentTextStreamContentEvent,
    AgentTextStreamCompleteEvent,
)

# Set up logger
logger = get_logger(__name__)


class Agent:
    """
    The core agent that processes a single command.
    This is based on the `nO` function from the Claude code analysis.
    """

    def __init__(
        self,
        session_config: OdaSessionConfig,
        message_manager: MessageManager,
        command: OdaMessage,
        llm_factory: ModelClientFactory,
        tool_manager: ToolManager,
        event_dispatcher: Any | None = None,
        reminder_manager: Any | None = None,
        permission_manager: PermissionManager | None = None,
    ):
        """
        Initializes the agent for a single execution turn.

        Args:
            session_config: The session configuration containing LLM config.
            message_manager: The message manager that handles message history.
            command: The user command to process.
            llm_factory: Factory to create LLM clients.
            tool_manager: The tool manager that handles tool execution.
            event_dispatcher: The event dispatcher for system events.
            reminder_manager: The reminder manager for system reminders.
            permission_manager: The permission manager for tool permissions.
        """
        self.session_config = session_config
        self.message_manager = message_manager
        self.command = command
        self.llm_factory = llm_factory
        self.tool_manager = tool_manager
        self.tools = tool_manager.tools
        self.event_dispatcher = event_dispatcher
        self.reminder_manager = reminder_manager
        self.permission_manager = permission_manager
        self.llm_exchange_history: list[dict[str, list]] = []

        # Create tool execution context
        self.tool_context = ToolExecutionContext(
            session_id=session_config.session_id,
            event_dispatcher=self.event_dispatcher,
            permission_manager=self.permission_manager,
        )

    async def execute(self) -> None:
        """
        Executes the main agent loop (mainAgentLoop).
        This method creates the LLM client and streams response messages as they are generated.
        All responses are published as events through the EventDispatcher for real-time updates.
        """

        llm_client: ModelClient | None = None

        try:
            # Convert OdaMessage to OdaModelMessage and add to message history
            model_message = OdaModelMessage(
                role=OdaModelMessageRole.USER, content=self.command.content
            )
            self.message_manager.append_message(model_message)

            # Create LLM client using the provided factory (现在使用缓存)
            llm_client = self.llm_factory.create_model_client(
                self.session_config.common_llm_config
            )

            while True:
                response_list = await self._call_llm(llm_client)
                logger.info(f"Got {len(response_list)} responses from _call_llm")

                tool_calls: list[OdaToolCall] = []
                for response in response_list:
                    logger.info(
                        f"Processing response with content type: {type(response.content)}"
                    )
                    self.message_manager.append_message(response)
                    if isinstance(response.content, OdaMessageTextContent):
                        logger.info("Found text content")
                        pass
                    elif isinstance(response.content, OdaMessageToolCallsContent):
                        logger.info(
                            f"Found tool calls content with {len(response.content.tool_calls)} calls"
                        )
                        tool_calls.extend(response.content.tool_calls)

                logger.info(f"Total tool calls to execute: {len(tool_calls)}")
                if len(tool_calls) > 0:
                    # Execute tool calls and add results to message history
                    logger.info("Calling ToolManager to execute tool calls")
                    async for tool_response in self.tool_manager.execute_tool_calls(
                        tool_calls, self.tool_context
                    ):
                        self.message_manager.append_message(tool_response)
                else:
                    # No more tool calls, we're done
                    logger.info("No more tool calls, breaking loop")
                    break

        except Exception as e:
            # Log the error
            logger.error(f"Error in agent execution: {str(e)}", exc_info=True)

            # Publish an error event for real-time updates
            if self.event_dispatcher:
                from one_dragon_agent.core.agent.agent_message import (
                    AgentErrorEvent,
                )

                await self.event_dispatcher.publish(
                    AgentErrorEvent(
                        self.session_config.session_id,
                        f"Error occurred during agent execution: {str(e)}",
                    )
                )

    async def _call_llm(self, llm_client: ModelClient) -> list[OdaModelMessage]:
        response_list: list[OdaModelMessage] = []
        current_type: OdaModelMessageType | None = None
        text_parts: list[str] = []
        tool_calls: list[OdaToolCall] = []

        # Prepare messages for the LLM (now using get_message_use)
        messages = self.message_manager.get_message_use()

        # Add system reminder messages if ReminderManager is available
        if self.reminder_manager:
            # Get system reminder messages
            reminder_messages = (
                await self.reminder_manager.get_system_reminder_messages()
            )

            # Add reminder messages to the beginning of the message list
            messages = reminder_messages + messages

        # Stream the response and publish events for real-time updates
        stream = llm_client.chat_completion_stream(messages=messages, tools=self.tools)

        # Flag to track if we have started streaming text
        text_stream_started = False
        async for content in stream:
            logger.info(f"Received content: {type(content)}")
            if isinstance(content, OdaMessageToolCallsContent):
                logger.info(
                    f"Processing tool calls content with {len(content.tool_calls)} calls"
                )
                if (
                    current_type is not None
                    and current_type != OdaModelMessageType.TOOL_CALLS
                ):
                    # If we were processing text and now have tool calls, finalize the text message
                    if current_type == OdaModelMessageType.TEXT:
                        full_text = "".join(text_parts)
                        if self.event_dispatcher:
                            await self.event_dispatcher.publish(
                                AgentTextStreamCompleteEvent(
                                    self.session_config.session_id, full_text
                                )
                            )
                    response_list.append(
                        self._merge_chunk_to_tool_calls_message(tool_calls)
                    )

                current_type = OdaModelMessageType.TOOL_CALLS
                tool_calls.extend(content.tool_calls)
            elif isinstance(content, OdaMessageTextContent) and content.text:
                logger.info(f"Processing text content: {content.text}")
                if (
                    current_type is not None
                    and current_type != OdaModelMessageType.TEXT
                ):
                    # If we were processing tool calls and now have text, finalize the tool calls message
                    if current_type == OdaModelMessageType.TOOL_CALLS:
                        response_list.append(
                            self._merge_chunk_to_tool_calls_message(tool_calls)
                        )

                # If this is the start of a new text stream
                if not text_stream_started:
                    text_stream_started = True
                    if self.event_dispatcher:
                        await self.event_dispatcher.publish(
                            AgentTextStreamStartEvent(self.session_config.session_id)
                        )

                current_type = OdaModelMessageType.TEXT
                text_parts.append(content.text)

                # Publish the text chunk event for real-time updates
                if self.event_dispatcher:
                    await self.event_dispatcher.publish(
                        AgentTextStreamContentEvent(
                            self.session_config.session_id, content.text
                        )
                    )

                # Create a message for real-time streaming to the CLI (old way, can be removed later)
                # stream_message = OdaMessage(
                #     source=OdaMessageSource.AI_RESPONSE,
                #     content=OdaMessageTextContent(text=content.text),
                # )

        # Handle any remaining content at the end of the stream
        logger.info(f"Finishing _call_llm with current_type: {current_type}")
        if current_type == OdaModelMessageType.TOOL_CALLS:
            response_list.append(self._merge_chunk_to_tool_calls_message(tool_calls))
        elif current_type == OdaModelMessageType.TEXT:
            # Finalize the text stream
            full_text = "".join(text_parts)
            if self.event_dispatcher:
                await self.event_dispatcher.publish(
                    AgentTextStreamCompleteEvent(
                        self.session_config.session_id, full_text
                    )
                )
            response_list.append(self._merge_chunk_to_text_message(text_parts))

        # Log messages in debug mode
        self._log_messages_for_debug(messages, response_list)

        logger.info(f"Returning {len(response_list)} responses")
        return response_list

    def _merge_chunk_to_text_message(self, text_parts: list[str]) -> OdaModelMessage:
        response = OdaModelMessage(
            role=OdaModelMessageRole.ASSISTANT,
            content=OdaMessageTextContent(text="".join(text_parts)),
        )
        text_parts.clear()

        return response

    def _merge_chunk_to_tool_calls_message(
        self,
        tool_calls: list[OdaToolCall],
    ) -> OdaModelMessage:
        response = OdaModelMessage(
            role=OdaModelMessageRole.ASSISTANT,
            content=OdaMessageToolCallsContent(
                tool_calls=tool_calls.copy()
            ),  # Create a copy
        )
        tool_calls.clear()

        return response

    def _log_messages_for_debug(
        self, inputs: list[OdaModelMessage], outputs: list[OdaModelMessage]
    ) -> None:
        """
        Log LLM input and output messages for debugging purposes by saving them to a structured JSON file.
        The entire history of exchanges for the session is saved on each call.

        Args:
            inputs: List of messages sent to the LLM.
            outputs: List of messages received from the LLM.
        """
        if not self.session_config.debug:
            return

        import json
        from pathlib import Path

        # Create a record for the current exchange
        current_exchange = {
            "inputs": [m.to_dict() for m in inputs],
            "outputs": [m.to_dict() for m in outputs],
        }

        # Append the current exchange to the in-memory history
        self.llm_exchange_history.append(current_exchange)

        # Serialize the entire history
        log_entry = json.dumps(self.llm_exchange_history, indent=2, ensure_ascii=False)

        # Define the log path and ensure it exists
        log_path = Path(self.session_config.log_dir) / "messages"
        log_path.mkdir(parents=True, exist_ok=True)

        # Define the file path for the session's history and write the log
        file_path = log_path / f"{self.session_config.session_id}.json"
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(log_entry)
            logger.info(f"Saved LLM exchange history to {file_path}")
        except IOError as e:
            logger.error(f"Failed to write LLM exchange history to {file_path}: {e}")
