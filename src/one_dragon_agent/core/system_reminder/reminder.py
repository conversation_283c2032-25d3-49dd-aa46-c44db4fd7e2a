"""System reminder manager for OneDragon-Agent."""

import asyncio
from typing import List

from ..event.event import Event
from ..model.message import (
    OdaModelMessage,
)


class ReminderManager:
    """Manages system reminders for the OneDragon-Agent."""

    def __init__(self, session_id: str):
        """
        Initialize the reminder manager.

        Args:
            session_id: The session ID for this reminder manager
        """
        self.session_id = session_id
        self._reminders: List[OdaModelMessage] = []
        self._lock = asyncio.Lock()

    async def get_system_reminder_messages(self) -> List[OdaModelMessage]:
        """
        Get system reminder messages to be added to the message list.

        Args:

        Returns:
            A list of system reminder messages
        """
        # First get pending reminders (this acquires and releases the lock)
        pending_reminders = await self.get_pending_reminders()

        return pending_reminders

    async def handle_event(self, event: Event) -> List[OdaModelMessage]:
        """
        Handle an event and generate appropriate reminder messages.

        Args:
            event: The event to handle

        Returns:
            A list of reminder messages generated from the event
        """
        async with self._lock:
            reminder_messages = []

            # Store reminders for next LLM call
            self._reminders.extend(reminder_messages)

            return reminder_messages

    async def get_pending_reminders(self) -> List[OdaModelMessage]:
        """
        Get pending reminders and clear them.

        Returns:
            A list of pending reminder messages
        """
        async with self._lock:
            reminders = self._reminders.copy()
            self._reminders.clear()
            return reminders

    async def close(self) -> None:
        """Clean up resources used by the reminder manager."""
        async with self._lock:
            self._reminders.clear()


_TODO_EMPTY_PROMPT = """
<system-reminder>
This is a reminder that your todo list is currently empty.
DO NOT mention this to the user explicitly because they are already aware.
If you are working on tasks that would benefit from a todo list please use the TodoWrite tool to create one.
If not, please feel free to ignore. Again do not mention this message to the user.
</system-reminder>
"""
