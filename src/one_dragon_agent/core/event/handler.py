"""Event handler base classes for OneDragon-Agent."""

from abc import ABC, abstractmethod
from .event import Event


class EventHandler(ABC):
    """Base class for event handlers in the OneDragon-Agent system."""

    @abstractmethod
    async def handle(self, event: Event) -> None:
        """
        Handle an event.

        Args:
            event: The event to handle
        """
        pass

    @property
    @abstractmethod
    def event_type(self) -> str:
        """Get the event type this handler is interested in."""
        pass
