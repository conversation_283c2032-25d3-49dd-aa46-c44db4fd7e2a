"""Event dispatcher for OneDragon-Agent."""

import asyncio
import uuid
from typing import Dict, List
from .event import Event
from .handler import EventHandler


class EventDispatcher:
    """Central event dispatcher for the OneDragon-Agent system."""

    def __init__(self):
        """Initialize the event dispatcher."""
        self._handlers: Dict[str, List[EventHandler]] = {}
        self._lock = asyncio.Lock()
        self._subscription_map: Dict[str, tuple[str, EventHandler]] = {}

    async def publish(self, event: Event) -> None:
        """
        Publish an event to all subscribed handlers.

        Args:
            event: The event to publish
        """
        await self.dispatch(event)

    def subscribe(self, event_type: str, handler: EventHandler) -> str:
        """
        Subscribe to events of a specific type.

        Args:
            event_type: The type of events to subscribe to
            handler: The event handler to register

        Returns:
            A subscription ID that can be used to unsubscribe
        """
        subscription_id = str(uuid.uuid4())

        # Since this is a sync method, we can't properly use the async lock
        # We'll use a simpler approach for now
        if event_type not in self._handlers:
            self._handlers[event_type] = []
        self._handlers[event_type].append(handler)
        self._subscription_map[subscription_id] = (event_type, handler)

        return subscription_id

    def unsubscribe(self, subscription_id: str) -> bool:
        """
        Unsubscribe from events using a subscription ID.

        Args:
            subscription_id: The subscription ID to unsubscribe

        Returns:
            True if successfully unsubscribed, False otherwise
        """
        if subscription_id not in self._subscription_map:
            return False

        event_type, handler = self._subscription_map[subscription_id]
        del self._subscription_map[subscription_id]

        if event_type in self._handlers:
            try:
                self._handlers[event_type].remove(handler)
                if not self._handlers[event_type]:
                    del self._handlers[event_type]
                return True
            except ValueError:
                return False
        return False

    async def dispatch(self, event: Event) -> None:
        """
        Dispatch an event to all subscribed handlers.

        Args:
            event: The event to dispatch
        """
        # Collect handlers for this event type
        async with self._lock:
            handlers_to_call = []

            # Exact match handlers
            if event.event_type in self._handlers:
                handlers_to_call.extend(self._handlers[event.event_type])

            # Wildcard handlers (subscribe to all events)
            if "*" in self._handlers:
                handlers_to_call.extend(self._handlers["*"])

        # Call all handlers asynchronously
        tasks = []
        for handler in handlers_to_call:
            try:
                task = asyncio.create_task(handler.handle(event))
                tasks.append(task)
            except Exception:
                # Log error but continue with other handlers
                pass

        # Wait for all handlers to complete
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)

    async def close(self) -> None:
        """Clean up resources used by the event dispatcher."""
        async with self._lock:
            self._handlers.clear()
            self._subscription_map.clear()
