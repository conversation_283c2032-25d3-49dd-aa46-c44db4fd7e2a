"""File search related events."""

from dataclasses import dataclass
from typing import Optional

from one_dragon_agent.core.event.event import Event


@dataclass
class FileSearchRequestEvent(Event):
    """Event for requesting file search."""

    event_type: str = "file.search.request"
    request_id: str = ""
    query: str = ""
    base_path: str = "."


@dataclass
class FileSearchResultsEvent(Event):
    """Event for file search results."""
    event_type: str = "file.search.results"
    request_id: str = ""
    results: Optional[list[dict]] = None
    from_cache: bool = False
    error: Optional[str] = None

    def __post_init__(self):
        if self.results is None:
            self.results = []
