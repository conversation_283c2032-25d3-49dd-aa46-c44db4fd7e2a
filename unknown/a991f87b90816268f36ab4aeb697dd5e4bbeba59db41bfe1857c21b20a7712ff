# 来源

[Claude Code逆向分析](https://github.com/shareAI-lab/analysis_claude_code)

删减了部分内容

- 逆向分析的过程
- 重建过程的项目/开发规范


# 建议阅读顺序


# 按模块索引

## CLI

1. 施工指南（Implementation Guides）这些文档提供了从零开始复刻CLI功能的具体步骤，非常适合初级程序员上手。
    - 施工步骤/阶段1_项目初始化和基础架构_施工指南.md
2. 深度分析报告（Analysis Reports）这些文档提供了对现有CLI功能逆向分析的详细报告，解释了其工作原理和设计。
    - components/about_cli_startup.md
    - components/about_slash_commands.md
    - prompt_list.md

## 消息解析器 (g2A)

1. [实时Steering机制还原代码实现.md](实时Steering机制还原代码实现.md) - 该文档在“流式消息解析器 (StreamingMessageParser)”部分提到了 g2A 是其原始的混淆类名。
2. [实时Steering机制函数调用链完整分析.md](实时Steering机制函数调用链完整分析.md) - 该文档在“解析层 - 消息解析器 (g2A)”部分详细分析了 g2A 的调用链。
3. [Claude_Code_最终验证后的完整认知更新.md](Claude_Code_最终验证后的完整认知更新.md) - 该文档在“实时Steering机制”部分确认了 g2A 类实现了 stdin 的实时解析和路由。
4. [Claude_Code_实时Steering机制完整技术文档.md](Claude_Code_实时Steering机制完整技术文档.md) - 该文档在“消息解析器 (g2A类)”部分详细分析了 g2A 的流式解析实现。

## 流式消息处理引擎 (kq5)

1. [实时Steering机制还原代码实现.md](实时Steering机制还原代码实现.md) - 在“流式处理引擎 (StreamingProcessor)”部分明确指出 kq5 是其原始的混淆函数名。该文档提供了 kq5 的一个高质量、可读性强的 TypeScript 实现版本，命名为 StreamingProcessor 类。
2. [实时Steering机制严格源码验证报告.md](实时Steering机制严格源码验证报告.md) - 在“流式消息处理机制”部分，引用了 kq5 的混淆源码片段，并验证了其作为流式消息处理引擎的存在和核心功能。
3. [实时Steering机制函数调用链完整分析.md](实时Steering机制函数调用链完整分析.md) - 在“处理层 - 流式处理引擎 (kq5)”部分，详细分析了 kq5 函数在整个调用链中的位置和作用，展示了它如何接收上游消息、管理命令队列并调用下游的 Agent 循环。
4. [Claude_Code_实时Steering机制完整技术文档.md](Claude_Code_实时Steering机制完整技术文档.md) - 相关内容: 在“流式消息处理引擎 (kq5函数)”部分，对 kq5 的核心调度逻辑、并发控制特性以及其在整个消息流处理管道中的作用进行了深入的技术阐述。

# 目录结构

  - ana_docs/
    ```
    该文件夹包含了对 Claude Code AI 编程助手 的完整逆向工程分析文档，基于其混淆源码进行深度技术解构，涵盖其核心架构、运行机制、工具系统、UI 交互、安全策略等各个方面。
    ```
  
  - code-prompts/
    ```
    该文件夹包含了一些 Claude Code 的核心提示词。
    ```

  - code-tools/
    ```
    该文件夹包含了 Claude Code 的核心工具的详细文档。
    ```
  
  - components/
    ```
    该文件夹列出了Claude Code系统分析的所有组件文件及其内容概要。
    ```
    
  - /other/
    ```
    未知
    ```
  
  - /施工步骤/
    - 阶段1_项目初始化和基础架构_施工指南.md
    - 阶段2_Agent核心引擎和工具系统_施工指南.md
    - 阶段3_高级特性和交互模式_施工指南.md
    - 阶段4_MCP集成和扩展系统_施工指南.md
    - 阶段5_测试优化和发布准备_施工指南.md
  - Claude_Code_Agent系统深度技术分析报告 v2.0.md
  - Claude_Code_Agent系统完整工作全貌：基于混淆代码逆向的精准分析.md
  - Claude_Code_IDE_Connection_and_Interaction_Deep_Analysis.md
  - Claude_Code_Image_Processing_and_LLM_API_Deep_Analysis.md
  - Claude_Code_MCP_Deep_Analysis.md
  - Claude_Code_Plan_Mode_Deep_Analysis.md
  - Claude_Code_Sandbox_Deobfuscated_Implementation.md
  - Claude_Code_Sandbox_Mechanism_Deep_Analysis.md
  - Claude_Code_UI_Component_System_Deep_Analysis.md
  - Claude_Code_分层多Agent架构完整技术文档.md
  - Claude_Code_关键机制严格验证报告.md
  - Claude_Code_最终验证后的完整认知更新.md
  - Claude_Code_混淆代码分析报告.md
  - Claude_Code_实时Steering机制完整技术文档.md
  - Claude_Code_实时Steering机制还原代码实现.md
  - Claude_Code_实时Steering机制严格源码验证报告.md
  - Edit工具强制读取机制完整技术文档.md
  - Edit工具强制读取机制严格源码验证报告.md
  - PROJECT_REBUILD_DOCUMENTS_CORRECTION_REPORT.md
  - agent-sop-flow-01.md
  - agent-sop-flow-02.md
  - agent-sop-flow-03.md
  - agent-sop-flow-result.md
  - claude_code_agent_system_comprehensive_analysis.md
  - claude_code_agent_system_v2.md
  - claude_code_comprehensive_analysis_cn.md
  - claude_code_comprehensive_technical_analysis_cn.md
  - claude_code_corrected_analysis.md
  - claude_code_deobfuscated_implementation_analysis.md
  - claude_code_deep_analysis_cn.md
  - claude_code_special_interaction_modes_analysis.md
  - claude_code_user_task_execution_flow_analysis.md
  - claudeCode_prompt_list.md
  - complete_context_dump.log
  - context.log
  - prompt_for_next_software.md
  - prompt_list.md
  - system_design_analysis_stage1.md
  - tools_complete_analysis.md
  - tools_merged.md
  