# OneDragon-Agent 模块设计文档总览

## 📋 概述

本目录包含了OneDragon-Agent核心模块的完整设计文档，基于对Claude Code的深度分析，设计了从简单对话循环升级为智能任务规划和执行系统的完整方案。

## 📚 文档结构

### 核心设计文档

- **[Agent 模块](./agent.md)**: 详细介绍了 Agent 的核心架构，包括主循环、工具结果处理和消息组装机制。
  - **[工具系统概述](./tool/README.md)**: 提供工具系统的整体概述和架构说明。
- **[CLI 模块](./cli.md)**: 介绍了命令行界面的实现，包括与 OdaSession 的集成和实时消息显示。
- **[Message 模块](./message.md)**: 定义了系统中使用的消息格式和类型。
- **[Session 模块](./session.md)**: 介绍会话管理，包括输入/输出流处理和与 Agent 的交互。
- **[System Reminder 模块](./system_reminder.md)**: 介绍系统提醒机制，实现上下文注入和状态同步功能。
- **[Event Dispatcher 模块](./event_dispatcher.md)**: 介绍事件分发中心，实现模块间解耦合通信机制。

## 🎯 设计目标

1. **模块化设计**：每个模块都有明确的职责和接口
2. **异步优先**：充分利用 Python 的异步特性
3. **可扩展性**：支持插件机制和功能扩展
4. **实时交互**：支持流式消息处理和实时显示
5. **容错性**：对异常输入进行适当处理，不中断系统运行