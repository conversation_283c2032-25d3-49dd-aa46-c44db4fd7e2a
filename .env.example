# OneDragon-Agent Environment Configuration
# Copy this file to .env and fill in your values

# =============================================================================
# LLM Configuration (Required)
# =============================================================================

# OpenAI API Configuration
ODA_LLM_COMMON_TYPE=openai
ODA_LLM_COMMON_BASE_URL=https://open.bigmodel.cn/api/paas/v4
ODA_LLM_COMMON_MODEL=glm-4.5-flash
ODA_LLM_COMMON_API_KEY=**


# Debug Mode
ODA_DEBUG=false

# OneDragon-Agent 专用日志配置
# ODA_LOG_PATH: 日志文件目录，如果为空则不输出到文件
# ODA_LOG_LEVEL: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
ODA_LOG_PATH=
ODA_LOG_LEVEL=INFO
